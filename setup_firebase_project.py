#!/usr/bin/env python3
"""
Script de configuration rapide pour créer et configurer un projet Firebase pour SABTRANS
"""

import subprocess
import json
import webbrowser
import time

def print_step(step, description):
    """Afficher une étape du processus"""
    print(f"\n🔄 ÉTAPE {step}: {description}")
    print("=" * 60)

def open_firebase_console():
    """Ouvrir Firebase Console dans le navigateur"""
    print("🌐 Ouverture de Firebase Console...")
    webbrowser.open("https://console.firebase.google.com")
    time.sleep(2)

def check_firebase_cli():
    """Vérifier que Firebase CLI est installé"""
    try:
        result = subprocess.run("firebase --version", shell=True, 
                              capture_output=True, text=True, check=True)
        print(f"✅ Firebase CLI installé: {result.stdout.strip()}")
        return True
    except subprocess.CalledProcessError:
        print("❌ Firebase CLI non installé")
        print("💡 Installez avec: npm install -g firebase-tools")
        return False

def login_firebase():
    """Se connecter à Firebase"""
    try:
        result = subprocess.run("firebase login --no-localhost", shell=True, 
                              capture_output=True, text=True, check=True)
        print("✅ Connexion Firebase réussie")
        return True
    except subprocess.CalledProcessError as e:
        print("❌ Échec de la connexion Firebase")
        print(f"Erreur: {e}")
        return False

def create_firebase_project():
    """Guide pour créer un projet Firebase"""
    print_step(1, "CRÉATION DU PROJET FIREBASE")
    
    print("📋 Instructions pour créer votre projet Firebase:")
    print("1. Allez sur Firebase Console (ouverture automatique)")
    print("2. Cliquez sur 'Ajouter un projet'")
    print("3. Nom du projet: SABTRANS")
    print("4. ID du projet: sabtrans-app (ou personnalisé)")
    print("5. Activez Google Analytics (recommandé)")
    print("6. Créez le projet")
    
    # Ouvrir Firebase Console
    open_firebase_console()
    
    input("\n⏸️  Appuyez sur Entrée après avoir créé votre projet...")
    
    return True

def configure_firebase_services():
    """Guide pour configurer les services Firebase"""
    print_step(2, "CONFIGURATION DES SERVICES FIREBASE")
    
    services = [
        ("Authentication", "Méthodes de connexion → Email/Password → Activer"),
        ("Firestore Database", "Créer une base de données → Mode test → Région europe-west1"),
        ("Storage", "Commencer → Mode test → Région europe-west1"),
        ("Functions", "Commencer → Continuer"),
        ("Hosting", "Commencer → Continuer")
    ]
    
    print("🔧 Services à configurer dans Firebase Console:")
    for i, (service, instruction) in enumerate(services, 1):
        print(f"{i}. {service}")
        print(f"   → {instruction}")
    
    print("\n💳 IMPORTANT: Activez la facturation (Plan Blaze)")
    print("   → Paramètres du projet → Utilisation et facturation")
    print("   → Modifier le forfait → Blaze (pay-as-you-go)")
    
    input("\n⏸️  Appuyez sur Entrée après avoir configuré tous les services...")
    
    return True

def setup_local_project():
    """Configurer le projet local"""
    print_step(3, "CONFIGURATION DU PROJET LOCAL")
    
    # Vérifier Firebase CLI
    if not check_firebase_cli():
        return False
    
    # Se connecter à Firebase
    print("🔐 Connexion à Firebase...")
    if not login_firebase():
        print("💡 Essayez manuellement: firebase login")
        return False
    
    # Demander l'ID du projet
    print("\n📋 Configuration du projet local:")
    project_id = input("🔧 Entrez l'ID de votre projet Firebase: ").strip()
    
    if not project_id:
        print("❌ ID de projet requis")
        return False
    
    # Configurer le projet
    try:
        result = subprocess.run(f"firebase use {project_id}", shell=True, 
                              capture_output=True, text=True, check=True)
        print(f"✅ Projet configuré: {project_id}")
    except subprocess.CalledProcessError as e:
        print(f"❌ Erreur lors de la configuration: {e}")
        print("💡 Vérifiez que l'ID du projet est correct")
        return False
    
    # Créer .firebaserc
    firebaserc_content = {
        "projects": {
            "default": project_id
        }
    }
    
    try:
        with open('.firebaserc', 'w') as f:
            json.dump(firebaserc_content, f, indent=2)
        print("✅ Fichier .firebaserc créé")
    except Exception as e:
        print(f"❌ Erreur lors de la création de .firebaserc: {e}")
        return False
    
    return True

def verify_setup():
    """Vérifier la configuration"""
    print_step(4, "VÉRIFICATION DE LA CONFIGURATION")
    
    checks = [
        ("firebase projects:list", "Liste des projets"),
        ("firebase use", "Projet actuel"),
    ]
    
    all_good = True
    for command, description in checks:
        try:
            result = subprocess.run(command, shell=True, 
                                  capture_output=True, text=True, check=True)
            print(f"✅ {description}: OK")
            if "projects:list" in command:
                print(f"   Projets: {result.stdout.strip()}")
            elif "use" in command:
                print(f"   Projet actuel: {result.stdout.strip()}")
        except subprocess.CalledProcessError as e:
            print(f"❌ {description}: ÉCHEC")
            all_good = False
    
    return all_good

def main():
    """Fonction principale"""
    print("🚀 CONFIGURATION FIREBASE POUR SABTRANS")
    print("=" * 60)
    print("Ce script va vous guider pour configurer Firebase.")
    print("Vous aurez besoin d'un compte Google et d'une carte de crédit.")
    print("=" * 60)
    
    confirm = input("\n▶️  Continuer avec la configuration? (o/N): ").strip().lower()
    if confirm not in ['o', 'oui', 'y', 'yes']:
        print("❌ Configuration annulée")
        return False
    
    # Étapes de configuration
    steps = [
        create_firebase_project,
        configure_firebase_services,
        setup_local_project,
        verify_setup
    ]
    
    for i, step in enumerate(steps, 1):
        try:
            if not step():
                print(f"\n❌ ÉCHEC À L'ÉTAPE {i}")
                return False
        except KeyboardInterrupt:
            print("\n⏹️  Configuration interrompue")
            return False
        except Exception as e:
            print(f"\n❌ ERREUR INATTENDUE À L'ÉTAPE {i}: {e}")
            return False
    
    # Succès
    print("\n" + "=" * 60)
    print("🎉 CONFIGURATION FIREBASE TERMINÉE!")
    print("=" * 60)
    print("✅ Projet Firebase créé et configuré")
    print("✅ Services Firebase activés")
    print("✅ Configuration locale prête")
    print("\n📋 Prochaines étapes:")
    print("1. Exécutez: python deploy_to_firebase.py")
    print("2. Ou suivez le guide de déploiement manuel")
    print("=" * 60)
    
    return True

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
