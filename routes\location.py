from flask import Blueprint, jsonify, request
from models.location import Country, Department, Currency
from database import db

location_bp = Blueprint('location', __name__)

@location_bp.route('/api/countries', methods=['GET'])
def get_countries():
    """Récupère la liste des pays"""
    try:
        countries = Country.query.filter_by(is_active=True).order_by(Country.name_fr).all()
        return jsonify({
            'success': True,
            'countries': [country.to_dict() for country in countries]
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'Erreur lors de la récupération des pays: {str(e)}'
        }), 500

@location_bp.route('/api/countries/<int:country_id>/departments', methods=['GET'])
def get_departments_by_country(country_id):
    """Récupère les départements/wilayas d'un pays"""
    try:
        country = Country.query.get_or_404(country_id)
        departments = Department.query.filter_by(
            country_id=country_id, 
            is_active=True
        ).order_by(Department.code).all()
        
        return jsonify({
            'success': True,
            'country': country.to_dict(),
            'departments': [dept.to_dict() for dept in departments]
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'Erreur lors de la récupération des départements: {str(e)}'
        }), 500

@location_bp.route('/api/countries/<country_code>/departments', methods=['GET'])
def get_departments_by_country_code(country_code):
    """Récupère les départements/wilayas d'un pays par son code"""
    try:
        country = Country.query.filter_by(code=country_code.upper()).first()
        if not country:
            return jsonify({
                'success': False,
                'message': 'Pays non trouvé'
            }), 404
            
        departments = Department.query.filter_by(
            country_id=country.id, 
            is_active=True
        ).order_by(Department.code).all()
        
        return jsonify({
            'success': True,
            'country': country.to_dict(),
            'departments': [dept.to_dict() for dept in departments]
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'Erreur lors de la récupération des départements: {str(e)}'
        }), 500

@location_bp.route('/api/departments/<int:department_id>', methods=['GET'])
def get_department(department_id):
    """Récupère les détails d'un département/wilaya"""
    try:
        department = Department.query.get_or_404(department_id)
        return jsonify({
            'success': True,
            'department': department.to_dict(),
            'country': department.country.to_dict()
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'Erreur lors de la récupération du département: {str(e)}'
        }), 500

@location_bp.route('/api/currencies', methods=['GET'])
def get_currencies():
    """Récupère la liste des devises"""
    try:
        currencies = Currency.query.order_by(Currency.name).all()
        return jsonify({
            'success': True,
            'currencies': [currency.to_dict() for currency in currencies]
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'Erreur lors de la récupération des devises: {str(e)}'
        }), 500

@location_bp.route('/api/countries/<int:country_id>/currency', methods=['GET'])
def get_country_currency(country_id):
    """Récupère la devise d'un pays"""
    try:
        country = Country.query.get_or_404(country_id)
        return jsonify({
            'success': True,
            'country': {
                'id': country.id,
                'name': country.name_fr,
                'code': country.code
            },
            'currency': country.currency.to_dict() if country.currency else None
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'Erreur lors de la récupération de la devise: {str(e)}'
        }), 500

@location_bp.route('/api/search/departments', methods=['GET'])
def search_departments():
    """Recherche de départements/wilayas par nom"""
    try:
        query = request.args.get('q', '').strip()
        country_id = request.args.get('country_id', type=int)
        
        if not query or len(query) < 2:
            return jsonify({
                'success': False,
                'message': 'La recherche doit contenir au moins 2 caractères'
            }), 400
        
        # Construction de la requête
        search_query = Department.query.filter(
            Department.is_active == True,
            db.or_(
                Department.name.ilike(f'%{query}%'),
                Department.name_fr.ilike(f'%{query}%'),
                Department.code.ilike(f'%{query}%')
            )
        )
        
        # Filtrer par pays si spécifié
        if country_id:
            search_query = search_query.filter(Department.country_id == country_id)
        
        departments = search_query.order_by(Department.name_fr).limit(20).all()
        
        return jsonify({
            'success': True,
            'query': query,
            'departments': [
                {
                    **dept.to_dict(),
                    'country_name': dept.country.name_fr,
                    'country_code': dept.country.code
                } for dept in departments
            ]
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'Erreur lors de la recherche: {str(e)}'
        }), 500

@location_bp.route('/api/location/validate', methods=['POST'])
def validate_location():
    """Valide une combinaison pays/département"""
    try:
        data = request.get_json()
        country_id = data.get('country_id')
        department_id = data.get('department_id')
        
        if not country_id or not department_id:
            return jsonify({
                'success': False,
                'message': 'Pays et département requis'
            }), 400
        
        # Vérifier que le département appartient bien au pays
        department = Department.query.filter_by(
            id=department_id,
            country_id=country_id,
            is_active=True
        ).first()
        
        if not department:
            return jsonify({
                'success': False,
                'message': 'Combinaison pays/département invalide'
            }), 400
        
        return jsonify({
            'success': True,
            'valid': True,
            'department': department.to_dict(),
            'country': department.country.to_dict()
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'Erreur lors de la validation: {str(e)}'
        }), 500
