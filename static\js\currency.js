/**
 * Gestionnaire de devises pour SABTRANS
 * Gère la sélection de devise et la logique par défaut
 */

class CurrencyManager {
    constructor() {
        this.currencies = [];
        this.init();
    }

    async init() {
        try {
            await this.loadCurrencies();
            this.populateCurrencySelects();
            this.setupEventListeners();
            this.initializeDefaultCurrency();
        } catch (error) {
            console.error('Erreur lors de l\'initialisation du gestionnaire de devises:', error);
        }
    }

    async loadCurrencies() {
        try {
            const response = await fetch('/api/currencies');
            const data = await response.json();
            
            if (data.success) {
                this.currencies = data.currencies;
                console.log('Devises chargées:', this.currencies.length);
            } else {
                throw new Error('Erreur lors du chargement des devises');
            }
        } catch (error) {
            console.error('Erreur lors du chargement des devises:', error);
            throw error;
        }
    }

    populateCurrencySelects() {
        const currencySelects = document.querySelectorAll('select[name="currency_id"], select#currency_id, select[name="preferred_currency_id"], select#preferred_currency_id');
        
        currencySelects.forEach(select => {
            // Clear existing options except the first one
            const firstOption = select.querySelector('option[value=""]');
            select.innerHTML = '';
            if (firstOption) {
                select.appendChild(firstOption);
            }

            // Add currency options
            this.currencies.forEach(currency => {
                const option = document.createElement('option');
                option.value = currency.id;
                option.textContent = `${currency.name_fr} (${currency.symbol})`;
                option.dataset.code = currency.code;
                option.dataset.symbol = currency.symbol;
                select.appendChild(option);
            });
        });
    }

    setupEventListeners() {
        // Handle currency selection changes
        document.addEventListener('change', (e) => {
            if (e.target.matches('select[name="currency_id"], select#currency_id')) {
                this.handleCurrencyChange(e.target);
            }
        });

        // Handle country selection changes to set default currency
        document.addEventListener('change', (e) => {
            if (e.target.matches('select[name="country_id"], select#country_id, select[name="pickup_country_id"], select#pickup_country_id')) {
                this.handleCountryChangeCurrency(e.target);
            }
        });

        // Handle price input changes
        document.addEventListener('input', (e) => {
            if (e.target.matches('input[name="price"], input#price')) {
                this.updatePricePreview();
            }
        });
    }

    handleCurrencyChange(currencySelect) {
        const selectedOption = currencySelect.selectedOptions[0];
        if (selectedOption && selectedOption.dataset.symbol) {
            this.updatePriceLabels(selectedOption.dataset.symbol);
            this.updatePricePreview();
        }
    }

    async handleCountryChangeCurrency(countrySelect) {
        const countryId = countrySelect.value;
        if (!countryId) return;

        try {
            // Get country information to determine default currency
            const response = await fetch('/api/countries');
            const data = await response.json();
            
            if (data.success) {
                const country = data.countries.find(c => c.id == countryId);
                if (country && country.currency) {
                    this.setDefaultCurrency(country.currency.id);
                }
            }
        } catch (error) {
            console.error('Erreur lors de la récupération des informations du pays:', error);
        }
    }

    setDefaultCurrency(currencyId) {
        // Set default currency for freight offers
        const currencySelect = document.querySelector('select[name="currency_id"], select#currency_id');
        if (currencySelect && !currencySelect.value) {
            currencySelect.value = currencyId;
            this.handleCurrencyChange(currencySelect);
        }

        // Set default currency for user preferences
        const preferredCurrencySelect = document.querySelector('select[name="preferred_currency_id"], select#preferred_currency_id');
        if (preferredCurrencySelect && !preferredCurrencySelect.value) {
            preferredCurrencySelect.value = currencyId;
        }
    }

    updatePriceLabels(currencySymbol) {
        // Update price input labels
        const priceLabels = document.querySelectorAll('label[for="price"]');
        priceLabels.forEach(label => {
            const originalText = label.textContent.replace(/\s*\([^)]*\)$/, '');
            label.textContent = `${originalText} (${currencySymbol})`;
        });
    }

    updatePricePreview() {
        const priceInput = document.querySelector('input[name="price"], input#price');
        const currencySelect = document.querySelector('select[name="currency_id"], select#currency_id');
        const pricePreview = document.getElementById('price_preview');
        
        if (priceInput && currencySelect && pricePreview) {
            const price = parseFloat(priceInput.value);
            const selectedOption = currencySelect.selectedOptions[0];
            
            if (price && selectedOption && selectedOption.dataset.symbol) {
                const symbol = selectedOption.dataset.symbol;
                pricePreview.innerHTML = `<i class="fas fa-calculator me-1"></i>Prix: ${price.toFixed(2)} ${symbol}`;
                pricePreview.className = 'form-text text-success';
            } else {
                pricePreview.innerHTML = `<i class="fas fa-calculator me-1"></i>Prix: -- --`;
                pricePreview.className = 'form-text text-muted';
            }
        }
    }

    initializeDefaultCurrency() {
        // Initialize default currency based on user's preferred currency or country
        const preferredCurrencySelect = document.querySelector('select[name="preferred_currency_id"], select#preferred_currency_id');
        const currencySelect = document.querySelector('select[name="currency_id"], select#currency_id');
        
        // If editing a profile, set preselected values
        if (preferredCurrencySelect && preferredCurrencySelect.dataset.preselected) {
            preferredCurrencySelect.value = preferredCurrencySelect.dataset.preselected;
        }
        
        // For freight forms, try to use user's preferred currency as default
        if (currencySelect && !currencySelect.value) {
            // Try to get user's preferred currency from a data attribute or API
            this.setUserPreferredCurrency();
        }
    }

    async setUserPreferredCurrency() {
        try {
            // This could be enhanced to fetch user's preferred currency from an API
            // For now, we'll default to EUR
            const eurCurrency = this.currencies.find(c => c.code === 'EUR');
            if (eurCurrency) {
                this.setDefaultCurrency(eurCurrency.id);
            }
        } catch (error) {
            console.error('Erreur lors de la définition de la devise préférée:', error);
        }
    }

    // Utility method to format price with currency
    formatPrice(amount, currencyCode) {
        const currency = this.currencies.find(c => c.code === currencyCode);
        if (currency) {
            return `${amount.toFixed(2)} ${currency.symbol}`;
        }
        return `${amount.toFixed(2)} €`;
    }

    // Get currency by code
    getCurrencyByCode(code) {
        return this.currencies.find(c => c.code === code);
    }

    // Get currency by ID
    getCurrencyById(id) {
        return this.currencies.find(c => c.id == id);
    }
}

// Initialize currency manager when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    window.currencyManager = new CurrencyManager();
});

// Export for use in other scripts
if (typeof module !== 'undefined' && module.exports) {
    module.exports = CurrencyManager;
}
