from datetime import datetime
from database import db

class FreightOffer(db.Model):
    __tablename__ = 'freight_offers'
    
    id = db.Column(db.Integer, primary_key=True)
    
    # Référence utilisateur
    user_id = db.Column(db.<PERSON>teger, db.<PERSON>ey('users.id'), nullable=False)
    
    # Informations de base
    title = db.Column(db.String(200), nullable=False)
    description = db.Column(db.Text)
    
    # Type d'offre
    offer_type = db.Column(db.Enum('demande', 'offre'), nullable=False)  # demande = expéditeur cherche transporteur, offre = transporteur propose service
    
    # Marchandise
    goods_type = db.Column(db.String(100), nullable=False)
    goods_description = db.Column(db.Text)
    weight = db.Column(db.Float)  # en tonnes
    volume = db.Column(db.Float)  # en m3
    quantity = db.Column(db.Integer)
    packaging = db.Column(db.String(50))  # palette, carton, vrac, etc.
    
    # Lieux de collecte
    pickup_address = db.Column(db.Text, nullable=False)
    pickup_city = db.Column(db.String(100), nullable=False)
    pickup_postal_code = db.Column(db.String(10))
    pickup_country_id = db.Column(db.Integer, db.ForeignKey('countries.id'))
    pickup_department_id = db.Column(db.Integer, db.ForeignKey('departments.id'))
    pickup_country = db.Column(db.String(50), default='France')  # Maintenu pour compatibilité
    pickup_lat = db.Column(db.Float)
    pickup_lng = db.Column(db.Float)

    # Lieux de livraison
    delivery_address = db.Column(db.Text, nullable=False)
    delivery_city = db.Column(db.String(100), nullable=False)
    delivery_postal_code = db.Column(db.String(10))
    delivery_country_id = db.Column(db.Integer, db.ForeignKey('countries.id'))
    delivery_department_id = db.Column(db.Integer, db.ForeignKey('departments.id'))
    delivery_country = db.Column(db.String(50), default='France')  # Maintenu pour compatibilité
    delivery_lat = db.Column(db.Float)
    delivery_lng = db.Column(db.Float)
    
    # Dates
    pickup_date = db.Column(db.DateTime, nullable=False)
    delivery_date = db.Column(db.DateTime)
    flexible_dates = db.Column(db.Boolean, default=False)
    
    # Véhicule requis
    vehicle_type = db.Column(db.String(50))  # camion, fourgon, semi-remorque, etc.
    vehicle_length = db.Column(db.Float)  # en mètres
    special_requirements = db.Column(db.Text)  # frigo, hayon, etc.
    
    # Prix
    price = db.Column(db.Float)
    price_type = db.Column(db.Enum('fixe', 'negociable', 'au_km'), default='negociable')
    currency_id = db.Column(db.Integer, db.ForeignKey('currencies.id'))
    currency = db.Column(db.String(3), default='EUR')  # Maintenu pour compatibilité
    
    # Distance estimée
    distance_km = db.Column(db.Float)
    
    # Statut
    status = db.Column(db.Enum('active', 'assigned', 'completed', 'cancelled'), default='active')
    
    # Options
    is_urgent = db.Column(db.Boolean, default=False)
    is_private = db.Column(db.Boolean, default=False)  # pour SAB Private
    requires_insurance = db.Column(db.Boolean, default=True)
    
    # Dates système
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    expires_at = db.Column(db.DateTime)
    
    # Relations
    missions = db.relationship('Mission', backref='freight_offer', lazy='dynamic')
    pickup_country_rel = db.relationship('Country', foreign_keys=[pickup_country_id], backref='pickup_offers')
    delivery_country_rel = db.relationship('Country', foreign_keys=[delivery_country_id], backref='delivery_offers')
    pickup_department_rel = db.relationship('Department', foreign_keys=[pickup_department_id], backref='pickup_offers')
    delivery_department_rel = db.relationship('Department', foreign_keys=[delivery_department_id], backref='delivery_offers')
    offer_currency = db.relationship('Currency', foreign_keys=[currency_id], backref='freight_offers')
    
    def calculate_distance(self):
        """Calcule la distance entre les points de collecte et de livraison"""
        # Implémentation simplifiée - en production, utiliser une API de géolocalisation
        if self.pickup_lat and self.pickup_lng and self.delivery_lat and self.delivery_lng:
            import math
            
            lat1, lon1 = math.radians(self.pickup_lat), math.radians(self.pickup_lng)
            lat2, lon2 = math.radians(self.delivery_lat), math.radians(self.delivery_lng)
            
            dlat = lat2 - lat1
            dlon = lon2 - lon1
            
            a = math.sin(dlat/2)**2 + math.cos(lat1) * math.cos(lat2) * math.sin(dlon/2)**2
            c = 2 * math.asin(math.sqrt(a))
            
            # Rayon de la Terre en km
            r = 6371
            
            self.distance_km = round(c * r, 2)
        
        return self.distance_km
    
    def is_expired(self):
        """Vérifie si l'offre a expiré"""
        if self.expires_at:
            return datetime.utcnow() > self.expires_at
        return False
    
    def get_route_summary(self):
        """Retourne un résumé de l'itinéraire"""
        return f"{self.pickup_city} → {self.delivery_city}"

    def get_pickup_location(self):
        """Retourne la localisation complète de collecte"""
        parts = [self.pickup_city]
        if self.pickup_department_rel:
            parts.append(self.pickup_department_rel.get_full_name())
        if self.pickup_country_rel:
            parts.append(self.pickup_country_rel.name_fr)
        return ', '.join(parts)

    def get_delivery_location(self):
        """Retourne la localisation complète de livraison"""
        parts = [self.delivery_city]
        if self.delivery_department_rel:
            parts.append(self.delivery_department_rel.get_full_name())
        if self.delivery_country_rel:
            parts.append(self.delivery_country_rel.name_fr)
        return ', '.join(parts)

    def get_currency_info(self):
        """Retourne les informations de devise de l'offre"""
        # Priorité à la devise spécifiée pour l'offre
        if self.offer_currency:
            return self.offer_currency
        # Sinon, devise du pays de collecte
        if self.pickup_country_rel and self.pickup_country_rel.currency:
            return self.pickup_country_rel.currency
        return None

    def get_currency_symbol(self):
        """Retourne le symbole de la devise"""
        currency = self.get_currency_info()
        return currency.symbol if currency else '€'

    def get_currency_code(self):
        """Retourne le code de la devise"""
        currency = self.get_currency_info()
        return currency.code if currency else 'EUR'

    def get_formatted_price(self):
        """Retourne le prix formaté avec la devise"""
        if self.price:
            symbol = self.get_currency_symbol()
            return f"{self.price:.2f} {symbol}"
        return "Prix non défini"
    
    def to_dict(self):
        """Convertit l'objet en dictionnaire pour l'API"""
        return {
            'id': self.id,
            'title': self.title,
            'offer_type': self.offer_type,
            'goods_type': self.goods_type,
            'weight': self.weight,
            'volume': self.volume,
            'pickup_city': self.pickup_city,
            'delivery_city': self.delivery_city,
            'pickup_date': self.pickup_date.isoformat() if self.pickup_date else None,
            'price': self.price,
            'price_type': self.price_type,
            'distance_km': self.distance_km,
            'status': self.status,
            'is_urgent': self.is_urgent,
            'created_at': self.created_at.isoformat() if self.created_at else None
        }
    
    def __repr__(self):
        return f'<FreightOffer {self.title}>'


class FreightProposal(db.Model):
    """Propositions de prix des transporteurs pour les demandes de fret"""
    __tablename__ = 'freight_proposals'

    id = db.Column(db.Integer, primary_key=True)

    # Référence à l'offre de fret (demande)
    freight_offer_id = db.Column(db.Integer, db.ForeignKey('freight_offers.id'), nullable=False)

    # Transporteur qui fait la proposition
    transporter_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)

    # Prix proposé
    proposed_price = db.Column(db.Float, nullable=False)
    price_type = db.Column(db.Enum('fixe', 'negociable', 'au_km'), default='fixe')
    currency_id = db.Column(db.Integer, db.ForeignKey('currencies.id'))
    currency = db.Column(db.String(3), default='EUR')  # Maintenu pour compatibilité

    # Détails de la proposition
    message = db.Column(db.Text)  # Message du transporteur
    estimated_duration = db.Column(db.Integer)  # Durée estimée en heures
    vehicle_details = db.Column(db.Text)  # Détails du véhicule proposé

    # Statut de la proposition
    status = db.Column(db.Enum('pending', 'accepted', 'rejected', 'withdrawn'), default='pending')

    # Dates
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    responded_at = db.Column(db.DateTime)  # Date de réponse de l'expéditeur

    # Relations
    freight_offer = db.relationship('FreightOffer', backref='proposals')
    transporter = db.relationship('User', foreign_keys=[transporter_id], backref='freight_proposals')
    proposal_currency = db.relationship('Currency', foreign_keys=[currency_id], backref='freight_proposals')

    def get_price_per_km(self):
        """Calcule le prix au kilomètre"""
        if self.freight_offer and self.freight_offer.distance_km and self.freight_offer.distance_km > 0:
            return round(self.proposed_price / self.freight_offer.distance_km, 2)
        return 0

    def get_price_per_ton(self):
        """Calcule le prix à la tonne"""
        if self.freight_offer and self.freight_offer.weight and self.freight_offer.weight > 0:
            return round(self.proposed_price / self.freight_offer.weight, 2)
        return 0

    def is_best_price(self):
        """Vérifie si c'est la meilleure proposition (prix le plus bas)"""
        other_proposals = FreightProposal.query.filter(
            FreightProposal.freight_offer_id == self.freight_offer_id,
            FreightProposal.status == 'pending',
            FreightProposal.id != self.id
        ).all()

        return all(self.proposed_price <= proposal.proposed_price for proposal in other_proposals)

    def to_dict(self):
        """Convertit l'objet en dictionnaire pour l'API"""
        return {
            'id': self.id,
            'freight_offer_id': self.freight_offer_id,
            'transporter_id': self.transporter_id,
            'transporter_name': self.transporter.get_full_name() if self.transporter else '',
            'transporter_company': self.transporter.company_name if self.transporter else '',
            'proposed_price': self.proposed_price,
            'price_type': self.price_type,
            'currency': self.currency,
            'message': self.message,
            'estimated_duration': self.estimated_duration,
            'vehicle_details': self.vehicle_details,
            'status': self.status,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'price_per_km': self.get_price_per_km(),
            'price_per_ton': self.get_price_per_ton(),
            'is_best_price': self.is_best_price()
        }

    def __repr__(self):
        return f'<FreightProposal {self.proposed_price}€ for Offer {self.freight_offer_id}>'
