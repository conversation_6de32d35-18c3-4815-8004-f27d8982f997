#!/usr/bin/env python3
"""
Test du système de devises pour SABTRANS
"""

import sys
import os

# Add the current directory to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app import create_app
from database import db
from models.location import Currency
from models.user import User
from models.freight import FreightOffer
from helpers.currency_helpers import format_price, format_price_with_name, get_currency_symbol

def test_currency_models():
    """Test les modèles de devise"""
    print("🧪 Test des modèles de devise...")
    
    try:
        # Test récupération des devises
        currencies = Currency.query.all()
        print(f"✅ {len(currencies)} devises trouvées dans la base")
        
        # Test devises principales
        eur = Currency.query.filter_by(code='EUR').first()
        dzd = Currency.query.filter_by(code='DZD').first()
        
        if eur:
            print(f"✅ EUR trouvé: {eur.name_fr} ({eur.symbol})")
        else:
            print("❌ EUR non trouvé")
            return False
            
        if dzd:
            print(f"✅ DZD trouvé: {dzd.name_fr} ({dzd.symbol})")
        else:
            print("❌ DZD non trouvé")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur lors du test des modèles: {e}")
        return False

def test_user_currency_methods():
    """Test les méthodes de devise des utilisateurs"""
    print("\n🧪 Test des méthodes de devise des utilisateurs...")
    
    try:
        # Récupérer un utilisateur test
        user = User.query.first()
        if not user:
            print("❌ Aucun utilisateur trouvé pour le test")
            return False
        
        print(f"✅ Utilisateur test: {user.get_full_name()}")
        
        # Test des méthodes de devise
        currency = user.get_currency()
        if currency:
            print(f"✅ Devise de l'utilisateur: {currency.name_fr} ({currency.symbol})")
        else:
            print("⚠️  Aucune devise définie pour l'utilisateur")
        
        symbol = user.get_currency_symbol()
        print(f"✅ Symbole de devise: {symbol}")
        
        code = user.get_currency_code()
        print(f"✅ Code de devise: {code}")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur lors du test des méthodes utilisateur: {e}")
        return False

def test_freight_currency_methods():
    """Test les méthodes de devise des offres de fret"""
    print("\n🧪 Test des méthodes de devise des offres de fret...")
    
    try:
        # Récupérer une offre de fret test
        offer = FreightOffer.query.first()
        if not offer:
            print("⚠️  Aucune offre de fret trouvée pour le test")
            return True  # Pas d'erreur, juste pas de données
        
        print(f"✅ Offre test: {offer.title}")
        
        # Test des méthodes de devise
        currency = offer.get_currency_info()
        if currency:
            print(f"✅ Devise de l'offre: {currency.name_fr} ({currency.symbol})")
        else:
            print("⚠️  Aucune devise définie pour l'offre")
        
        symbol = offer.get_currency_symbol()
        print(f"✅ Symbole de devise: {symbol}")
        
        code = offer.get_currency_code()
        print(f"✅ Code de devise: {code}")
        
        if offer.price:
            formatted_price = offer.get_formatted_price()
            print(f"✅ Prix formaté: {formatted_price}")
        else:
            print("⚠️  Aucun prix défini pour l'offre")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur lors du test des méthodes d'offre: {e}")
        return False

def test_currency_helpers():
    """Test les helpers de formatage de devise"""
    print("\n🧪 Test des helpers de formatage...")
    
    try:
        # Test formatage avec EUR
        eur = Currency.query.filter_by(code='EUR').first()
        if eur:
            formatted = format_price(100.50, eur.id)
            print(f"✅ Format EUR: {formatted}")
            
            formatted_with_name = format_price_with_name(100.50, eur.id)
            print(f"✅ Format EUR avec nom: {formatted_with_name}")
        
        # Test formatage avec DZD
        dzd = Currency.query.filter_by(code='DZD').first()
        if dzd:
            formatted = format_price(14500, dzd.id)
            print(f"✅ Format DZD: {formatted}")
        
        # Test récupération de symbole
        symbol = get_currency_symbol(currency_code='EUR')
        print(f"✅ Symbole EUR: {symbol}")
        
        symbol = get_currency_symbol(currency_code='DZD')
        print(f"✅ Symbole DZD: {symbol}")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur lors du test des helpers: {e}")
        return False

def test_api_currencies():
    """Test l'API des devises"""
    print("\n🧪 Test de l'API des devises...")
    
    try:
        import requests
        
        # Test API devises
        response = requests.get('http://localhost:5000/api/currencies')
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                currencies = data.get('currencies', [])
                print(f"✅ API devises: {len(currencies)} devises disponibles")
                
                # Afficher quelques devises
                for currency in currencies[:3]:
                    print(f"   - {currency['name_fr']} ({currency['code']}) {currency['symbol']}")
                
                return True
            else:
                print("❌ API devises retourne une erreur")
                return False
        else:
            print(f"❌ API devises inaccessible: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Erreur lors du test de l'API: {e}")
        return False

def main():
    """Exécuter tous les tests"""
    app = create_app()
    
    with app.app_context():
        print("🚀 Test complet du système de devises SABTRANS...\n")
        
        tests = [
            test_currency_models,
            test_user_currency_methods,
            test_freight_currency_methods,
            test_currency_helpers,
            test_api_currencies
        ]
        
        results = []
        for test in tests:
            try:
                result = test()
                results.append(result)
            except Exception as e:
                print(f"❌ Erreur inattendue dans {test.__name__}: {e}")
                results.append(False)
        
        print("\n" + "="*60)
        if all(results):
            print("🎉 SUCCÈS: Tous les tests du système de devises sont passés!")
            print("\n✅ Fonctionnalités validées:")
            print("   - Modèles de devise")
            print("   - Méthodes utilisateur")
            print("   - Méthodes d'offre de fret")
            print("   - Helpers de formatage")
            print("   - API des devises")
            print("\n📝 Le système de devises est prêt à l'emploi!")
        else:
            print("❌ ÉCHEC: Certains tests ont échoué")
            failed_tests = [test.__name__ for test, result in zip(tests, results) if not result]
            print(f"   Tests échoués: {', '.join(failed_tests)}")
        print("="*60)
        
        return all(results)

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
