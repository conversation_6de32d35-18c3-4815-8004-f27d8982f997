rules_version = '2';
service firebase.storage {
  match /b/{bucket}/o {
    // Règles pour les documents utilisateur
    match /documents/{userId}/{allPaths=**} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
      allow read: if request.auth != null && 
        get(/databases/$(database)/documents/users/$(request.auth.uid)).data.user_type == 'admin';
    }
    
    // Règles pour les images de profil
    match /profiles/{userId}/{allPaths=**} {
      allow read: if true;
      allow write: if request.auth != null && request.auth.uid == userId;
    }
    
    // Règles pour les documents de fret
    match /freight/{offerId}/{allPaths=**} {
      allow read: if request.auth != null;
      allow write: if request.auth != null && 
        (request.auth.uid == get(/databases/$(database)/documents/freight_offers/$(offerId)).data.user_id ||
         get(/databases/$(database)/documents/users/$(request.auth.uid)).data.user_type == 'admin');
    }
    
    // Règles pour les fichiers publics
    match /public/{allPaths=**} {
      allow read: if true;
      allow write: if request.auth != null && 
        get(/databases/$(database)/documents/users/$(request.auth.uid)).data.user_type == 'admin';
    }
  }
}
