#!/usr/bin/env python3
"""
Script pour réparer l'environnement Python et préparer Firebase
Résout les problèmes de dépendances et de permissions
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

def print_step(step, description):
    """Afficher une étape du processus"""
    print(f"\n🔄 ÉTAPE {step}: {description}")
    print("=" * 60)

def run_command(command, description, check=True):
    """Exécuter une commande avec gestion d'erreur"""
    print(f"📋 {description}")
    print(f"💻 Commande: {command}")
    
    try:
        result = subprocess.run(command, shell=True, check=check, 
                              capture_output=True, text=True, 
                              encoding='utf-8', errors='replace')
        if result.stdout:
            print(f"✅ Sortie: {result.stdout.strip()}")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Erreur: {e}")
        if e.stderr:
            print(f"❌ Détails: {e.stderr.strip()}")
        return False

def check_python_installation():
    """Vérifier l'installation Python"""
    print_step(1, "VÉRIFICATION DE L'INSTALLATION PYTHON")
    
    # Vérifier Python
    try:
        result = subprocess.run("python --version", shell=True, 
                              capture_output=True, text=True, check=True)
        print(f"✅ Python: {result.stdout.strip()}")
    except:
        print("❌ Python non trouvé")
        return False
    
    # Vérifier pip
    try:
        result = subprocess.run("pip --version", shell=True, 
                              capture_output=True, text=True, check=True)
        print(f"✅ Pip: {result.stdout.strip()}")
    except:
        print("❌ Pip non trouvé")
        return False
    
    # Vérifier les distributions corrompues
    try:
        result = subprocess.run("pip list", shell=True, 
                              capture_output=True, text=True, check=False)
        if "-ip" in result.stderr or "invalid distribution" in result.stderr:
            print("⚠️  Distributions Python corrompues détectées")
            return False
    except:
        pass
    
    return True

def create_virtual_environment():
    """Créer un environnement virtuel propre"""
    print_step(2, "CRÉATION D'UN ENVIRONNEMENT VIRTUEL")
    
    venv_path = Path("venv_firebase")
    
    # Supprimer l'ancien environnement s'il existe
    if venv_path.exists():
        print("🗑️  Suppression de l'ancien environnement virtuel...")
        try:
            shutil.rmtree(venv_path)
            print("✅ Ancien environnement supprimé")
        except Exception as e:
            print(f"⚠️  Erreur lors de la suppression: {e}")
    
    # Créer le nouvel environnement
    if not run_command("python -m venv venv_firebase", "Création de l'environnement virtuel"):
        print("❌ Échec de la création de l'environnement virtuel")
        return False
    
    print("✅ Environnement virtuel créé: venv_firebase")
    return True

def activate_and_setup_venv():
    """Activer l'environnement virtuel et installer les dépendances"""
    print_step(3, "CONFIGURATION DE L'ENVIRONNEMENT VIRTUEL")
    
    # Déterminer le chemin d'activation selon l'OS
    if os.name == 'nt':  # Windows
        activate_script = "venv_firebase\\Scripts\\activate"
        pip_path = "venv_firebase\\Scripts\\pip"
        python_path = "venv_firebase\\Scripts\\python"
    else:  # Unix/Linux/Mac
        activate_script = "venv_firebase/bin/activate"
        pip_path = "venv_firebase/bin/pip"
        python_path = "venv_firebase/bin/python"
    
    print(f"📋 Chemin d'activation: {activate_script}")
    
    # Mettre à jour pip dans l'environnement virtuel
    commands = [
        (f"{python_path} -m pip install --upgrade pip", "Mise à jour de pip"),
        (f"{pip_path} install wheel setuptools", "Installation des outils de base"),
    ]
    
    for command, description in commands:
        if not run_command(command, description):
            print(f"⚠️  Échec: {description}")
    
    return True

def install_firebase_dependencies():
    """Installer les dépendances Firebase"""
    print_step(4, "INSTALLATION DES DÉPENDANCES FIREBASE")
    
    # Vérifier que le dossier functions existe
    functions_dir = Path("functions")
    if not functions_dir.exists():
        print("❌ Dossier functions manquant")
        return False
    
    # Déterminer le chemin pip
    if os.name == 'nt':  # Windows
        pip_path = "venv_firebase\\Scripts\\pip"
    else:  # Unix/Linux/Mac
        pip_path = "venv_firebase/bin/pip"
    
    # Installer les dépendances
    original_dir = os.getcwd()
    os.chdir("functions")
    
    install_commands = [
        (f"{pip_path} install -r requirements.txt", "Installation des dépendances"),
        (f"{pip_path} install -r requirements.txt --no-cache-dir", "Installation sans cache"),
        (f"{pip_path} install functions-framework flask firebase-admin", "Installation manuelle des packages principaux")
    ]
    
    success = False
    for command, description in install_commands:
        print(f"🔄 Tentative: {description}")
        if run_command(command, description, check=False):
            success = True
            break
        print("⚠️  Tentative échouée, essai suivant...")
    
    os.chdir(original_dir)
    
    if success:
        print("✅ Dépendances Firebase installées")
    else:
        print("❌ Échec de l'installation des dépendances")
    
    return success

def test_installation():
    """Tester l'installation"""
    print_step(5, "TEST DE L'INSTALLATION")
    
    # Déterminer le chemin python
    if os.name == 'nt':  # Windows
        python_path = "venv_firebase\\Scripts\\python"
    else:  # Unix/Linux/Mac
        python_path = "venv_firebase/bin/python"
    
    # Tester l'importation des modules
    test_commands = [
        (f"{python_path} -c \"import flask; print('Flask OK')\"", "Test Flask"),
        (f"{python_path} -c \"import firebase_admin; print('Firebase Admin OK')\"", "Test Firebase Admin"),
        (f"{python_path} -c \"import functions_framework; print('Functions Framework OK')\"", "Test Functions Framework")
    ]
    
    all_success = True
    for command, description in test_commands:
        if not run_command(command, description, check=False):
            all_success = False
    
    return all_success

def provide_instructions():
    """Fournir les instructions finales"""
    print_step(6, "INSTRUCTIONS FINALES")
    
    print("🎯 Environnement Python réparé et configuré !")
    print("\n📋 Pour utiliser l'environnement virtuel:")
    
    if os.name == 'nt':  # Windows
        print("   CMD: venv_firebase\\Scripts\\activate")
        print("   PowerShell: venv_firebase\\Scripts\\Activate.ps1")
    else:  # Unix/Linux/Mac
        print("   source venv_firebase/bin/activate")
    
    print("\n🚀 Prochaines étapes:")
    print("1. Activez l'environnement virtuel (commandes ci-dessus)")
    print("2. Exécutez: python deploy_to_firebase.py")
    print("3. Ou continuez avec le déploiement Firebase")
    
    print("\n💡 Si vous rencontrez encore des problèmes:")
    print("- Redémarrez votre terminal")
    print("- Exécutez en tant qu'administrateur")
    print("- Vérifiez les permissions sur le dossier Python")

def main():
    """Fonction principale"""
    print("🛠️  RÉPARATION DE L'ENVIRONNEMENT PYTHON POUR FIREBASE")
    print("=" * 60)
    print("Ce script va résoudre les problèmes Python et préparer Firebase.")
    print("=" * 60)
    
    # Vérifier l'installation Python
    if not check_python_installation():
        print("\n❌ Installation Python problématique détectée")
        print("💡 Solutions recommandées:")
        print("1. Réinstaller Python depuis python.org")
        print("2. Utiliser un environnement virtuel")
        print("3. Réparer l'installation existante")
        
        continue_anyway = input("\n▶️  Continuer quand même? (o/N): ").strip().lower()
        if continue_anyway not in ['o', 'oui', 'y', 'yes']:
            return False
    
    # Étapes de réparation
    steps = [
        create_virtual_environment,
        activate_and_setup_venv,
        install_firebase_dependencies,
        test_installation,
        provide_instructions
    ]
    
    for i, step in enumerate(steps, 1):
        try:
            if not step():
                print(f"\n❌ ÉCHEC À L'ÉTAPE {i}")
                print("Consultez les messages d'erreur ci-dessus")
                return False
        except KeyboardInterrupt:
            print("\n⏹️  Processus interrompu")
            return False
        except Exception as e:
            print(f"\n❌ ERREUR INATTENDUE À L'ÉTAPE {i}: {e}")
            return False
    
    # Succès
    print("\n" + "=" * 60)
    print("🎉 ENVIRONNEMENT PYTHON RÉPARÉ AVEC SUCCÈS!")
    print("=" * 60)
    print("✅ Environnement virtuel créé")
    print("✅ Dépendances Firebase installées")
    print("✅ Tests de fonctionnement réussis")
    print("\n🚀 Vous pouvez maintenant déployer sur Firebase!")
    print("=" * 60)
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
