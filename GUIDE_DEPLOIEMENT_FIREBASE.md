# 🚀 GUIDE DE DÉPLOIEMENT SABTRANS SUR FIREBASE

## 📋 PRÉREQUIS

### 1. Compte Firebase
- ✅ Compte Google/Firebase actif
- ✅ Projet Firebase créé
- ✅ Facturation activée (pour Cloud Functions)

### 2. Outils Nécessaires
```bash
# Installer Node.js (pour Firebase CLI)
# Télécharger depuis: https://nodejs.org/

# Installer Firebase CLI
npm install -g firebase-tools

# Installer Python 3.9+ (pour Cloud Functions)
# Télécharger depuis: https://python.org/

# Vérifier les installations
firebase --version
python --version
```

---

## 🔧 ÉTAPE 1: CONFIGURATION FIREBASE

### 1.1 Connexion à Firebase
```bash
# Se connecter à Firebase
firebase login

# Vérifier la connexion
firebase projects:list
```

### 1.2 Initialisation du Projet
```bash
# Dans le dossier SABTRANS
firebase init

# Sélectionner les services:
# ✅ Firestore
# ✅ Functions
# ✅ Hosting
# ✅ Storage

# Configuration recommandée:
# - Firestore rules: firestore.rules
# - Firestore indexes: firestore.indexes.json
# - Functions language: Python
# - Functions source: functions
# - Public directory: public
# - Single-page app: Yes
# - Automatic builds: No
```

### 1.3 Configuration du Projet Firebase
```bash
# Définir le projet par défaut
firebase use --add

# Sélectionner votre projet Firebase
# Alias recommandé: default
```

---

## 🗄️ ÉTAPE 2: MIGRATION DES DONNÉES

### 2.1 Préparer les Credentials
```bash
# Télécharger la clé de service depuis Firebase Console
# Console Firebase > Paramètres du projet > Comptes de service
# Générer une nouvelle clé privée

# Définir la variable d'environnement
export GOOGLE_APPLICATION_CREDENTIALS="path/to/serviceAccountKey.json"

# Ou sur Windows:
set GOOGLE_APPLICATION_CREDENTIALS=path\to\serviceAccountKey.json
```

### 2.2 Exécuter la Migration
```bash
# Installer les dépendances Python pour la migration
pip install firebase-admin google-cloud-firestore

# Exécuter le script de migration
python migrate_to_firestore.py
```

### 2.3 Vérifier la Migration
```bash
# Vérifier dans Firebase Console
# Firestore Database > Données
# Vérifier les collections: users, freight_offers, countries, etc.
```

---

## ⚙️ ÉTAPE 3: CONFIGURATION DES CLOUD FUNCTIONS

### 3.1 Préparer les Functions
```bash
cd functions

# Installer les dépendances Python
pip install -r requirements.txt

# Tester localement (optionnel)
functions-framework --target=api --debug
```

### 3.2 Configuration des Variables d'Environnement
```bash
# Définir les variables d'environnement pour les functions
firebase functions:config:set app.secret_key="your-super-secret-key-here"

# Vérifier la configuration
firebase functions:config:get
```

---

## 🌐 ÉTAPE 4: CONFIGURATION DU HOSTING

### 4.1 Préparer les Fichiers Statiques
```bash
# Copier les fichiers statiques dans public/
cp -r static/* public/
cp -r templates/* public/templates/

# Ou sur Windows:
xcopy static public\static /E /I
xcopy templates public\templates /E /I
```

### 4.2 Configurer Firebase dans le Frontend
```javascript
// Modifier public/index.html
// Remplacer la configuration Firebase par vos vraies clés:

const firebaseConfig = {
    apiKey: "votre-api-key",
    authDomain: "votre-projet.firebaseapp.com",
    projectId: "votre-projet-id",
    storageBucket: "votre-projet.appspot.com",
    messagingSenderId: "123456789",
    appId: "votre-app-id"
};
```

---

## 🚀 ÉTAPE 5: DÉPLOIEMENT

### 5.1 Déploiement des Règles Firestore
```bash
# Déployer les règles de sécurité
firebase deploy --only firestore:rules

# Déployer les index
firebase deploy --only firestore:indexes
```

### 5.2 Déploiement des Cloud Functions
```bash
# Déployer les functions
firebase deploy --only functions

# Vérifier le déploiement
firebase functions:log
```

### 5.3 Déploiement du Hosting
```bash
# Déployer le frontend
firebase deploy --only hosting

# Obtenir l'URL de l'application
firebase hosting:channel:list
```

### 5.4 Déploiement Complet
```bash
# Déployer tout en une fois
firebase deploy

# Vérifier le statut
firebase deploy --only hosting,functions,firestore
```

---

## 🔍 ÉTAPE 6: TESTS ET VÉRIFICATION

### 6.1 Tests de Base
```bash
# Tester l'URL de l'application
curl https://votre-projet.web.app/

# Tester les API
curl https://votre-projet.web.app/api/countries
curl https://votre-projet.web.app/api/currencies
```

### 6.2 Tests Fonctionnels
1. **Inscription/Connexion**
   - Créer un compte utilisateur
   - Se connecter/déconnecter
   - Vérifier les données dans Firestore

2. **Gestion des Offres**
   - Créer une offre de fret
   - Vérifier la localisation dynamique
   - Tester la sélection de devise

3. **Propositions**
   - Créer une proposition
   - Vérifier les notifications
   - Tester les statuts

---

## 🛠️ ÉTAPE 7: CONFIGURATION AVANCÉE

### 7.1 Domaine Personnalisé
```bash
# Ajouter un domaine personnalisé
firebase hosting:channel:deploy production --expires 30d

# Configurer le domaine dans Firebase Console
# Hosting > Domaines personnalisés
```

### 7.2 Monitoring et Logs
```bash
# Activer les logs détaillés
firebase functions:log --only api

# Configurer les alertes dans Firebase Console
# Functions > Santé > Alertes
```

### 7.3 Sauvegardes
```bash
# Exporter les données Firestore
gcloud firestore export gs://votre-bucket/backup-$(date +%Y%m%d)

# Programmer des sauvegardes automatiques
# Cloud Console > Firestore > Importer/Exporter
```

---

## 🔧 DÉPANNAGE

### Problèmes Courants

#### 1. Erreur de Permissions
```bash
# Vérifier les permissions IAM
gcloud projects get-iam-policy votre-projet-id

# Ajouter les rôles nécessaires
gcloud projects add-iam-policy-binding votre-projet-id \
    --member="user:<EMAIL>" \
    --role="roles/firebase.admin"
```

#### 2. Erreur de Quota Functions
```bash
# Vérifier les quotas
gcloud compute project-info describe --project=votre-projet-id

# Augmenter les quotas dans Cloud Console
```

#### 3. Erreur de CORS
```javascript
// Ajouter dans functions/main.py
from flask_cors import CORS
CORS(app, origins=["https://votre-projet.web.app"])
```

### Logs Utiles
```bash
# Logs des functions
firebase functions:log

# Logs spécifiques
firebase functions:log --only api

# Logs en temps réel
firebase functions:log --follow
```

---

## 📊 MONITORING ET MAINTENANCE

### 1. Surveillance
- **Firebase Console** : Métriques en temps réel
- **Cloud Monitoring** : Alertes personnalisées
- **Error Reporting** : Suivi des erreurs

### 2. Mises à Jour
```bash
# Mettre à jour les functions
firebase deploy --only functions

# Mettre à jour le frontend
firebase deploy --only hosting

# Mise à jour complète
firebase deploy
```

### 3. Sécurité
- Réviser les règles Firestore régulièrement
- Surveiller les accès non autorisés
- Mettre à jour les dépendances

---

## ✅ CHECKLIST FINALE

- [ ] Projet Firebase configuré
- [ ] Données migrées vers Firestore
- [ ] Cloud Functions déployées
- [ ] Frontend déployé sur Hosting
- [ ] Règles de sécurité configurées
- [ ] Tests fonctionnels réussis
- [ ] Monitoring activé
- [ ] Domaine personnalisé configuré (optionnel)
- [ ] Sauvegardes programmées

---

## 🎯 RÉSULTAT ATTENDU

Après ce déploiement, vous aurez :

✅ **Application SABTRANS** accessible sur `https://votre-projet.web.app`  
✅ **Backend serverless** avec Cloud Functions  
✅ **Base de données** Firestore sécurisée  
✅ **Authentification** Firebase intégrée  
✅ **Hosting** rapide et sécurisé  
✅ **Monitoring** et logs complets  

**Votre application SABTRANS sera prête pour la production sur Firebase ! 🚀**

---

## 🤖 SCRIPT D'INSTALLATION AUTOMATIQUE

Pour simplifier le déploiement, utilisez le script `deploy_to_firebase.py` :

```bash
# Exécuter le script d'installation automatique
python deploy_to_firebase.py

# Le script va:
# 1. Vérifier les prérequis
# 2. Configurer Firebase
# 3. Migrer les données
# 4. Déployer l'application
# 5. Effectuer les tests de base
```

Ce script automatise la plupart des étapes et vous guide pour les configurations manuelles nécessaires.
