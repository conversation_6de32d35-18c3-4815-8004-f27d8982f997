{% extends "base.html" %}

{% block title %}Changer le <PERSON> de Passe - SABTRANS{% endblock %}

{% block content %}
<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-6">
            <!-- Header -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 text-primary">
                    <i class="fas fa-key me-2"></i>Changer le <PERSON> de Pass<PERSON>
                </h1>
                <a href="{{ url_for('auth.profile') }}" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left me-2"></i>Retour
                </a>
            </div>

            <div class="card shadow">
                <div class="card-header bg-warning text-dark">
                    <h5 class="mb-0">
                        <i class="fas fa-shield-alt me-2"></i>Sécurité du Compte
                    </h5>
                </div>
                <div class="card-body">
                    <form method="POST" class="needs-validation" novalidate>
                        <div class="mb-3">
                            <label for="current_password" class="form-label">
                                <i class="fas fa-lock me-1"></i>Mot de passe actuel *
                            </label>
                            <input type="password" class="form-control" id="current_password" name="current_password" required>
                            <div class="invalid-feedback">Veuillez saisir votre mot de passe actuel</div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="new_password" class="form-label">
                                <i class="fas fa-key me-1"></i>Nouveau mot de passe *
                            </label>
                            <input type="password" class="form-control" id="new_password" name="new_password" required>
                            <div class="form-text">
                                Le mot de passe doit contenir au moins 8 caractères avec au moins :
                                <ul class="small mt-1">
                                    <li>Une majuscule</li>
                                    <li>Une minuscule</li>
                                    <li>Un chiffre</li>
                                </ul>
                            </div>
                            <div class="invalid-feedback">Veuillez saisir un nouveau mot de passe valide</div>
                            <!-- Indicateur de force du mot de passe -->
                            <div id="password-strength" class="form-text"></div>
                        </div>
                        
                        <div class="mb-4">
                            <label for="confirm_password" class="form-label">
                                <i class="fas fa-check me-1"></i>Confirmer le nouveau mot de passe *
                            </label>
                            <input type="password" class="form-control" id="confirm_password" name="confirm_password" required>
                            <div class="invalid-feedback">Les mots de passe ne correspondent pas</div>
                            <!-- Indicateur de correspondance -->
                            <div id="password-match-feedback" class="form-text"></div>
                        </div>
                        
                        <!-- Conseils de sécurité -->
                        <div class="alert alert-info">
                            <h6 class="alert-heading">
                                <i class="fas fa-lightbulb me-2"></i>Conseils de sécurité
                            </h6>
                            <ul class="mb-0 small">
                                <li>Utilisez un mot de passe unique pour SABTRANS</li>
                                <li>Évitez les informations personnelles (nom, date de naissance)</li>
                                <li>Mélangez lettres, chiffres et caractères spéciaux</li>
                                <li>Changez votre mot de passe régulièrement</li>
                            </ul>
                        </div>
                        
                        <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                            <a href="{{ url_for('auth.profile') }}" class="btn btn-outline-secondary me-md-2">
                                <i class="fas fa-times me-2"></i>Annuler
                            </a>
                            <button type="submit" class="btn btn-warning">
                                <i class="fas fa-save me-2"></i>Changer le mot de passe
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Informations de sécurité -->
            <div class="card mt-4">
                <div class="card-header bg-info text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-info-circle me-2"></i>Informations de Sécurité
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label text-muted">Dernière modification</label>
                            <p>{{ current_user.updated_at.strftime('%d/%m/%Y à %H:%M') }}</p>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label text-muted">Dernière connexion</label>
                            <p>
                                {% if current_user.last_login %}
                                    {{ current_user.last_login.strftime('%d/%m/%Y à %H:%M') }}
                                {% else %}
                                    <span class="text-muted">Jamais</span>
                                {% endif %}
                            </p>
                        </div>
                    </div>
                    
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <strong>Important :</strong> Après avoir changé votre mot de passe, vous serez déconnecté 
                        et devrez vous reconnecter avec votre nouveau mot de passe.
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Validation du formulaire
(function() {
    'use strict';
    window.addEventListener('load', function() {
        var forms = document.getElementsByClassName('needs-validation');
        var validation = Array.prototype.filter.call(forms, function(form) {
            form.addEventListener('submit', function(event) {
                if (form.checkValidity() === false) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                form.classList.add('was-validated');
            }, false);
        });
    }, false);
})();

// Vérification de la force du mot de passe
function checkPasswordStrength(password) {
    var strengthIndicator = document.getElementById('password-strength');
    if (!strengthIndicator) return;

    var strength = 0;
    var feedback = [];

    // Longueur
    if (password.length >= 8) {
        strength += 1;
    } else {
        feedback.push('Au moins 8 caractères');
    }

    // Majuscule
    if (/[A-Z]/.test(password)) {
        strength += 1;
    } else {
        feedback.push('Une majuscule');
    }

    // Minuscule
    if (/[a-z]/.test(password)) {
        strength += 1;
    } else {
        feedback.push('Une minuscule');
    }

    // Chiffre
    if (/\d/.test(password)) {
        strength += 1;
    } else {
        feedback.push('Un chiffre');
    }

    // Caractère spécial
    if (/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
        strength += 1;
    }

    // Mise à jour de l'indicateur
    var strengthText = '';
    var strengthClass = '';

    switch (strength) {
        case 0:
        case 1:
            strengthText = 'Très faible';
            strengthClass = 'text-danger';
            break;
        case 2:
            strengthText = 'Faible';
            strengthClass = 'text-warning';
            break;
        case 3:
            strengthText = 'Moyen';
            strengthClass = 'text-info';
            break;
        case 4:
        case 5:
            strengthText = 'Fort';
            strengthClass = 'text-success';
            break;
    }

    strengthIndicator.textContent = 'Force du mot de passe : ' + strengthText;
    strengthIndicator.className = 'form-text ' + strengthClass;

    if (feedback.length > 0) {
        strengthIndicator.textContent += ' (Manque: ' + feedback.join(', ') + ')';
    }
}

// Vérification de la correspondance des mots de passe
function validatePasswordMatch() {
    var password = document.getElementById('new_password').value;
    var confirmPassword = document.getElementById('confirm_password').value;
    var feedback = document.getElementById('password-match-feedback');
    var confirmInput = document.getElementById('confirm_password');

    if (!feedback) return;

    if (confirmPassword === '') {
        feedback.textContent = '';
        confirmInput.classList.remove('is-valid', 'is-invalid');
        return;
    }

    if (password !== confirmPassword) {
        feedback.textContent = 'Les mots de passe ne correspondent pas';
        feedback.className = 'form-text text-danger';
        confirmInput.classList.remove('is-valid');
        confirmInput.classList.add('is-invalid');
    } else {
        feedback.textContent = 'Les mots de passe correspondent';
        feedback.className = 'form-text text-success';
        confirmInput.classList.remove('is-invalid');
        confirmInput.classList.add('is-valid');
    }
}

// Événements
document.getElementById('new_password').addEventListener('input', function() {
    checkPasswordStrength(this.value);
    validatePasswordMatch(); // Revérifier la correspondance
});

document.getElementById('confirm_password').addEventListener('input', function() {
    validatePasswordMatch();
});

// Confirmation avant soumission
document.querySelector('form').addEventListener('submit', function(e) {
    if (!confirm('Êtes-vous sûr de vouloir changer votre mot de passe ? Vous serez déconnecté après cette action.')) {
        e.preventDefault();
    }
});

// Afficher/masquer le mot de passe
function togglePasswordVisibility(inputId) {
    const input = document.getElementById(inputId);
    const type = input.getAttribute('type') === 'password' ? 'text' : 'password';
    input.setAttribute('type', type);
}

// Ajouter des boutons pour afficher/masquer les mots de passe
document.addEventListener('DOMContentLoaded', function() {
    const passwordInputs = ['current_password', 'new_password', 'confirm_password'];
    
    passwordInputs.forEach(function(inputId) {
        const input = document.getElementById(inputId);
        const wrapper = input.parentNode;
        
        // Créer le bouton toggle
        const toggleBtn = document.createElement('button');
        toggleBtn.type = 'button';
        toggleBtn.className = 'btn btn-outline-secondary btn-sm mt-1';
        toggleBtn.innerHTML = '<i class="fas fa-eye"></i> Afficher';
        toggleBtn.onclick = function() {
            const type = input.getAttribute('type') === 'password' ? 'text' : 'password';
            input.setAttribute('type', type);
            toggleBtn.innerHTML = type === 'password' ? '<i class="fas fa-eye"></i> Afficher' : '<i class="fas fa-eye-slash"></i> Masquer';
        };
        
        wrapper.appendChild(toggleBtn);
    });
});
</script>
{% endblock %}
