{% extends "base.html" %}

{% block title %}Mon Profil - SABTRANS{% endblock %}

{% block content %}
<div class="container">
    <div class="row">
        <div class="col-md-8 mx-auto">
            <!-- Header -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 text-primary">
                    <i class="fas fa-user me-2"></i>Mon Profil
                </h1>
                <a href="{{ url_for('auth.edit_profile') }}" class="btn btn-outline-primary">
                    <i class="fas fa-edit me-2"></i>Modifier
                </a>
            </div>

            <!-- Informations principales -->
            <div class="card mb-4">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-id-card me-2"></i>Informations Personnelles
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label text-muted">Nom complet</label>
                            <p class="fw-bold">{{ user.get_full_name() }}</p>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label text-muted">Email</label>
                            <p class="fw-bold">
                                {{ user.email }}
                                {% if user.email_confirmed %}
                                    <span class="badge bg-success ms-2">Vérifié</span>
                                {% else %}
                                    <span class="badge bg-warning ms-2">Non vérifié</span>
                                {% endif %}
                            </p>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label text-muted">Nom d'utilisateur</label>
                            <p class="fw-bold">{{ user.username }}</p>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label text-muted">Téléphone</label>
                            <p class="fw-bold">{{ user.phone or 'Non renseigné' }}</p>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label text-muted">Type de compte</label>
                            <p>
                                <span class="badge bg-{{ 'primary' if user.user_type == 'admin' else 'secondary' }} fs-6">
                                    {{ user.user_type.title() }}
                                </span>
                            </p>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label text-muted">Statut</label>
                            <p>
                                {% if user.is_active %}
                                    <span class="badge bg-success fs-6">Actif</span>
                                {% else %}
                                    <span class="badge bg-danger fs-6">Inactif</span>
                                {% endif %}
                                {% if user.is_verified %}
                                    <span class="badge bg-info fs-6 ms-1">Vérifié</span>
                                {% endif %}
                            </p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Informations entreprise -->
            {% if user.company_name or user.address %}
            <div class="card mb-4">
                <div class="card-header bg-success text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-building me-2"></i>Informations Entreprise
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        {% if user.company_name %}
                        <div class="col-md-6 mb-3">
                            <label class="form-label text-muted">Entreprise</label>
                            <p class="fw-bold">{{ user.company_name }}</p>
                        </div>
                        {% endif %}
                        {% if user.siret %}
                        <div class="col-md-6 mb-3">
                            <label class="form-label text-muted">SIRET</label>
                            <p class="fw-bold">{{ user.siret }}</p>
                        </div>
                        {% endif %}
                        {% if user.address %}
                        <div class="col-12 mb-3">
                            <label class="form-label text-muted">Adresse</label>
                            <p class="fw-bold">
                                {{ user.address }}<br>
                                {% if user.postal_code %}{{ user.postal_code }} {% endif %}
                                {% if user.city %}{{ user.city }}{% endif %}
                                {% if user.country %}, {{ user.country }}{% endif %}
                            </p>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
            {% endif %}

            <!-- Statistiques -->
            <div class="card mb-4">
                <div class="card-header bg-info text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-chart-bar me-2"></i>Statistiques
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-md-3 mb-3">
                            <div class="stat-item">
                                <h3 class="stat-number text-primary">0</h3>
                                <p class="text-muted">Offres publiées</p>
                            </div>
                        </div>
                        <div class="col-md-3 mb-3">
                            <div class="stat-item">
                                <h3 class="stat-number text-success">0</h3>
                                <p class="text-muted">Missions réalisées</p>
                            </div>
                        </div>
                        <div class="col-md-3 mb-3">
                            <div class="stat-item">
                                <h3 class="stat-number text-warning">0</h3>
                                <p class="text-muted">Documents</p>
                            </div>
                        </div>
                        <div class="col-md-3 mb-3">
                            <div class="stat-item">
                                <h3 class="stat-number text-info">
                                    {% for i in range(5) %}
                                        <i class="far fa-star"></i>
                                    {% endfor %}
                                </h3>
                                <p class="text-muted">Évaluation</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Informations système -->
            <div class="card mb-4">
                <div class="card-header bg-secondary text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-info-circle me-2"></i>Informations Système
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label text-muted">Membre depuis</label>
                            <p class="fw-bold">{{ user.created_at.strftime('%d/%m/%Y à %H:%M') }}</p>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label text-muted">Dernière connexion</label>
                            <p class="fw-bold">
                                {% if user.last_login %}
                                    {{ user.last_login.strftime('%d/%m/%Y à %H:%M') }}
                                {% else %}
                                    Jamais
                                {% endif %}
                            </p>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label text-muted">Dernière mise à jour</label>
                            <p class="fw-bold">{{ user.updated_at.strftime('%d/%m/%Y à %H:%M') }}</p>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label text-muted">Devise préférée</label>
                            <p class="fw-bold">
                                {% if user.get_currency() %}
                                    <i class="fas fa-coins me-2 text-warning"></i>
                                    {{ user.get_currency().name }} ({{ user.get_currency_symbol() }})
                                {% else %}
                                    <span class="text-muted">Non définie</span>
                                {% endif %}
                            </p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Actions -->
            <div class="card">
                <div class="card-header bg-warning text-dark">
                    <h5 class="mb-0">
                        <i class="fas fa-cogs me-2"></i>Actions
                    </h5>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2 d-md-flex">
                        <a href="{{ url_for('auth.edit_profile') }}" class="btn btn-primary me-md-2">
                            <i class="fas fa-edit me-2"></i>Modifier le profil
                        </a>
                        <a href="{{ url_for('auth.change_password') }}" class="btn btn-outline-warning me-md-2">
                            <i class="fas fa-key me-2"></i>Changer le mot de passe
                        </a>
                        <a href="{{ url_for('dashboard.index') }}" class="btn btn-outline-primary">
                            <i class="fas fa-tachometer-alt me-2"></i>Retour au dashboard
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
