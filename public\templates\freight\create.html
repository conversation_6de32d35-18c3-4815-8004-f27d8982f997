{% extends "base.html" %}

{% block title %}Nouvelle Offre - SABTRANS{% endblock %}

{% block content %}
<div class="container">
    <div class="row">
        <div class="col-md-10 mx-auto">
            <!-- Header -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 text-primary">
                    <i class="fas fa-plus me-2"></i>Nouvelle Offre de Fret
                </h1>
                <a href="{{ url_for('freight.index') }}" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left me-2"></i>Retour
                </a>
            </div>

            <form method="POST" class="needs-validation" novalidate>
                <!-- Informations générales -->
                <div class="card mb-4">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-info-circle me-2"></i>Informations Générales
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-8 mb-3">
                                <label for="title" class="form-label">Titre de l'offre *</label>
                                <input type="text" class="form-control" id="title" name="title" required
                                       placeholder="Ex: Transport de marchandises Paris-Lyon">
                                <div class="invalid-feedback">Veuillez saisir un titre</div>
                            </div>
                            <div class="col-md-4 mb-3">
                                <label for="offer_type" class="form-label">Type d'offre *</label>
                                <select class="form-select" id="offer_type" name="offer_type" required>
                                    <option value="">Sélectionnez</option>
                                    {% if current_user.is_shipper() %}
                                        <option value="demande">Demande de transport</option>
                                    {% endif %}
                                    {% if current_user.is_transporter() %}
                                        <option value="offre">Offre de transport</option>
                                    {% endif %}
                                    {% if current_user.is_admin() %}
                                        <option value="demande">Demande de transport</option>
                                        <option value="offre">Offre de transport</option>
                                    {% endif %}
                                </select>
                                <div class="invalid-feedback">Veuillez sélectionner un type</div>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="description" class="form-label">Description</label>
                            <textarea class="form-control" id="description" name="description" rows="3"
                                      placeholder="Décrivez votre offre en détail..."></textarea>
                        </div>
                    </div>
                </div>

                <!-- Marchandise -->
                <div class="card mb-4">
                    <div class="card-header bg-success text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-box me-2"></i>Marchandise
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="goods_type" class="form-label">Type de marchandise *</label>
                                <input type="text" class="form-control" id="goods_type" name="goods_type" required
                                       placeholder="Ex: Électroménager, Alimentaire, Textile...">
                                <div class="invalid-feedback">Veuillez préciser le type de marchandise</div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="packaging" class="form-label">Conditionnement</label>
                                <select class="form-select" id="packaging" name="packaging">
                                    <option value="">Sélectionnez</option>
                                    <option value="palette">Palette</option>
                                    <option value="carton">Carton</option>
                                    <option value="vrac">Vrac</option>
                                    <option value="conteneur">Conteneur</option>
                                    <option value="autre">Autre</option>
                                </select>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-4 mb-3">
                                <label for="weight" class="form-label">Poids (tonnes)</label>
                                <input type="number" class="form-control" id="weight" name="weight" 
                                       step="0.1" min="0" placeholder="Ex: 2.5">
                            </div>
                            <div class="col-md-4 mb-3">
                                <label for="volume" class="form-label">Volume (m³)</label>
                                <input type="number" class="form-control" id="volume" name="volume" 
                                       step="0.1" min="0" placeholder="Ex: 10.5">
                            </div>
                            <div class="col-md-4 mb-3">
                                <label for="quantity" class="form-label">Quantité (unités)</label>
                                <input type="number" class="form-control" id="quantity" name="quantity" 
                                       min="1" placeholder="Ex: 50">
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="goods_description" class="form-label">Description détaillée</label>
                            <textarea class="form-control" id="goods_description" name="goods_description" rows="2"
                                      placeholder="Précisions sur la marchandise..."></textarea>
                        </div>
                    </div>
                </div>

                <!-- Itinéraire -->
                <div class="card mb-4">
                    <div class="card-header bg-warning text-dark">
                        <h5 class="mb-0">
                            <i class="fas fa-route me-2"></i>Itinéraire
                        </h5>
                    </div>
                    <div class="card-body">
                        <!-- Collecte -->
                        <h6 class="text-success mb-3">
                            <i class="fas fa-map-marker-alt me-2"></i>Lieu de collecte
                        </h6>
                        <div class="row">
                            <div class="col-md-8 mb-3">
                                <label for="pickup_address" class="form-label">Adresse *</label>
                                <input type="text" class="form-control" id="pickup_address" name="pickup_address" required
                                       placeholder="Adresse complète de collecte">
                                <div class="invalid-feedback">Veuillez saisir l'adresse de collecte</div>
                            </div>
                            <div class="col-md-4 mb-3">
                                <label for="pickup_city" class="form-label">Ville *</label>
                                <input type="text" class="form-control" id="pickup_city" name="pickup_city" required
                                       placeholder="Ville">
                                <div class="invalid-feedback">Veuillez saisir la ville</div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-4 mb-3">
                                <label for="pickup_country_id" class="form-label">Pays *</label>
                                <select class="form-select" id="pickup_country_id" name="pickup_country_id" required>
                                    <option value="">Sélectionnez le pays</option>
                                </select>
                                <div class="invalid-feedback">Veuillez sélectionner le pays</div>
                            </div>
                            <div class="col-md-4 mb-3">
                                <label for="pickup_department_id" class="form-label">
                                    <span id="pickup_department_label">Département</span> *
                                </label>
                                <select class="form-select" id="pickup_department_id" name="pickup_department_id" required disabled>
                                    <option value="">Sélectionnez d'abord un pays</option>
                                </select>
                                <div class="invalid-feedback">Veuillez sélectionner le département</div>
                            </div>
                            <div class="col-md-4 mb-3">
                                <label for="pickup_postal_code" class="form-label">Code postal</label>
                                <input type="text" class="form-control" id="pickup_postal_code" name="pickup_postal_code"
                                       placeholder="75001">
                            </div>
                        </div>

                        <hr>

                        <!-- Livraison -->
                        <h6 class="text-danger mb-3">
                            <i class="fas fa-flag-checkered me-2"></i>Lieu de livraison
                        </h6>
                        <div class="row">
                            <div class="col-md-8 mb-3">
                                <label for="delivery_address" class="form-label">Adresse *</label>
                                <input type="text" class="form-control" id="delivery_address" name="delivery_address" required
                                       placeholder="Adresse complète de livraison">
                                <div class="invalid-feedback">Veuillez saisir l'adresse de livraison</div>
                            </div>
                            <div class="col-md-4 mb-3">
                                <label for="delivery_city" class="form-label">Ville *</label>
                                <input type="text" class="form-control" id="delivery_city" name="delivery_city" required
                                       placeholder="Ville">
                                <div class="invalid-feedback">Veuillez saisir la ville</div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-4 mb-3">
                                <label for="delivery_country_id" class="form-label">Pays *</label>
                                <select class="form-select" id="delivery_country_id" name="delivery_country_id" required>
                                    <option value="">Sélectionnez le pays</option>
                                </select>
                                <div class="invalid-feedback">Veuillez sélectionner le pays</div>
                            </div>
                            <div class="col-md-4 mb-3">
                                <label for="delivery_department_id" class="form-label">
                                    <span id="delivery_department_label">Département</span> *
                                </label>
                                <select class="form-select" id="delivery_department_id" name="delivery_department_id" required disabled>
                                    <option value="">Sélectionnez d'abord un pays</option>
                                </select>
                                <div class="invalid-feedback">Veuillez sélectionner le département</div>
                            </div>
                            <div class="col-md-4 mb-3">
                                <label for="delivery_postal_code" class="form-label">Code postal</label>
                                <input type="text" class="form-control" id="delivery_postal_code" name="delivery_postal_code"
                                       placeholder="69001">
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Dates et véhicule -->
                <div class="card mb-4">
                    <div class="card-header bg-info text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-calendar me-2"></i>Planning et Véhicule
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-4 mb-3">
                                <label for="pickup_date" class="form-label">Date de collecte *</label>
                                <input type="date" class="form-control" id="pickup_date" name="pickup_date" required>
                                <div class="invalid-feedback">Veuillez sélectionner une date</div>
                            </div>
                            <div class="col-md-4 mb-3">
                                <label for="delivery_date" class="form-label">Date de livraison</label>
                                <input type="date" class="form-control" id="delivery_date" name="delivery_date">
                            </div>
                            <div class="col-md-4 mb-3 d-flex align-items-end">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="flexible_dates" name="flexible_dates">
                                    <label class="form-check-label" for="flexible_dates">
                                        Dates flexibles
                                    </label>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="vehicle_type" class="form-label">Type de véhicule</label>
                                <select class="form-select" id="vehicle_type" name="vehicle_type">
                                    <option value="">Sélectionnez</option>
                                    <option value="fourgon">Fourgon</option>
                                    <option value="camion">Camion</option>
                                    <option value="semi-remorque">Semi-remorque</option>
                                    <option value="plateau">Plateau</option>
                                    <option value="frigorifique">Frigorifique</option>
                                </select>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="vehicle_length" class="form-label">Longueur véhicule (m)</label>
                                <input type="number" class="form-control" id="vehicle_length" name="vehicle_length" 
                                       step="0.1" min="0" placeholder="Ex: 7.5">
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="special_requirements" class="form-label">Exigences particulières</label>
                            <textarea class="form-control" id="special_requirements" name="special_requirements" rows="2"
                                      placeholder="Hayon, sangles, bâches, température..."></textarea>
                        </div>
                    </div>
                </div>

                <!-- Prix et options -->
                <div class="card mb-4">
                    <div class="card-header bg-secondary text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-euro-sign me-2"></i>Prix et Options
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3 mb-3">
                                <label for="price" class="form-label">Prix</label>
                                <input type="number" class="form-control" id="price" name="price"
                                       step="0.01" min="0" placeholder="Ex: 500.00">
                            </div>
                            <div class="col-md-3 mb-3">
                                <label for="currency_id" class="form-label">Devise</label>
                                <select class="form-select" id="currency_id" name="currency_id">
                                    <option value="">Sélectionnez la devise</option>
                                </select>
                                <div class="form-text">
                                    <i class="fas fa-info-circle me-1"></i>
                                    Devise pour cette offre
                                </div>
                            </div>
                            <div class="col-md-3 mb-3">
                                <label for="price_type" class="form-label">Type de prix</label>
                                <select class="form-select" id="price_type" name="price_type">
                                    <option value="negociable">Négociable</option>
                                    <option value="fixe">Fixe</option>
                                    <option value="au_km">Au kilomètre</option>
                                </select>
                            </div>
                            <div class="col-md-3 mb-3 d-flex align-items-end">
                                <div class="form-text">
                                    <i class="fas fa-calculator me-1"></i>
                                    <span id="price_preview">Prix: -- --</span>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-4 mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="is_urgent" name="is_urgent">
                                    <label class="form-check-label" for="is_urgent">
                                        <span class="badge bg-danger">URGENT</span> Transport urgent
                                    </label>
                                </div>
                            </div>
                            <div class="col-md-4 mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="is_private" name="is_private">
                                    <label class="form-check-label" for="is_private">
                                        <span class="badge bg-warning">PRIVÉ</span> Offre privée
                                    </label>
                                </div>
                            </div>
                            <div class="col-md-4 mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="requires_insurance" name="requires_insurance" checked>
                                    <label class="form-check-label" for="requires_insurance">
                                        Assurance requise
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Boutons d'action -->
                <div class="d-grid gap-2 d-md-flex justify-content-md-end mb-4">
                    <a href="{{ url_for('freight.index') }}" class="btn btn-outline-secondary me-md-2">
                        <i class="fas fa-times me-2"></i>Annuler
                    </a>
                    <button type="submit" class="btn btn-success">
                        <i class="fas fa-save me-2"></i>Publier l'offre
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Validation du formulaire
(function() {
    'use strict';
    window.addEventListener('load', function() {
        var forms = document.getElementsByClassName('needs-validation');
        var validation = Array.prototype.filter.call(forms, function(form) {
            form.addEventListener('submit', function(event) {
                if (form.checkValidity() === false) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                form.classList.add('was-validated');
            }, false);
        });
    }, false);
})();

// Date minimum = aujourd'hui
document.addEventListener('DOMContentLoaded', function() {
    const today = new Date().toISOString().split('T')[0];
    document.getElementById('pickup_date').min = today;
    document.getElementById('delivery_date').min = today;
    
    // Mise à jour automatique de la date de livraison
    document.getElementById('pickup_date').addEventListener('change', function() {
        const pickupDate = this.value;
        const deliveryInput = document.getElementById('delivery_date');
        if (pickupDate) {
            deliveryInput.min = pickupDate;
            if (deliveryInput.value && deliveryInput.value < pickupDate) {
                deliveryInput.value = pickupDate;
            }
        }
    });
});
</script>
<script src="{{ url_for('static', filename='js/location.js') }}"></script>
<script src="{{ url_for('static', filename='js/currency.js') }}"></script>
{% endblock %}
