{"auth": {"oauth2": {"scopes": {"https://www.googleapis.com/auth/cloud-platform": {"description": "See, edit, configure, and delete your Google Cloud data and see the email address for your Google Account."}}}}, "basePath": "", "baseUrl": "https://datamigration.googleapis.com/", "batchPath": "batch", "canonicalName": "Database Migration Service", "description": "Manage Cloud Database Migration Service resources on Google Cloud Platform.", "discoveryVersion": "v1", "documentationLink": "https://cloud.google.com/database-migration/", "fullyEncodeReservedExpansion": true, "icons": {"x16": "http://www.google.com/images/icons/product/search-16.gif", "x32": "http://www.google.com/images/icons/product/search-32.gif"}, "id": "datamigration:v1", "kind": "discovery#restDescription", "mtlsRootUrl": "https://datamigration.mtls.googleapis.com/", "name": "datamigration", "ownerDomain": "google.com", "ownerName": "Google", "parameters": {"$.xgafv": {"description": "V1 error format.", "enum": ["1", "2"], "enumDescriptions": ["v1 error format", "v2 error format"], "location": "query", "type": "string"}, "access_token": {"description": "OAuth access token.", "location": "query", "type": "string"}, "alt": {"default": "json", "description": "Data format for response.", "enum": ["json", "media", "proto"], "enumDescriptions": ["Responses with Content-Type of application/json", "Media download with context-dependent Content-Type", "Responses with Content-Type of application/x-protobuf"], "location": "query", "type": "string"}, "callback": {"description": "JSONP", "location": "query", "type": "string"}, "fields": {"description": "Selector specifying which fields to include in a partial response.", "location": "query", "type": "string"}, "key": {"description": "API key. Your API key identifies your project and provides you with API access, quota, and reports. Required unless you provide an OAuth 2.0 token.", "location": "query", "type": "string"}, "oauth_token": {"description": "OAuth 2.0 token for the current user.", "location": "query", "type": "string"}, "prettyPrint": {"default": "true", "description": "Returns response with indentations and line breaks.", "location": "query", "type": "boolean"}, "quotaUser": {"description": "Available to use for quota purposes for server-side applications. Can be any arbitrary string assigned to a user, but should not exceed 40 characters.", "location": "query", "type": "string"}, "uploadType": {"description": "Legacy upload protocol for media (e.g. \"media\", \"multipart\").", "location": "query", "type": "string"}, "upload_protocol": {"description": "Upload protocol for media (e.g. \"raw\", \"multipart\").", "location": "query", "type": "string"}}, "protocol": "rest", "resources": {"projects": {"resources": {"locations": {"methods": {"fetchStaticIps": {"description": "Fetches a set of static IP addresses that need to be allowlisted by the customer when using the static-IP connectivity method.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}:fetchStaticIps", "httpMethod": "GET", "id": "datamigration.projects.locations.fetchStaticIps", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The resource name for the location for which static IPs should be returned. Must be in the format `projects/*/locations/*`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}, "pageSize": {"description": "Maximum number of IPs to return.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "A page token, received from a previous `FetchStaticIps` call.", "location": "query", "type": "string"}}, "path": "v1/{+name}:fetchStaticIps", "response": {"$ref": "FetchStaticIpsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Gets information about a location.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}", "httpMethod": "GET", "id": "datamigration.projects.locations.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Resource name for the location.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Location"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists information about the supported locations for this service.", "flatPath": "v1/projects/{projectsId}/locations", "httpMethod": "GET", "id": "datamigration.projects.locations.list", "parameterOrder": ["name"], "parameters": {"extraLocationTypes": {"description": "Optional. A list of extra location types that should be used as conditions for controlling the visibility of the locations.", "location": "query", "repeated": true, "type": "string"}, "filter": {"description": "A filter to narrow down results to a preferred subset. The filtering language accepts strings like `\"displayName=tokyo\"`, and is documented in more detail in [AIP-160](https://google.aip.dev/160).", "location": "query", "type": "string"}, "name": {"description": "The resource that owns the locations collection, if applicable.", "location": "path", "pattern": "^projects/[^/]+$", "required": true, "type": "string"}, "pageSize": {"description": "The maximum number of results to return. If not set, the service selects a default.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "A page token received from the `next_page_token` field in the response. Send that page token to receive the subsequent page.", "location": "query", "type": "string"}}, "path": "v1/{+name}/locations", "response": {"$ref": "ListLocationsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}, "resources": {"connectionProfiles": {"methods": {"create": {"description": "Creates a new connection profile in a given project and location.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/connectionProfiles", "httpMethod": "POST", "id": "datamigration.projects.locations.connectionProfiles.create", "parameterOrder": ["parent"], "parameters": {"connectionProfileId": {"description": "Required. The connection profile identifier.", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent which owns this collection of connection profiles.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "Optional. A unique ID used to identify the request. If the server receives two requests with the same ID, then the second request is ignored. It is recommended to always set this value to a UUID. The ID must contain only letters (a-z, A-Z), numbers (0-9), underscores (_), and hyphens (-). The maximum length is 40 characters.", "location": "query", "type": "string"}, "skipValidation": {"description": "Optional. Create the connection profile without validating it. The default is false. Only supported for Oracle connection profiles.", "location": "query", "type": "boolean"}, "validateOnly": {"description": "Optional. Only validate the connection profile, but don't create any resources. The default is false. Only supported for Oracle connection profiles.", "location": "query", "type": "boolean"}}, "path": "v1/{+parent}/connectionProfiles", "request": {"$ref": "ConnectionProfile"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Deletes a single Database Migration Service connection profile. A connection profile can only be deleted if it is not in use by any active migration jobs.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/connectionProfiles/{connectionProfilesId}", "httpMethod": "DELETE", "id": "datamigration.projects.locations.connectionProfiles.delete", "parameterOrder": ["name"], "parameters": {"force": {"description": "In case of force delete, the CloudSQL replica database is also deleted (only for CloudSQL connection profile).", "location": "query", "type": "boolean"}, "name": {"description": "Required. Name of the connection profile resource to delete.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/connectionProfiles/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "A unique ID used to identify the request. If the server receives two requests with the same ID, then the second request is ignored. It is recommended to always set this value to a UUID. The ID must contain only letters (a-z, A-Z), numbers (0-9), underscores (_), and hyphens (-). The maximum length is 40 characters.", "location": "query", "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Gets details of a single connection profile.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/connectionProfiles/{connectionProfilesId}", "httpMethod": "GET", "id": "datamigration.projects.locations.connectionProfiles.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Name of the connection profile resource to get.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/connectionProfiles/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "ConnectionProfile"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "getIamPolicy": {"description": "Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/connectionProfiles/{connectionProfilesId}:getIamPolicy", "httpMethod": "GET", "id": "datamigration.projects.locations.connectionProfiles.getIamPolicy", "parameterOrder": ["resource"], "parameters": {"options.requestedPolicyVersion": {"description": "Optional. The maximum policy version that will be used to format the policy. Valid values are 0, 1, and 3. Requests specifying an invalid value will be rejected. Requests for policies with any conditional role bindings must specify version 3. Policies with no conditional role bindings may specify any valid value or leave the field unset. The policy in the response might use the policy version that you specified, or it might use a lower policy version. For example, if you specify version 3, but the policy has no conditional role bindings, the response uses version 1. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies).", "format": "int32", "location": "query", "type": "integer"}, "resource": {"description": "REQUIRED: The resource for which the policy is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/connectionProfiles/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+resource}:getIamPolicy", "response": {"$ref": "Policy"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Retrieves a list of all connection profiles in a given project and location.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/connectionProfiles", "httpMethod": "GET", "id": "datamigration.projects.locations.connectionProfiles.list", "parameterOrder": ["parent"], "parameters": {"filter": {"description": "A filter expression that filters connection profiles listed in the response. The expression must specify the field name, a comparison operator, and the value that you want to use for filtering. The value must be a string, a number, or a boolean. The comparison operator must be either =, !=, >, or <. For example, list connection profiles created this year by specifying **createTime %gt; 2020-01-01T00:00:00.000000000Z**. You can also filter nested fields. For example, you could specify **mySql.username = %lt;my_username%gt;** to list all connection profiles configured to connect with a specific username.", "location": "query", "type": "string"}, "orderBy": {"description": "A comma-separated list of fields to order results according to.", "location": "query", "type": "string"}, "pageSize": {"description": "The maximum number of connection profiles to return. The service may return fewer than this value. If unspecified, at most 50 connection profiles will be returned. The maximum value is 1000; values above 1000 are coerced to 1000.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "A page token, received from a previous `ListConnectionProfiles` call. Provide this to retrieve the subsequent page. When paginating, all other parameters provided to `ListConnectionProfiles` must match the call that provided the page token.", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent which owns this collection of connection profiles.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/connectionProfiles", "response": {"$ref": "ListConnectionProfilesResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "patch": {"description": "Update the configuration of a single connection profile.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/connectionProfiles/{connectionProfilesId}", "httpMethod": "PATCH", "id": "datamigration.projects.locations.connectionProfiles.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "The name of this connection profile resource in the form of projects/{project}/locations/{location}/connectionProfiles/{connectionProfile}.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/connectionProfiles/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "Optional. A unique ID used to identify the request. If the server receives two requests with the same ID, then the second request is ignored. It is recommended to always set this value to a UUID. The ID must contain only letters (a-z, A-Z), numbers (0-9), underscores (_), and hyphens (-). The maximum length is 40 characters.", "location": "query", "type": "string"}, "skipValidation": {"description": "Optional. Update the connection profile without validating it. The default is false. Only supported for Oracle connection profiles.", "location": "query", "type": "boolean"}, "updateMask": {"description": "Required. Field mask is used to specify the fields to be overwritten by the update in the conversion workspace resource.", "format": "google-fieldmask", "location": "query", "type": "string"}, "validateOnly": {"description": "Optional. Only validate the connection profile, but don't update any resources. The default is false. Only supported for Oracle connection profiles.", "location": "query", "type": "boolean"}}, "path": "v1/{+name}", "request": {"$ref": "ConnectionProfile"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "setIamPolicy": {"description": "Sets the access control policy on the specified resource. Replaces any existing policy. Can return `NOT_FOUND`, `INVALID_ARGUMENT`, and `PERMISSION_DENIED` errors.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/connectionProfiles/{connectionProfilesId}:setIamPolicy", "httpMethod": "POST", "id": "datamigration.projects.locations.connectionProfiles.setIamPolicy", "parameterOrder": ["resource"], "parameters": {"resource": {"description": "REQUIRED: The resource for which the policy is being specified. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/connectionProfiles/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+resource}:setIamPolicy", "request": {"$ref": "SetIamPolicyRequest"}, "response": {"$ref": "Policy"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "testIamPermissions": {"description": "Returns permissions that a caller has on the specified resource. If the resource does not exist, this will return an empty set of permissions, not a `NOT_FOUND` error. Note: This operation is designed to be used for building permission-aware UIs and command-line tools, not for authorization checking. This operation may \"fail open\" without warning.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/connectionProfiles/{connectionProfilesId}:testIamPermissions", "httpMethod": "POST", "id": "datamigration.projects.locations.connectionProfiles.testIamPermissions", "parameterOrder": ["resource"], "parameters": {"resource": {"description": "REQUIRED: The resource for which the policy detail is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/connectionProfiles/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+resource}:testIamPermissions", "request": {"$ref": "TestIamPermissionsRequest"}, "response": {"$ref": "TestIamPermissionsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}, "conversionWorkspaces": {"methods": {"apply": {"description": "Applies draft tree onto a specific destination database.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/conversionWorkspaces/{conversionWorkspacesId}:apply", "httpMethod": "POST", "id": "datamigration.projects.locations.conversionWorkspaces.apply", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the conversion workspace resource for which to apply the draft tree. Must be in the form of: projects/{project}/locations/{location}/conversionWorkspaces/{conversion_workspace}.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/conversionWorkspaces/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}:apply", "request": {"$ref": "ApplyConversionWorkspaceRequest"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "commit": {"description": "Marks all the data in the conversion workspace as committed.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/conversionWorkspaces/{conversionWorkspacesId}:commit", "httpMethod": "POST", "id": "datamigration.projects.locations.conversionWorkspaces.commit", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Name of the conversion workspace resource to commit.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/conversionWorkspaces/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}:commit", "request": {"$ref": "CommitConversionWorkspaceRequest"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "convert": {"description": "Creates a draft tree schema for the destination database.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/conversionWorkspaces/{conversionWorkspacesId}:convert", "httpMethod": "POST", "id": "datamigration.projects.locations.conversionWorkspaces.convert", "parameterOrder": ["name"], "parameters": {"name": {"description": "Name of the conversion workspace resource to convert in the form of: projects/{project}/locations/{location}/conversionWorkspaces/{conversion_workspace}.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/conversionWorkspaces/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}:convert", "request": {"$ref": "ConvertConversionWorkspaceRequest"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "create": {"description": "Creates a new conversion workspace in a given project and location.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/conversionWorkspaces", "httpMethod": "POST", "id": "datamigration.projects.locations.conversionWorkspaces.create", "parameterOrder": ["parent"], "parameters": {"conversionWorkspaceId": {"description": "Required. The ID of the conversion workspace to create.", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent which owns this collection of conversion workspaces.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "A unique ID used to identify the request. If the server receives two requests with the same ID, then the second request is ignored. It is recommended to always set this value to a UUID. The ID must contain only letters (a-z, A-Z), numbers (0-9), underscores (_), and hyphens (-). The maximum length is 40 characters.", "location": "query", "type": "string"}}, "path": "v1/{+parent}/conversionWorkspaces", "request": {"$ref": "ConversionWorkspace"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Deletes a single conversion workspace.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/conversionWorkspaces/{conversionWorkspacesId}", "httpMethod": "DELETE", "id": "datamigration.projects.locations.conversionWorkspaces.delete", "parameterOrder": ["name"], "parameters": {"force": {"description": "Force delete the conversion workspace, even if there's a running migration that is using the workspace.", "location": "query", "type": "boolean"}, "name": {"description": "Required. Name of the conversion workspace resource to delete.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/conversionWorkspaces/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "A unique ID used to identify the request. If the server receives two requests with the same ID, then the second request is ignored. It is recommended to always set this value to a UUID. The ID must contain only letters (a-z, A-Z), numbers (0-9), underscores (_), and hyphens (-). The maximum length is 40 characters.", "location": "query", "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "describeConversionWorkspaceRevisions": {"description": "Retrieves a list of committed revisions of a specific conversion workspace.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/conversionWorkspaces/{conversionWorkspacesId}:describeConversionWorkspaceRevisions", "httpMethod": "GET", "id": "datamigration.projects.locations.conversionWorkspaces.describeConversionWorkspaceRevisions", "parameterOrder": ["conversionWorkspace"], "parameters": {"commitId": {"description": "Optional. Optional filter to request a specific commit ID.", "location": "query", "type": "string"}, "conversionWorkspace": {"description": "Required. Name of the conversion workspace resource whose revisions are listed. Must be in the form of: projects/{project}/locations/{location}/conversionWorkspaces/{conversion_workspace}.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/conversionWorkspaces/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+conversionWorkspace}:describeConversionWorkspaceRevisions", "response": {"$ref": "DescribeConversionWorkspaceRevisionsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "describeDatabaseEntities": {"description": "Describes the database entities tree for a specific conversion workspace and a specific tree type. Database entities are not resources like conversion workspaces or mapping rules, and they can't be created, updated or deleted. Instead, they are simple data objects describing the structure of the client database.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/conversionWorkspaces/{conversionWorkspacesId}:describeDatabaseEntities", "httpMethod": "GET", "id": "datamigration.projects.locations.conversionWorkspaces.describeDatabaseEntities", "parameterOrder": ["conversionWorkspace"], "parameters": {"commitId": {"description": "Optional. Request a specific commit ID. If not specified, the entities from the latest commit are returned.", "location": "query", "type": "string"}, "conversionWorkspace": {"description": "Required. Name of the conversion workspace resource whose database entities are described. Must be in the form of: projects/{project}/locations/{location}/conversionWorkspaces/{conversion_workspace}.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/conversionWorkspaces/[^/]+$", "required": true, "type": "string"}, "filter": {"description": "Optional. Filter the returned entities based on AIP-160 standard.", "location": "query", "type": "string"}, "pageSize": {"description": "Optional. The maximum number of entities to return. The service may return fewer entities than the value specifies.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Optional. The nextPageToken value received in the previous call to conversionWorkspace.describeDatabaseEntities, used in the subsequent request to retrieve the next page of results. On first call this should be left blank. When paginating, all other parameters provided to conversionWorkspace.describeDatabaseEntities must match the call that provided the page token.", "location": "query", "type": "string"}, "tree": {"description": "Required. The tree to fetch.", "enum": ["DB_TREE_TYPE_UNSPECIFIED", "SOURCE_TREE", "DRAFT_TREE", "DESTINATION_TREE"], "enumDescriptions": ["Unspecified tree type.", "The source database tree.", "The draft database tree.", "The destination database tree."], "location": "query", "type": "string"}, "uncommitted": {"description": "Optional. Whether to retrieve the latest committed version of the entities or the latest version. This field is ignored if a specific commit_id is specified.", "location": "query", "type": "boolean"}, "view": {"description": "Optional. Results view based on AIP-157", "enum": ["DATABASE_ENTITY_VIEW_UNSPECIFIED", "DATABASE_ENTITY_VIEW_BASIC", "DATABASE_ENTITY_VIEW_FULL", "DATABASE_ENTITY_VIEW_ROOT_SUMMARY", "DATABASE_ENTITY_VIEW_FULL_COMPACT"], "enumDescriptions": ["Unspecified view. Defaults to basic view.", "Default view. Does not return DDLs or Issues.", "Return full entity details including mappings, ddl and issues.", "Top-most (Database, Schema) nodes which are returned contains summary details for their descendants such as the number of entities per type and issues rollups. When this view is used, only a single page of result is returned and the page_size property of the request is ignored. The returned page will only include the top-most node types.", "Returns full entity details except for ddls and schema custom features."], "location": "query", "type": "string"}}, "path": "v1/{+conversionWorkspace}:describeDatabaseEntities", "response": {"$ref": "DescribeDatabaseEntitiesResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Gets details of a single conversion workspace.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/conversionWorkspaces/{conversionWorkspacesId}", "httpMethod": "GET", "id": "datamigration.projects.locations.conversionWorkspaces.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Name of the conversion workspace resource to get.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/conversionWorkspaces/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "ConversionWorkspace"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "getIamPolicy": {"description": "Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/conversionWorkspaces/{conversionWorkspacesId}:getIamPolicy", "httpMethod": "GET", "id": "datamigration.projects.locations.conversionWorkspaces.getIamPolicy", "parameterOrder": ["resource"], "parameters": {"options.requestedPolicyVersion": {"description": "Optional. The maximum policy version that will be used to format the policy. Valid values are 0, 1, and 3. Requests specifying an invalid value will be rejected. Requests for policies with any conditional role bindings must specify version 3. Policies with no conditional role bindings may specify any valid value or leave the field unset. The policy in the response might use the policy version that you specified, or it might use a lower policy version. For example, if you specify version 3, but the policy has no conditional role bindings, the response uses version 1. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies).", "format": "int32", "location": "query", "type": "integer"}, "resource": {"description": "REQUIRED: The resource for which the policy is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/conversionWorkspaces/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+resource}:getIamPolicy", "response": {"$ref": "Policy"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists conversion workspaces in a given project and location.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/conversionWorkspaces", "httpMethod": "GET", "id": "datamigration.projects.locations.conversionWorkspaces.list", "parameterOrder": ["parent"], "parameters": {"filter": {"description": "A filter expression that filters conversion workspaces listed in the response. The expression must specify the field name, a comparison operator, and the value that you want to use for filtering. The value must be a string, a number, or a boolean. The comparison operator must be either =, !=, >, or <. For example, list conversion workspaces created this year by specifying **createTime %gt; 2020-01-01T00:00:00.000000000Z.** You can also filter nested fields. For example, you could specify **source.version = \"12.c.1\"** to select all conversion workspaces with source database version equal to 12.c.1.", "location": "query", "type": "string"}, "pageSize": {"description": "The maximum number of conversion workspaces to return. The service may return fewer than this value. If unspecified, at most 50 sets are returned.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "The nextPageToken value received in the previous call to conversionWorkspaces.list, used in the subsequent request to retrieve the next page of results. On first call this should be left blank. When paginating, all other parameters provided to conversionWorkspaces.list must match the call that provided the page token.", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent which owns this collection of conversion workspaces.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/conversionWorkspaces", "response": {"$ref": "ListConversionWorkspacesResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "patch": {"description": "Updates the parameters of a single conversion workspace.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/conversionWorkspaces/{conversionWorkspacesId}", "httpMethod": "PATCH", "id": "datamigration.projects.locations.conversionWorkspaces.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "Full name of the workspace resource, in the form of: projects/{project}/locations/{location}/conversionWorkspaces/{conversion_workspace}.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/conversionWorkspaces/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "A unique ID used to identify the request. If the server receives two requests with the same ID, then the second request is ignored. It is recommended to always set this value to a UUID. The ID must contain only letters (a-z, A-Z), numbers (0-9), underscores (_), and hyphens (-). The maximum length is 40 characters.", "location": "query", "type": "string"}, "updateMask": {"description": "Required. Field mask is used to specify the fields to be overwritten by the update in the conversion workspace resource.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1/{+name}", "request": {"$ref": "ConversionWorkspace"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "rollback": {"description": "Rolls back a conversion workspace to the last committed snapshot.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/conversionWorkspaces/{conversionWorkspacesId}:rollback", "httpMethod": "POST", "id": "datamigration.projects.locations.conversionWorkspaces.rollback", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Name of the conversion workspace resource to roll back to.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/conversionWorkspaces/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}:rollback", "request": {"$ref": "RollbackConversionWorkspaceRequest"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "searchBackgroundJobs": {"description": "Searches/lists the background jobs for a specific conversion workspace. The background jobs are not resources like conversion workspaces or mapping rules, and they can't be created, updated or deleted. Instead, they are a way to expose the data plane jobs log.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/conversionWorkspaces/{conversionWorkspacesId}:searchBackgroundJobs", "httpMethod": "GET", "id": "datamigration.projects.locations.conversionWorkspaces.searchBackgroundJobs", "parameterOrder": ["conversionWorkspace"], "parameters": {"completedUntilTime": {"description": "Optional. If provided, only returns jobs that completed until (not including) the given timestamp.", "format": "google-datetime", "location": "query", "type": "string"}, "conversionWorkspace": {"description": "Required. Name of the conversion workspace resource whose jobs are listed, in the form of: projects/{project}/locations/{location}/conversionWorkspaces/{conversion_workspace}.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/conversionWorkspaces/[^/]+$", "required": true, "type": "string"}, "maxSize": {"description": "Optional. The maximum number of jobs to return. The service may return fewer than this value. If unspecified, at most 100 jobs are returned. The maximum value is 100; values above 100 are coerced to 100.", "format": "int32", "location": "query", "type": "integer"}, "returnMostRecentPerJobType": {"description": "Optional. Whether or not to return just the most recent job per job type,", "location": "query", "type": "boolean"}}, "path": "v1/{+conversionWorkspace}:searchBackgroundJobs", "response": {"$ref": "SearchBackgroundJobsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "seed": {"description": "Imports a snapshot of the source database into the conversion workspace.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/conversionWorkspaces/{conversionWorkspacesId}:seed", "httpMethod": "POST", "id": "datamigration.projects.locations.conversionWorkspaces.seed", "parameterOrder": ["name"], "parameters": {"name": {"description": "Name of the conversion workspace resource to seed with new database structure, in the form of: projects/{project}/locations/{location}/conversionWorkspaces/{conversion_workspace}.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/conversionWorkspaces/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}:seed", "request": {"$ref": "SeedConversionWorkspaceRequest"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "setIamPolicy": {"description": "Sets the access control policy on the specified resource. Replaces any existing policy. Can return `NOT_FOUND`, `INVALID_ARGUMENT`, and `PERMISSION_DENIED` errors.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/conversionWorkspaces/{conversionWorkspacesId}:setIamPolicy", "httpMethod": "POST", "id": "datamigration.projects.locations.conversionWorkspaces.setIamPolicy", "parameterOrder": ["resource"], "parameters": {"resource": {"description": "REQUIRED: The resource for which the policy is being specified. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/conversionWorkspaces/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+resource}:setIamPolicy", "request": {"$ref": "SetIamPolicyRequest"}, "response": {"$ref": "Policy"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "testIamPermissions": {"description": "Returns permissions that a caller has on the specified resource. If the resource does not exist, this will return an empty set of permissions, not a `NOT_FOUND` error. Note: This operation is designed to be used for building permission-aware UIs and command-line tools, not for authorization checking. This operation may \"fail open\" without warning.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/conversionWorkspaces/{conversionWorkspacesId}:testIamPermissions", "httpMethod": "POST", "id": "datamigration.projects.locations.conversionWorkspaces.testIamPermissions", "parameterOrder": ["resource"], "parameters": {"resource": {"description": "REQUIRED: The resource for which the policy detail is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/conversionWorkspaces/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+resource}:testIamPermissions", "request": {"$ref": "TestIamPermissionsRequest"}, "response": {"$ref": "TestIamPermissionsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}, "resources": {"mappingRules": {"methods": {"create": {"description": "Creates a new mapping rule for a given conversion workspace.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/conversionWorkspaces/{conversionWorkspacesId}/mappingRules", "httpMethod": "POST", "id": "datamigration.projects.locations.conversionWorkspaces.mappingRules.create", "parameterOrder": ["parent"], "parameters": {"mappingRuleId": {"description": "Required. The ID of the rule to create.", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent which owns this collection of mapping rules.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/conversionWorkspaces/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "A unique ID used to identify the request. If the server receives two requests with the same ID, then the second request is ignored. It is recommended to always set this value to a UUID. The ID must contain only letters (a-z, A-Z), numbers (0-9), underscores (_), and hyphens (-). The maximum length is 40 characters.", "location": "query", "type": "string"}}, "path": "v1/{+parent}/mappingRules", "request": {"$ref": "MappingRule"}, "response": {"$ref": "MappingRule"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Deletes a single mapping rule.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/conversionWorkspaces/{conversionWorkspacesId}/mappingRules/{mappingRulesId}", "httpMethod": "DELETE", "id": "datamigration.projects.locations.conversionWorkspaces.mappingRules.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Name of the mapping rule resource to delete.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/conversionWorkspaces/[^/]+/mappingRules/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "Optional. A unique ID used to identify the request. If the server receives two requests with the same ID, then the second request is ignored. It is recommended to always set this value to a UUID. The ID must contain only letters (a-z, A-Z), numbers (0-9), underscores (_), and hyphens (-). The maximum length is 40 characters.", "location": "query", "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Empty"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Gets the details of a mapping rule.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/conversionWorkspaces/{conversionWorkspacesId}/mappingRules/{mappingRulesId}", "httpMethod": "GET", "id": "datamigration.projects.locations.conversionWorkspaces.mappingRules.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Name of the mapping rule resource to get. Example: conversionWorkspaces/123/mappingRules/rule123 In order to retrieve a previous revision of the mapping rule, also provide the revision ID. Example: conversionWorkspace/123/mappingRules/rule123@c7cfa2a8c7cfa2a8c7cfa2a8c7cfa2a8", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/conversionWorkspaces/[^/]+/mappingRules/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "MappingRule"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "import": {"description": "Imports the mapping rules for a given conversion workspace. Supports various formats of external rules files.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/conversionWorkspaces/{conversionWorkspacesId}/mappingRules:import", "httpMethod": "POST", "id": "datamigration.projects.locations.conversionWorkspaces.mappingRules.import", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. Name of the conversion workspace resource to import the rules to in the form of: projects/{project}/locations/{location}/conversionWorkspaces/{conversion_workspace}.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/conversionWorkspaces/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/mappingRules:import", "request": {"$ref": "ImportMappingRulesRequest"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists the mapping rules for a specific conversion workspace.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/conversionWorkspaces/{conversionWorkspacesId}/mappingRules", "httpMethod": "GET", "id": "datamigration.projects.locations.conversionWorkspaces.mappingRules.list", "parameterOrder": ["parent"], "parameters": {"pageSize": {"description": "The maximum number of rules to return. The service may return fewer than this value.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "The nextPageToken value received in the previous call to mappingRules.list, used in the subsequent request to retrieve the next page of results. On first call this should be left blank. When paginating, all other parameters provided to mappingRules.list must match the call that provided the page token.", "location": "query", "type": "string"}, "parent": {"description": "Required. Name of the conversion workspace resource whose mapping rules are listed in the form of: projects/{project}/locations/{location}/conversionWorkspaces/{conversion_workspace}.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/conversionWorkspaces/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/mappingRules", "response": {"$ref": "ListMappingRulesResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}}}, "migrationJobs": {"methods": {"create": {"description": "Creates a new migration job in a given project and location.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/migrationJobs", "httpMethod": "POST", "id": "datamigration.projects.locations.migrationJobs.create", "parameterOrder": ["parent"], "parameters": {"migrationJobId": {"description": "Required. The ID of the instance to create.", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent which owns this collection of migration jobs.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "Optional. A unique ID used to identify the request. If the server receives two requests with the same ID, then the second request is ignored. It is recommended to always set this value to a UUID. The ID must contain only letters (a-z, A-Z), numbers (0-9), underscores (_), and hyphens (-). The maximum length is 40 characters.", "location": "query", "type": "string"}}, "path": "v1/{+parent}/migrationJobs", "request": {"$ref": "<PERSON><PERSON><PERSON><PERSON>"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Deletes a single migration job.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/migrationJobs/{migrationJobsId}", "httpMethod": "DELETE", "id": "datamigration.projects.locations.migrationJobs.delete", "parameterOrder": ["name"], "parameters": {"force": {"description": "The destination CloudSQL connection profile is always deleted with the migration job. In case of force delete, the destination CloudSQL replica database is also deleted.", "location": "query", "type": "boolean"}, "name": {"description": "Required. Name of the migration job resource to delete.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/migrationJobs/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "A unique ID used to identify the request. If the server receives two requests with the same ID, then the second request is ignored. It is recommended to always set this value to a UUID. The ID must contain only letters (a-z, A-Z), numbers (0-9), underscores (_), and hyphens (-). The maximum length is 40 characters.", "location": "query", "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "demoteDestination": {"description": "Demotes the destination database to become a read replica of the source. This is applicable for the following migrations: 1. MySQL to Cloud SQL for MySQL 2. PostgreSQL to Cloud SQL for PostgreSQL 3. PostgreSQL to AlloyDB for PostgreSQL.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/migrationJobs/{migrationJobsId}:demoteDestination", "httpMethod": "POST", "id": "datamigration.projects.locations.migrationJobs.demoteDestination", "parameterOrder": ["name"], "parameters": {"name": {"description": "Name of the migration job resource to demote its destination.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/migrationJobs/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}:demoteDestination", "request": {"$ref": "DemoteDestinationRequest"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "fetchSourceObjects": {"description": "Retrieves objects from the source database that can be selected for data migration. This is applicable for the following migrations: 1. PostgreSQL to Cloud SQL for PostgreSQL 2. PostgreSQL to AlloyDB for PostgreSQL.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/migrationJobs/{migrationJobsId}:fetchSourceObjects", "httpMethod": "GET", "id": "datamigration.projects.locations.migrationJobs.fetchSourceObjects", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The resource name for the migration job for which source objects should be returned.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/migrationJobs/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}:fetchSourceObjects", "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "generateSshScript": {"description": "Generate a SSH configuration script to configure the reverse SSH connectivity.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/migrationJobs/{migrationJobsId}:generateSshScript", "httpMethod": "POST", "id": "datamigration.projects.locations.migrationJobs.generateSshScript", "parameterOrder": ["migrationJob"], "parameters": {"migrationJob": {"description": "Name of the migration job resource to generate the SSH script.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/migrationJobs/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+migrationJob}:generateSshScript", "request": {"$ref": "GenerateSshScriptRequest"}, "response": {"$ref": "SshScript"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "generateTcpProxyScript": {"description": "Generate a TCP Proxy configuration script to configure a cloud-hosted VM running a TCP Proxy.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/migrationJobs/{migrationJobsId}:generateTcpProxyScript", "httpMethod": "POST", "id": "datamigration.projects.locations.migrationJobs.generateTcpProxyScript", "parameterOrder": ["migrationJob"], "parameters": {"migrationJob": {"description": "Name of the migration job resource to generate the TCP Proxy script.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/migrationJobs/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+migrationJob}:generateTcpProxyScript", "request": {"$ref": "GenerateTcpProxyScriptRequest"}, "response": {"$ref": "TcpProxyScript"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Gets details of a single migration job.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/migrationJobs/{migrationJobsId}", "httpMethod": "GET", "id": "datamigration.projects.locations.migrationJobs.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Name of the migration job resource to get.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/migrationJobs/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "<PERSON><PERSON><PERSON><PERSON>"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "getIamPolicy": {"description": "Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/migrationJobs/{migrationJobsId}:getIamPolicy", "httpMethod": "GET", "id": "datamigration.projects.locations.migrationJobs.getIamPolicy", "parameterOrder": ["resource"], "parameters": {"options.requestedPolicyVersion": {"description": "Optional. The maximum policy version that will be used to format the policy. Valid values are 0, 1, and 3. Requests specifying an invalid value will be rejected. Requests for policies with any conditional role bindings must specify version 3. Policies with no conditional role bindings may specify any valid value or leave the field unset. The policy in the response might use the policy version that you specified, or it might use a lower policy version. For example, if you specify version 3, but the policy has no conditional role bindings, the response uses version 1. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies).", "format": "int32", "location": "query", "type": "integer"}, "resource": {"description": "REQUIRED: The resource for which the policy is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/migrationJobs/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+resource}:getIamPolicy", "response": {"$ref": "Policy"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists migration jobs in a given project and location.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/migrationJobs", "httpMethod": "GET", "id": "datamigration.projects.locations.migrationJobs.list", "parameterOrder": ["parent"], "parameters": {"filter": {"description": "A filter expression that filters migration jobs listed in the response. The expression must specify the field name, a comparison operator, and the value that you want to use for filtering. The value must be a string, a number, or a boolean. The comparison operator must be either =, !=, >, or <. For example, list migration jobs created this year by specifying **createTime %gt; 2020-01-01T00:00:00.000000000Z.** You can also filter nested fields. For example, you could specify **reverseSshConnectivity.vmIp = \"*******\"** to select all migration jobs connecting through the specific SSH tunnel bastion.", "location": "query", "type": "string"}, "orderBy": {"description": "Sort the results based on the migration job name. Valid values are: \"name\", \"name asc\", and \"name desc\".", "location": "query", "type": "string"}, "pageSize": {"description": "The maximum number of migration jobs to return. The service may return fewer than this value. If unspecified, at most 50 migration jobs will be returned. The maximum value is 1000; values above 1000 are coerced to 1000.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "The nextPageToken value received in the previous call to migrationJobs.list, used in the subsequent request to retrieve the next page of results. On first call this should be left blank. When paginating, all other parameters provided to migrationJobs.list must match the call that provided the page token.", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent which owns this collection of migrationJobs.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/migrationJobs", "response": {"$ref": "ListMigrationJobsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "patch": {"description": "Updates the parameters of a single migration job.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/migrationJobs/{migrationJobsId}", "httpMethod": "PATCH", "id": "datamigration.projects.locations.migrationJobs.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "The name (URI) of this migration job resource, in the form of: projects/{project}/locations/{location}/migrationJobs/{migrationJob}.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/migrationJobs/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "A unique ID used to identify the request. If the server receives two requests with the same ID, then the second request is ignored. It is recommended to always set this value to a UUID. The ID must contain only letters (a-z, A-Z), numbers (0-9), underscores (_), and hyphens (-). The maximum length is 40 characters.", "location": "query", "type": "string"}, "updateMask": {"description": "Required. Field mask is used to specify the fields to be overwritten by the update in the conversion workspace resource.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1/{+name}", "request": {"$ref": "<PERSON><PERSON><PERSON><PERSON>"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "promote": {"description": "Promote a migration job, stopping replication to the destination and promoting the destination to be a standalone database.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/migrationJobs/{migrationJobsId}:promote", "httpMethod": "POST", "id": "datamigration.projects.locations.migrationJobs.promote", "parameterOrder": ["name"], "parameters": {"name": {"description": "Name of the migration job resource to promote.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/migrationJobs/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}:promote", "request": {"$ref": "PromoteMigrationJobRequest"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "restart": {"description": "Restart a stopped or failed migration job, resetting the destination instance to its original state and starting the migration process from scratch.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/migrationJobs/{migrationJobsId}:restart", "httpMethod": "POST", "id": "datamigration.projects.locations.migrationJobs.restart", "parameterOrder": ["name"], "parameters": {"name": {"description": "Name of the migration job resource to restart.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/migrationJobs/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}:restart", "request": {"$ref": "RestartMigrationJobRequest"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "resume": {"description": "Resume a migration job that is currently stopped and is resumable (was stopped during CDC phase).", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/migrationJobs/{migrationJobsId}:resume", "httpMethod": "POST", "id": "datamigration.projects.locations.migrationJobs.resume", "parameterOrder": ["name"], "parameters": {"name": {"description": "Name of the migration job resource to resume.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/migrationJobs/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}:resume", "request": {"$ref": "ResumeMigrationJobRequest"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "setIamPolicy": {"description": "Sets the access control policy on the specified resource. Replaces any existing policy. Can return `NOT_FOUND`, `INVALID_ARGUMENT`, and `PERMISSION_DENIED` errors.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/migrationJobs/{migrationJobsId}:setIamPolicy", "httpMethod": "POST", "id": "datamigration.projects.locations.migrationJobs.setIamPolicy", "parameterOrder": ["resource"], "parameters": {"resource": {"description": "REQUIRED: The resource for which the policy is being specified. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/migrationJobs/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+resource}:setIamPolicy", "request": {"$ref": "SetIamPolicyRequest"}, "response": {"$ref": "Policy"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "start": {"description": "Start an already created migration job.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/migrationJobs/{migrationJobsId}:start", "httpMethod": "POST", "id": "datamigration.projects.locations.migrationJobs.start", "parameterOrder": ["name"], "parameters": {"name": {"description": "Name of the migration job resource to start.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/migrationJobs/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}:start", "request": {"$ref": "StartMigrationJobRequest"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "stop": {"description": "Stops a running migration job.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/migrationJobs/{migrationJobsId}:stop", "httpMethod": "POST", "id": "datamigration.projects.locations.migrationJobs.stop", "parameterOrder": ["name"], "parameters": {"name": {"description": "Name of the migration job resource to stop.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/migrationJobs/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}:stop", "request": {"$ref": "StopMigrationJobRequest"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "testIamPermissions": {"description": "Returns permissions that a caller has on the specified resource. If the resource does not exist, this will return an empty set of permissions, not a `NOT_FOUND` error. Note: This operation is designed to be used for building permission-aware UIs and command-line tools, not for authorization checking. This operation may \"fail open\" without warning.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/migrationJobs/{migrationJobsId}:testIamPermissions", "httpMethod": "POST", "id": "datamigration.projects.locations.migrationJobs.testIamPermissions", "parameterOrder": ["resource"], "parameters": {"resource": {"description": "REQUIRED: The resource for which the policy detail is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/migrationJobs/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+resource}:testIamPermissions", "request": {"$ref": "TestIamPermissionsRequest"}, "response": {"$ref": "TestIamPermissionsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "verify": {"description": "Verify a migration job, making sure the destination can reach the source and that all configuration and prerequisites are met.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/migrationJobs/{migrationJobsId}:verify", "httpMethod": "POST", "id": "datamigration.projects.locations.migrationJobs.verify", "parameterOrder": ["name"], "parameters": {"name": {"description": "Name of the migration job resource to verify.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/migrationJobs/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}:verify", "request": {"$ref": "VerifyMigrationJobRequest"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}, "resources": {"objects": {"methods": {"get": {"description": "Use this method to get details about a migration job object.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/migrationJobs/{migrationJobsId}/objects/{objectsId}", "httpMethod": "GET", "id": "datamigration.projects.locations.migrationJobs.objects.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the migration job object resource to get.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/migrationJobs/[^/]+/objects/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "MigrationJobObject"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "getIamPolicy": {"description": "Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/migrationJobs/{migrationJobsId}/objects/{objectsId}:getIamPolicy", "httpMethod": "GET", "id": "datamigration.projects.locations.migrationJobs.objects.getIamPolicy", "parameterOrder": ["resource"], "parameters": {"options.requestedPolicyVersion": {"description": "Optional. The maximum policy version that will be used to format the policy. Valid values are 0, 1, and 3. Requests specifying an invalid value will be rejected. Requests for policies with any conditional role bindings must specify version 3. Policies with no conditional role bindings may specify any valid value or leave the field unset. The policy in the response might use the policy version that you specified, or it might use a lower policy version. For example, if you specify version 3, but the policy has no conditional role bindings, the response uses version 1. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies).", "format": "int32", "location": "query", "type": "integer"}, "resource": {"description": "REQUIRED: The resource for which the policy is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/migrationJobs/[^/]+/objects/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+resource}:getIamPolicy", "response": {"$ref": "Policy"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Use this method to list the objects of a specific migration job.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/migrationJobs/{migrationJobsId}/objects", "httpMethod": "GET", "id": "datamigration.projects.locations.migrationJobs.objects.list", "parameterOrder": ["parent"], "parameters": {"pageSize": {"description": "Maximum number of objects to return. Default is 50. The maximum value is 1000; values above 1000 will be coerced to 1000.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Page token received from a previous `ListMigrationJObObjectsRequest` call. Provide this to retrieve the subsequent page. When paginating, all other parameters provided to `ListMigrationJobObjectsRequest` must match the call that provided the page token.", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent migration job that owns the collection of objects.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/migrationJobs/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/objects", "response": {"$ref": "ListMigrationJobObjectsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "lookup": {"description": "Use this method to look up a migration job object by its source object identifier.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/migrationJobs/{migrationJobsId}/objects:lookup", "httpMethod": "POST", "id": "datamigration.projects.locations.migrationJobs.objects.lookup", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. The parent migration job that owns the collection of objects.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/migrationJobs/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/objects:lookup", "request": {"$ref": "LookupMigrationJobObjectRequest"}, "response": {"$ref": "MigrationJobObject"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "setIamPolicy": {"description": "Sets the access control policy on the specified resource. Replaces any existing policy. Can return `NOT_FOUND`, `INVALID_ARGUMENT`, and `PERMISSION_DENIED` errors.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/migrationJobs/{migrationJobsId}/objects/{objectsId}:setIamPolicy", "httpMethod": "POST", "id": "datamigration.projects.locations.migrationJobs.objects.setIamPolicy", "parameterOrder": ["resource"], "parameters": {"resource": {"description": "REQUIRED: The resource for which the policy is being specified. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/migrationJobs/[^/]+/objects/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+resource}:setIamPolicy", "request": {"$ref": "SetIamPolicyRequest"}, "response": {"$ref": "Policy"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "testIamPermissions": {"description": "Returns permissions that a caller has on the specified resource. If the resource does not exist, this will return an empty set of permissions, not a `NOT_FOUND` error. Note: This operation is designed to be used for building permission-aware UIs and command-line tools, not for authorization checking. This operation may \"fail open\" without warning.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/migrationJobs/{migrationJobsId}/objects/{objectsId}:testIamPermissions", "httpMethod": "POST", "id": "datamigration.projects.locations.migrationJobs.objects.testIamPermissions", "parameterOrder": ["resource"], "parameters": {"resource": {"description": "REQUIRED: The resource for which the policy detail is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/migrationJobs/[^/]+/objects/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+resource}:testIamPermissions", "request": {"$ref": "TestIamPermissionsRequest"}, "response": {"$ref": "TestIamPermissionsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}}}, "operations": {"methods": {"cancel": {"description": "Starts asynchronous cancellation on a long-running operation. The server makes a best effort to cancel the operation, but success is not guaranteed. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`. Clients can use Operations.GetOperation or other methods to check whether the cancellation succeeded or whether the operation completed despite cancellation. On successful cancellation, the operation is not deleted; instead, it becomes an operation with an Operation.error value with a google.rpc.Status.code of `1`, corresponding to `Code.CANCELLED`.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/operations/{operationsId}:cancel", "httpMethod": "POST", "id": "datamigration.projects.locations.operations.cancel", "parameterOrder": ["name"], "parameters": {"name": {"description": "The name of the operation resource to be cancelled.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/operations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}:cancel", "request": {"$ref": "CancelOperationRequest"}, "response": {"$ref": "Empty"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Deletes a long-running operation. This method indicates that the client is no longer interested in the operation result. It does not cancel the operation. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/operations/{operationsId}", "httpMethod": "DELETE", "id": "datamigration.projects.locations.operations.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "The name of the operation resource to be deleted.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/operations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Empty"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/operations/{operationsId}", "httpMethod": "GET", "id": "datamigration.projects.locations.operations.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "The name of the operation resource.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/operations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists operations that match the specified filter in the request. If the server doesn't support this method, it returns `UNIMPLEMENTED`.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/operations", "httpMethod": "GET", "id": "datamigration.projects.locations.operations.list", "parameterOrder": ["name"], "parameters": {"filter": {"description": "The standard list filter.", "location": "query", "type": "string"}, "name": {"description": "The name of the operation's parent resource.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}, "pageSize": {"description": "The standard list page size.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "The standard list page token.", "location": "query", "type": "string"}}, "path": "v1/{+name}/operations", "response": {"$ref": "ListOperationsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}, "privateConnections": {"methods": {"create": {"description": "Creates a new private connection in a given project and location.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/privateConnections", "httpMethod": "POST", "id": "datamigration.projects.locations.privateConnections.create", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. The parent that owns the collection of PrivateConnections.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}, "privateConnectionId": {"description": "Required. The private connection identifier.", "location": "query", "type": "string"}, "requestId": {"description": "Optional. A unique ID used to identify the request. If the server receives two requests with the same ID, then the second request is ignored. It is recommended to always set this value to a UUID. The ID must contain only letters (a-z, A-Z), numbers (0-9), underscores (_), and hyphens (-). The maximum length is 40 characters.", "location": "query", "type": "string"}, "skipValidation": {"description": "Optional. If set to true, will skip validations.", "location": "query", "type": "boolean"}}, "path": "v1/{+parent}/privateConnections", "request": {"$ref": "PrivateConnection"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Deletes a single Database Migration Service private connection.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/privateConnections/{privateConnectionsId}", "httpMethod": "DELETE", "id": "datamigration.projects.locations.privateConnections.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the private connection to delete.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/privateConnections/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "Optional. A unique ID used to identify the request. If the server receives two requests with the same ID, then the second request is ignored. It is recommended to always set this value to a UUID. The ID must contain only letters (a-z, A-Z), numbers (0-9), underscores (_), and hyphens (-). The maximum length is 40 characters.", "location": "query", "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Gets details of a single private connection.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/privateConnections/{privateConnectionsId}", "httpMethod": "GET", "id": "datamigration.projects.locations.privateConnections.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the private connection to get.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/privateConnections/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "PrivateConnection"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "getIamPolicy": {"description": "Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/privateConnections/{privateConnectionsId}:getIamPolicy", "httpMethod": "GET", "id": "datamigration.projects.locations.privateConnections.getIamPolicy", "parameterOrder": ["resource"], "parameters": {"options.requestedPolicyVersion": {"description": "Optional. The maximum policy version that will be used to format the policy. Valid values are 0, 1, and 3. Requests specifying an invalid value will be rejected. Requests for policies with any conditional role bindings must specify version 3. Policies with no conditional role bindings may specify any valid value or leave the field unset. The policy in the response might use the policy version that you specified, or it might use a lower policy version. For example, if you specify version 3, but the policy has no conditional role bindings, the response uses version 1. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies).", "format": "int32", "location": "query", "type": "integer"}, "resource": {"description": "REQUIRED: The resource for which the policy is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/privateConnections/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+resource}:getIamPolicy", "response": {"$ref": "Policy"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Retrieves a list of private connections in a given project and location.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/privateConnections", "httpMethod": "GET", "id": "datamigration.projects.locations.privateConnections.list", "parameterOrder": ["parent"], "parameters": {"filter": {"description": "A filter expression that filters private connections listed in the response. The expression must specify the field name, a comparison operator, and the value that you want to use for filtering. The value must be a string, a number, or a boolean. The comparison operator must be either =, !=, >, or <. For example, list private connections created this year by specifying **createTime %gt; 2021-01-01T00:00:00.000000000Z**.", "location": "query", "type": "string"}, "orderBy": {"description": "Order by fields for the result.", "location": "query", "type": "string"}, "pageSize": {"description": "Maximum number of private connections to return. If unspecified, at most 50 private connections that are returned. The maximum value is 1000; values above 1000 are coerced to 1000.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Page token received from a previous `ListPrivateConnections` call. Provide this to retrieve the subsequent page. When paginating, all other parameters provided to `ListPrivateConnections` must match the call that provided the page token.", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent that owns the collection of private connections.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/privateConnections", "response": {"$ref": "ListPrivateConnectionsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "setIamPolicy": {"description": "Sets the access control policy on the specified resource. Replaces any existing policy. Can return `NOT_FOUND`, `INVALID_ARGUMENT`, and `PERMISSION_DENIED` errors.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/privateConnections/{privateConnectionsId}:setIamPolicy", "httpMethod": "POST", "id": "datamigration.projects.locations.privateConnections.setIamPolicy", "parameterOrder": ["resource"], "parameters": {"resource": {"description": "REQUIRED: The resource for which the policy is being specified. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/privateConnections/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+resource}:setIamPolicy", "request": {"$ref": "SetIamPolicyRequest"}, "response": {"$ref": "Policy"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "testIamPermissions": {"description": "Returns permissions that a caller has on the specified resource. If the resource does not exist, this will return an empty set of permissions, not a `NOT_FOUND` error. Note: This operation is designed to be used for building permission-aware UIs and command-line tools, not for authorization checking. This operation may \"fail open\" without warning.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/privateConnections/{privateConnectionsId}:testIamPermissions", "httpMethod": "POST", "id": "datamigration.projects.locations.privateConnections.testIamPermissions", "parameterOrder": ["resource"], "parameters": {"resource": {"description": "REQUIRED: The resource for which the policy detail is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/privateConnections/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+resource}:testIamPermissions", "request": {"$ref": "TestIamPermissionsRequest"}, "response": {"$ref": "TestIamPermissionsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}}}}}}, "revision": "20250521", "rootUrl": "https://datamigration.googleapis.com/", "schemas": {"AlloyDbConnectionProfile": {"description": "Specifies required connection parameters, and the parameters required to create an AlloyDB destination cluster.", "id": "AlloyDbConnectionProfile", "properties": {"clusterId": {"description": "Required. The AlloyDB cluster ID that this connection profile is associated with.", "type": "string"}, "settings": {"$ref": "AlloyDbSettings", "description": "Immutable. Metadata used to create the destination AlloyDB cluster."}}, "type": "object"}, "AlloyDbSettings": {"description": "Settings for creating an AlloyDB cluster.", "id": "AlloyDbSettings", "properties": {"databaseVersion": {"description": "Optional. The database engine major version. This is an optional field. If a database version is not supplied at cluster creation time, then a default database version will be used.", "enum": ["DATABASE_VERSION_UNSPECIFIED", "POSTGRES_14", "POSTGRES_15", "POSTGRES_16"], "enumDescriptions": ["This is an unknown database version.", "The database version is Postgres 14.", "The database version is Postgres 15.", "The database version is Postgres 16."], "type": "string"}, "encryptionConfig": {"$ref": "EncryptionConfig", "description": "Optional. The encryption config can be specified to encrypt the data disks and other persistent data resources of a cluster with a customer-managed encryption key (CMEK). When this field is not specified, the cluster will then use default encryption scheme to protect the user data."}, "initialUser": {"$ref": "UserPassword", "description": "Required. Input only. Initial user to setup during cluster creation. Required."}, "labels": {"additionalProperties": {"type": "string"}, "description": "Labels for the AlloyDB cluster created by DMS. An object containing a list of 'key', 'value' pairs.", "type": "object"}, "primaryInstanceSettings": {"$ref": "PrimaryInstanceSettings", "description": "Settings for the cluster's primary instance"}, "vpcNetwork": {"description": "Required. The resource link for the VPC network in which cluster resources are created and from which they are accessible via Private IP. The network must belong to the same project as the cluster. It is specified in the form: \"projects/{project_number}/global/networks/{network_id}\". This is required to create a cluster.", "type": "string"}}, "type": "object"}, "ApplyConversionWorkspaceRequest": {"description": "Request message for 'ApplyConversionWorkspace' request.", "id": "ApplyConversionWorkspaceRequest", "properties": {"autoCommit": {"description": "Optional. Specifies whether the conversion workspace is to be committed automatically after the apply.", "type": "boolean"}, "connectionProfile": {"description": "Optional. Fully qualified (Uri) name of the destination connection profile.", "type": "string"}, "dryRun": {"description": "Optional. Only validates the apply process, but doesn't change the destination database. Only works for PostgreSQL destination connection profile.", "type": "boolean"}, "filter": {"description": "Filter which entities to apply. Leaving this field empty will apply all of the entities. Supports Google AIP 160 based filtering.", "type": "string"}}, "type": "object"}, "ApplyHash": {"description": "Apply a hash function on the value.", "id": "ApplyHash", "properties": {"uuidFromBytes": {"$ref": "Empty", "description": "Optional. Generate UUID from the data's byte array"}}, "type": "object"}, "ApplyJobDetails": {"description": "Details regarding an Apply background job.", "id": "ApplyJobDetails", "properties": {"connectionProfile": {"description": "Output only. The connection profile which was used for the apply job.", "readOnly": true, "type": "string"}, "filter": {"description": "Output only. AIP-160 based filter used to specify the entities to apply", "readOnly": true, "type": "string"}}, "type": "object"}, "AssignSpecificValue": {"description": "Set to a specific value (value is converted to fit the target data type)", "id": "AssignSpecificValue", "properties": {"value": {"description": "Required. Specific value to be assigned", "type": "string"}}, "type": "object"}, "AuditConfig": {"description": "Specifies the audit configuration for a service. The configuration determines which permission types are logged, and what identities, if any, are exempted from logging. An AuditConfig must have one or more AuditLogConfigs. If there are AuditConfigs for both `allServices` and a specific service, the union of the two AuditConfigs is used for that service: the log_types specified in each AuditConfig are enabled, and the exempted_members in each AuditLogConfig are exempted. Example Policy with multiple AuditConfigs: { \"audit_configs\": [ { \"service\": \"allServices\", \"audit_log_configs\": [ { \"log_type\": \"DATA_READ\", \"exempted_members\": [ \"user:<EMAIL>\" ] }, { \"log_type\": \"DATA_WRITE\" }, { \"log_type\": \"ADMIN_READ\" } ] }, { \"service\": \"sampleservice.googleapis.com\", \"audit_log_configs\": [ { \"log_type\": \"DATA_READ\" }, { \"log_type\": \"DATA_WRITE\", \"exempted_members\": [ \"user:<EMAIL>\" ] } ] } ] } For sampleservice, this policy enables DATA_READ, DATA_WRITE and ADMIN_READ logging. It also exempts `<EMAIL>` from DATA_READ logging, and `<EMAIL>` from DATA_WRITE logging.", "id": "AuditConfig", "properties": {"auditLogConfigs": {"description": "The configuration for logging of each type of permission.", "items": {"$ref": "AuditLogConfig"}, "type": "array"}, "service": {"description": "Specifies a service that will be enabled for audit logging. For example, `storage.googleapis.com`, `cloudsql.googleapis.com`. `allServices` is a special value that covers all services.", "type": "string"}}, "type": "object"}, "AuditLogConfig": {"description": "Provides the configuration for logging a type of permissions. Example: { \"audit_log_configs\": [ { \"log_type\": \"DATA_READ\", \"exempted_members\": [ \"user:<EMAIL>\" ] }, { \"log_type\": \"DATA_WRITE\" } ] } This enables 'DATA_READ' and 'DATA_WRITE' logging, <NAME_EMAIL> from DATA_READ logging.", "id": "AuditLogConfig", "properties": {"exemptedMembers": {"description": "Specifies the identities that do not cause logging for this type of permission. Follows the same format of Binding.members.", "items": {"type": "string"}, "type": "array"}, "logType": {"description": "The log type that this config enables.", "enum": ["LOG_TYPE_UNSPECIFIED", "ADMIN_READ", "DATA_WRITE", "DATA_READ"], "enumDescriptions": ["Default case. Should never be this.", "Admin reads. Example: CloudIAM getIamPolicy", "Data writes. Example: CloudSQL Users create", "Data reads. Example: CloudSQL Users list"], "type": "string"}}, "type": "object"}, "AuthorizedNetwork": {"description": "AuthorizedNetwork contains metadata for an authorized network.", "id": "AuthorizedNetwork", "properties": {"cidrRange": {"description": "Optional. CIDR range for one authorzied network of the instance.", "type": "string"}}, "type": "object"}, "BackgroundJobLogEntry": {"description": "Execution log of a background job.", "id": "BackgroundJobLogEntry", "properties": {"applyJobDetails": {"$ref": "ApplyJobDetails", "description": "Output only. Apply job details.", "readOnly": true}, "completionComment": {"description": "Output only. Job completion comment, such as how many entities were seeded, how many warnings were found during conversion, and similar information.", "readOnly": true, "type": "string"}, "completionState": {"description": "Output only. Job completion state, i.e. the final state after the job completed.", "enum": ["JOB_COMPLETION_STATE_UNSPECIFIED", "SUCCEEDED", "FAILED"], "enumDescriptions": ["The status is not specified. This state is used when job is not yet finished.", "Success.", "Error."], "readOnly": true, "type": "string"}, "convertJobDetails": {"$ref": "ConvertJobDetails", "description": "Output only. Convert job details.", "readOnly": true}, "finishTime": {"description": "The timestamp when the background job was finished.", "format": "google-datetime", "type": "string"}, "id": {"description": "The background job log entry ID.", "type": "string"}, "importRulesJobDetails": {"$ref": "ImportRulesJobDetails", "description": "Output only. Import rules job details.", "readOnly": true}, "jobType": {"description": "The type of job that was executed.", "enum": ["BACKGROUND_JOB_TYPE_UNSPECIFIED", "BACKGROUND_JOB_TYPE_SOURCE_SEED", "BACKGROUND_JOB_TYPE_CONVERT", "BACKGROUND_JOB_TYPE_APPLY_DESTINATION", "BACKGROUND_JOB_TYPE_IMPORT_RULES_FILE"], "enumDescriptions": ["Unspecified background job type.", "Job to seed from the source database.", "Job to convert the source database into a draft of the destination database.", "Job to apply the draft tree onto the destination.", "Job to import and convert mapping rules from an external source such as an ora2pg config file."], "type": "string"}, "requestAutocommit": {"description": "Output only. Whether the client requested the conversion workspace to be committed after a successful completion of the job.", "readOnly": true, "type": "boolean"}, "seedJobDetails": {"$ref": "SeedJobDetails", "description": "Output only. Seed job details.", "readOnly": true}, "startTime": {"description": "The timestamp when the background job was started.", "format": "google-datetime", "type": "string"}}, "type": "object"}, "BinaryLogParser": {"description": "Configuration to use Binary Log Parser CDC technique.", "id": "BinaryLog<PERSON><PERSON>er", "properties": {"logFileDirectories": {"$ref": "LogFileDirectories", "description": "Use Oracle directories."}, "oracleAsmLogFileAccess": {"$ref": "OracleAsmLogFileAccess", "description": "Use Oracle ASM."}}, "type": "object"}, "Binding": {"description": "Associates `members`, or principals, with a `role`.", "id": "Binding", "properties": {"condition": {"$ref": "Expr", "description": "The condition that is associated with this binding. If the condition evaluates to `true`, then this binding applies to the current request. If the condition evaluates to `false`, then this binding does not apply to the current request. However, a different role binding might grant the same role to one or more of the principals in this binding. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies)."}, "members": {"description": "Specifies the principals requesting access for a Google Cloud resource. `members` can have the following values: * `allUsers`: A special identifier that represents anyone who is on the internet; with or without a Google account. * `allAuthenticatedUsers`: A special identifier that represents anyone who is authenticated with a Google account or a service account. Does not include identities that come from external identity providers (IdPs) through identity federation. * `user:{emailid}`: An email address that represents a specific Google account. For example, `<EMAIL>` . * `serviceAccount:{emailid}`: An email address that represents a Google service account. For example, `<EMAIL>`. * `serviceAccount:{projectid}.svc.id.goog[{namespace}/{kubernetes-sa}]`: An identifier for a [Kubernetes service account](https://cloud.google.com/kubernetes-engine/docs/how-to/kubernetes-service-accounts). For example, `my-project.svc.id.goog[my-namespace/my-kubernetes-sa]`. * `group:{emailid}`: An email address that represents a Google group. For example, `<EMAIL>`. * `domain:{domain}`: The G Suite domain (primary) that represents all the users of that domain. For example, `google.com` or `example.com`. * `principal://iam.googleapis.com/locations/global/workforcePools/{pool_id}/subject/{subject_attribute_value}`: A single identity in a workforce identity pool. * `principalSet://iam.googleapis.com/locations/global/workforcePools/{pool_id}/group/{group_id}`: All workforce identities in a group. * `principalSet://iam.googleapis.com/locations/global/workforcePools/{pool_id}/attribute.{attribute_name}/{attribute_value}`: All workforce identities with a specific attribute value. * `principalSet://iam.googleapis.com/locations/global/workforcePools/{pool_id}/*`: All identities in a workforce identity pool. * `principal://iam.googleapis.com/projects/{project_number}/locations/global/workloadIdentityPools/{pool_id}/subject/{subject_attribute_value}`: A single identity in a workload identity pool. * `principalSet://iam.googleapis.com/projects/{project_number}/locations/global/workloadIdentityPools/{pool_id}/group/{group_id}`: A workload identity pool group. * `principalSet://iam.googleapis.com/projects/{project_number}/locations/global/workloadIdentityPools/{pool_id}/attribute.{attribute_name}/{attribute_value}`: All identities in a workload identity pool with a certain attribute. * `principalSet://iam.googleapis.com/projects/{project_number}/locations/global/workloadIdentityPools/{pool_id}/*`: All identities in a workload identity pool. * `deleted:user:{emailid}?uid={uniqueid}`: An email address (plus unique identifier) representing a user that has been recently deleted. For example, `<EMAIL>?uid=123456789012345678901`. If the user is recovered, this value reverts to `user:{emailid}` and the recovered user retains the role in the binding. * `deleted:serviceAccount:{emailid}?uid={uniqueid}`: An email address (plus unique identifier) representing a service account that has been recently deleted. For example, `<EMAIL>?uid=123456789012345678901`. If the service account is undeleted, this value reverts to `serviceAccount:{emailid}` and the undeleted service account retains the role in the binding. * `deleted:group:{emailid}?uid={uniqueid}`: An email address (plus unique identifier) representing a Google group that has been recently deleted. For example, `<EMAIL>?uid=123456789012345678901`. If the group is recovered, this value reverts to `group:{emailid}` and the recovered group retains the role in the binding. * `deleted:principal://iam.googleapis.com/locations/global/workforcePools/{pool_id}/subject/{subject_attribute_value}`: Deleted single identity in a workforce identity pool. For example, `deleted:principal://iam.googleapis.com/locations/global/workforcePools/my-pool-id/subject/my-subject-attribute-value`.", "items": {"type": "string"}, "type": "array"}, "role": {"description": "Role that is assigned to the list of `members`, or principals. For example, `roles/viewer`, `roles/editor`, or `roles/owner`. For an overview of the IAM roles and permissions, see the [IAM documentation](https://cloud.google.com/iam/docs/roles-overview). For a list of the available pre-defined roles, see [here](https://cloud.google.com/iam/docs/understanding-roles).", "type": "string"}}, "type": "object"}, "CancelOperationRequest": {"description": "The request message for Operations.CancelOperation.", "id": "CancelOperationRequest", "properties": {}, "type": "object"}, "CloudSqlConnectionProfile": {"description": "Specifies required connection parameters, and, optionally, the parameters required to create a Cloud SQL destination database instance.", "id": "CloudSqlConnectionProfile", "properties": {"additionalPublicIp": {"description": "Output only. The Cloud SQL database instance's additional (outgoing) public IP. Used when the Cloud SQL database availability type is REGIONAL (i.e. multiple zones / highly available).", "readOnly": true, "type": "string"}, "cloudSqlId": {"description": "Output only. The Cloud SQL instance ID that this connection profile is associated with.", "readOnly": true, "type": "string"}, "privateIp": {"description": "Output only. The Cloud SQL database instance's private IP.", "readOnly": true, "type": "string"}, "publicIp": {"description": "Output only. The Cloud SQL database instance's public IP.", "readOnly": true, "type": "string"}, "settings": {"$ref": "CloudSqlSettings", "description": "Immutable. Metadata used to create the destination Cloud SQL database."}}, "type": "object"}, "CloudSqlSettings": {"description": "Settings for creating a Cloud SQL database instance.", "id": "CloudSqlSettings", "properties": {"activationPolicy": {"description": "The activation policy specifies when the instance is activated; it is applicable only when the instance state is 'RUNNABLE'. Valid values: 'ALWAYS': The instance is on, and remains so even in the absence of connection requests. `NEVER`: The instance is off; it is not activated, even if a connection request arrives.", "enum": ["SQL_ACTIVATION_POLICY_UNSPECIFIED", "ALWAYS", "NEVER"], "enumDescriptions": ["unspecified policy.", "The instance is always up and running.", "The instance should never spin up."], "type": "string"}, "autoStorageIncrease": {"description": "[default: ON] If you enable this setting, Cloud SQL checks your available storage every 30 seconds. If the available storage falls below a threshold size, Cloud SQL automatically adds additional storage capacity. If the available storage repeatedly falls below the threshold size, Cloud SQL continues to add storage until it reaches the maximum of 30 TB.", "type": "boolean"}, "availabilityType": {"description": "Optional. Availability type. Potential values: * `ZONAL`: The instance serves data from only one zone. Outages in that zone affect data availability. * `REGIONAL`: The instance can serve data from more than one zone in a region (it is highly available).", "enum": ["SQL_AVAILABILITY_TYPE_UNSPECIFIED", "ZONAL", "REGIONAL"], "enumDescriptions": ["This is an unknown Availability type.", "Zonal availablility instance.", "Regional availability instance."], "type": "string"}, "cmekKeyName": {"description": "The KMS key name used for the csql instance.", "type": "string"}, "collation": {"description": "The Cloud SQL default instance level collation.", "type": "string"}, "dataCacheConfig": {"$ref": "DataCacheConfig", "description": "Optional. Data cache is an optional feature available for Cloud SQL for MySQL Enterprise Plus edition only. For more information on data cache, see [Data cache overview](https://cloud.google.com/sql/help/mysql-data-cache) in Cloud SQL documentation."}, "dataDiskProvisionedIops": {"description": "Optional. Provisioned number of I/O operations per second for the data disk. This field is only used for hyperdisk-balanced disk types.", "format": "int64", "type": "string"}, "dataDiskProvisionedThroughput": {"description": "Optional. Provisioned throughput measured in MiB per second for the data disk. This field is only used for hyperdisk-balanced disk types.", "format": "int64", "type": "string"}, "dataDiskSizeGb": {"description": "The storage capacity available to the database, in GB. The minimum (and default) size is 10GB.", "format": "int64", "type": "string"}, "dataDiskType": {"description": "The type of storage: `PD_SSD` (default) or `PD_HDD` or `HYPERDISK_BALANCED`.", "enum": ["SQL_DATA_DISK_TYPE_UNSPECIFIED", "PD_SSD", "PD_HDD", "HYPERDISK_BALANCED"], "enumDescriptions": ["Unspecified.", "SSD disk.", "HDD disk.", "A Hyperdisk Balanced data disk."], "type": "string"}, "databaseFlags": {"additionalProperties": {"type": "string"}, "description": "The database flags passed to the Cloud SQL instance at startup. An object containing a list of \"key\": value pairs. Example: { \"name\": \"wrench\", \"mass\": \"1.3kg\", \"count\": \"3\" }.", "type": "object"}, "databaseVersion": {"description": "The database engine type and version. Deprecated. Use database_version_name instead.", "enum": ["SQL_DATABASE_VERSION_UNSPECIFIED", "MYSQL_5_6", "MYSQL_5_7", "MYSQL_8_0", "MYSQL_8_0_18", "MYSQL_8_0_26", "MYSQL_8_0_27", "MYSQL_8_0_28", "MYSQL_8_0_30", "MYSQL_8_0_31", "MYSQL_8_0_32", "MYSQL_8_0_33", "MYSQL_8_0_34", "MYSQL_8_0_35", "MYSQL_8_0_36", "MYSQL_8_0_37", "MYSQL_8_4", "POSTGRES_9_6", "POSTGRES_11", "POSTGRES_10", "POSTGRES_12", "POSTGRES_13", "POSTGRES_14", "POSTGRES_15", "POSTGRES_16"], "enumDescriptions": ["Unspecified version.", "MySQL 5.6.", "MySQL 5.7.", "MySQL 8.0.", "The database major version is MySQL 8.0 and the minor version is 18.", "The database major version is MySQL 8.0 and the minor version is 26.", "The database major version is MySQL 8.0 and the minor version is 27.", "The database major version is MySQL 8.0 and the minor version is 28.", "The database major version is MySQL 8.0 and the minor version is 30.", "The database major version is MySQL 8.0 and the minor version is 31.", "The database major version is MySQL 8.0 and the minor version is 32.", "The database major version is MySQL 8.0 and the minor version is 33.", "The database major version is MySQL 8.0 and the minor version is 34.", "The database major version is MySQL 8.0 and the minor version is 35.", "The database major version is MySQL 8.0 and the minor version is 36.", "The database major version is MySQL 8.0 and the minor version is 37.", "MySQL 8.4.", "PostgreSQL 9.6.", "PostgreSQL 11.", "PostgreSQL 10.", "PostgreSQL 12.", "PostgreSQL 13.", "PostgreSQL 14.", "PostgreSQL 15.", "PostgreSQL 16."], "type": "string"}, "databaseVersionName": {"description": "Optional. The database engine type and version name.", "type": "string"}, "edition": {"description": "Optional. The edition of the given Cloud SQL instance.", "enum": ["EDITION_UNSPECIFIED", "ENTERPRISE", "ENTERPRISE_PLUS"], "enumDescriptions": ["The instance did not specify the edition.", "The instance is an enterprise edition.", "The instance is an enterprise plus edition."], "type": "string"}, "ipConfig": {"$ref": "SqlIpConfig", "description": "The settings for IP Management. This allows to enable or disable the instance IP and manage which external networks can connect to the instance. The IPv4 address cannot be disabled."}, "rootPassword": {"description": "Input only. Initial root password.", "type": "string"}, "rootPasswordSet": {"description": "Output only. Indicates If this connection profile root password is stored.", "readOnly": true, "type": "boolean"}, "secondaryZone": {"description": "Optional. The Google Cloud Platform zone where the failover Cloud SQL database instance is located. Used when the Cloud SQL database availability type is REGIONAL (i.e. multiple zones / highly available).", "type": "string"}, "sourceId": {"description": "The Database Migration Service source connection profile ID, in the format: `projects/my_project_name/locations/us-central1/connectionProfiles/connection_profile_ID`", "type": "string"}, "storageAutoResizeLimit": {"description": "The maximum size to which storage capacity can be automatically increased. The default value is 0, which specifies that there is no limit.", "format": "int64", "type": "string"}, "tier": {"description": "The tier (or machine type) for this instance, for example: `db-n1-standard-1` (MySQL instances) or `db-custom-1-3840` (PostgreSQL instances). For more information, see [Cloud SQL Instance Settings](https://cloud.google.com/sql/docs/mysql/instance-settings).", "type": "string"}, "userLabels": {"additionalProperties": {"type": "string"}, "description": "The resource labels for a Cloud SQL instance to use to annotate any related underlying resources such as Compute Engine VMs. An object containing a list of \"key\": \"value\" pairs. Example: `{ \"name\": \"wrench\", \"mass\": \"18kg\", \"count\": \"3\" }`.", "type": "object"}, "zone": {"description": "The Google Cloud Platform zone where your Cloud SQL database instance is located.", "type": "string"}}, "type": "object"}, "ColumnEntity": {"description": "Column is not used as an independent entity, it is retrieved as part of a Table entity.", "id": "ColumnEntity", "properties": {"array": {"description": "Is the column of array type.", "type": "boolean"}, "arrayLength": {"description": "If the column is array, of which length.", "format": "int32", "type": "integer"}, "autoGenerated": {"description": "Is the column auto-generated/identity.", "type": "boolean"}, "charset": {"description": "Charset override - instead of table level charset.", "type": "string"}, "collation": {"description": "Collation override - instead of table level collation.", "type": "string"}, "comment": {"description": "Comment associated with the column.", "type": "string"}, "computed": {"description": "Is the column a computed column.", "type": "boolean"}, "customFeatures": {"additionalProperties": {"description": "Properties of the object.", "type": "any"}, "description": "Custom engine specific features.", "type": "object"}, "dataType": {"description": "Column data type.", "type": "string"}, "defaultValue": {"description": "Default value of the column.", "type": "string"}, "fractionalSecondsPrecision": {"description": "Column fractional second precision - used for timestamp based datatypes.", "format": "int32", "type": "integer"}, "length": {"description": "Column length - e.g. var<PERSON><PERSON> (50).", "format": "int64", "type": "string"}, "name": {"description": "Column name.", "type": "string"}, "nullable": {"description": "Is the column nullable.", "type": "boolean"}, "ordinalPosition": {"description": "Column order in the table.", "format": "int32", "type": "integer"}, "precision": {"description": "Column precision - when relevant.", "format": "int32", "type": "integer"}, "scale": {"description": "Column scale - when relevant.", "format": "int32", "type": "integer"}, "setValues": {"description": "Specifies the list of values allowed in the column. Only used for set data type.", "items": {"type": "string"}, "type": "array"}, "udt": {"description": "Is the column a UDT.", "type": "boolean"}}, "type": "object"}, "CommitConversionWorkspaceRequest": {"description": "Request message for 'CommitConversionWorkspace' request.", "id": "CommitConversionWorkspaceRequest", "properties": {"commitName": {"description": "Optional. Optional name of the commit.", "type": "string"}}, "type": "object"}, "ConditionalColumnSetValue": {"description": "Options to configure rule type ConditionalColumnSetValue. The rule is used to transform the data which is being replicated/migrated. The rule filter field can refer to one or more entities. The rule scope can be one of: Column.", "id": "ConditionalColumnSetValue", "properties": {"customFeatures": {"additionalProperties": {"description": "Properties of the object.", "type": "any"}, "description": "Optional. Custom engine specific features.", "type": "object"}, "sourceNumericFilter": {"$ref": "SourceNumericFilter", "description": "Optional. Optional filter on source column precision and scale. Used for fixed point numbers such as NUMERIC/NUMBER data types."}, "sourceTextFilter": {"$ref": "SourceTextFilter", "description": "Optional. Optional filter on source column length. Used for text based data types like varchar."}, "valueTransformation": {"$ref": "ValueTransformation", "description": "Required. Description of data transformation during migration."}}, "type": "object"}, "ConnectionProfile": {"description": "A connection profile definition.", "id": "ConnectionProfile", "properties": {"alloydb": {"$ref": "AlloyDbConnectionProfile", "description": "An AlloyDB cluster connection profile."}, "cloudsql": {"$ref": "CloudSqlConnectionProfile", "description": "A CloudSQL database connection profile."}, "createTime": {"description": "Output only. The timestamp when the resource was created. A timestamp in RFC3339 UTC \"Zulu\" format, accurate to nanoseconds. Example: \"2014-10-02T15:01:23.045123456Z\".", "format": "google-datetime", "readOnly": true, "type": "string"}, "displayName": {"description": "The connection profile display name.", "type": "string"}, "error": {"$ref": "Status", "description": "Output only. The error details in case of state FAILED.", "readOnly": true}, "labels": {"additionalProperties": {"type": "string"}, "description": "The resource labels for connection profile to use to annotate any related underlying resources such as Compute Engine VMs. An object containing a list of \"key\": \"value\" pairs. Example: `{ \"name\": \"wrench\", \"mass\": \"1.3kg\", \"count\": \"3\" }`.", "type": "object"}, "mysql": {"$ref": "MySqlConnectionProfile", "description": "A MySQL database connection profile."}, "name": {"description": "The name of this connection profile resource in the form of projects/{project}/locations/{location}/connectionProfiles/{connectionProfile}.", "type": "string"}, "oracle": {"$ref": "OracleConnectionProfile", "description": "An Oracle database connection profile."}, "postgresql": {"$ref": "PostgreSqlConnectionProfile", "description": "A PostgreSQL database connection profile."}, "provider": {"description": "The database provider.", "enum": ["DATABASE_PROVIDER_UNSPECIFIED", "CLOUDSQL", "RDS", "AURORA", "ALLOYDB", "AZURE_DATABASE"], "enumDescriptions": ["Use this value for on-premise source database instances and ORACLE.", "Cloud SQL is the source instance provider.", "Amazon RDS is the source instance provider.", "Amazon Aurora is the source instance provider.", "AlloyDB for PostgreSQL is the source instance provider.", "Microsoft Azure Database for MySQL/PostgreSQL."], "type": "string"}, "role": {"description": "Optional. The connection profile role.", "enum": ["ROLE_UNSPECIFIED", "SOURCE", "DESTINATION"], "enumDescriptions": ["The role is unspecified.", "The role is source.", "The role is destination."], "type": "string"}, "satisfiesPzi": {"description": "Output only. Reserved for future use.", "readOnly": true, "type": "boolean"}, "satisfiesPzs": {"description": "Output only. Reserved for future use.", "readOnly": true, "type": "boolean"}, "sqlserver": {"$ref": "SqlServerConnectionProfile", "description": "Connection profile for a SQL Server data source."}, "state": {"description": "The current connection profile state (e.g. DRAFT, READY, or FAILED).", "enum": ["STATE_UNSPECIFIED", "DRAFT", "CREATING", "READY", "UPDATING", "DELETING", "DELETED", "FAILED"], "enumDescriptions": ["The state of the connection profile is unknown.", "The connection profile is in draft mode and fully editable.", "The connection profile is being created.", "The connection profile is ready.", "The connection profile is being updated.", "The connection profile is being deleted.", "The connection profile has been deleted.", "The last action on the connection profile failed."], "type": "string"}, "updateTime": {"description": "Output only. The timestamp when the resource was last updated. A timestamp in RFC3339 UTC \"Zulu\" format, accurate to nanoseconds. Example: \"2014-10-02T15:01:23.045123456Z\".", "format": "google-datetime", "readOnly": true, "type": "string"}}, "type": "object"}, "ConstraintEntity": {"description": "Constraint is not used as an independent entity, it is retrieved as part of another entity such as Table or View.", "id": "ConstraintEntity", "properties": {"customFeatures": {"additionalProperties": {"description": "Properties of the object.", "type": "any"}, "description": "Custom engine specific features.", "type": "object"}, "name": {"description": "The name of the table constraint.", "type": "string"}, "referenceColumns": {"description": "Reference columns which may be associated with the constraint. For example, if the constraint is a FOREIGN_KEY, this represents the list of full names of referenced columns by the foreign key.", "items": {"type": "string"}, "type": "array"}, "referenceTable": {"description": "Reference table which may be associated with the constraint. For example, if the constraint is a FOREIGN_KEY, this represents the list of full name of the referenced table by the foreign key.", "type": "string"}, "tableColumns": {"description": "Table columns used as part of the Constraint, for example primary key constraint should list the columns which constitutes the key.", "items": {"type": "string"}, "type": "array"}, "tableName": {"description": "Table which is associated with the constraint. In case the constraint is defined on a table, this field is left empty as this information is stored in parent_name. However, if constraint is defined on a view, this field stores the table name on which the view is defined.", "type": "string"}, "type": {"description": "Type of constraint, for example unique, primary key, foreign key (currently only primary key is supported).", "type": "string"}}, "type": "object"}, "ConversionWorkspace": {"description": "The main conversion workspace resource entity.", "id": "ConversionWorkspace", "properties": {"createTime": {"description": "Output only. The timestamp when the workspace resource was created.", "format": "google-datetime", "readOnly": true, "type": "string"}, "destination": {"$ref": "DatabaseEngineInfo", "description": "Required. The destination engine details."}, "destinationProvider": {"description": "Optional. The provider for the destination database.", "enum": ["DATABASE_PROVIDER_UNSPECIFIED", "CLOUDSQL", "RDS", "AURORA", "ALLOYDB", "AZURE_DATABASE"], "enumDescriptions": ["Use this value for on-premise source database instances and ORACLE.", "Cloud SQL is the source instance provider.", "Amazon RDS is the source instance provider.", "Amazon Aurora is the source instance provider.", "AlloyDB for PostgreSQL is the source instance provider.", "Microsoft Azure Database for MySQL/PostgreSQL."], "type": "string"}, "displayName": {"description": "Optional. The display name for the workspace.", "type": "string"}, "globalSettings": {"additionalProperties": {"type": "string"}, "description": "Optional. A generic list of settings for the workspace. The settings are database pair dependant and can indicate default behavior for the mapping rules engine or turn on or off specific features. Such examples can be: convert_foreign_key_to_interleave=true, skip_triggers=false, ignore_non_table_synonyms=true", "type": "object"}, "hasUncommittedChanges": {"description": "Output only. Whether the workspace has uncommitted changes (changes which were made after the workspace was committed).", "readOnly": true, "type": "boolean"}, "latestCommitId": {"description": "Output only. The latest commit ID.", "readOnly": true, "type": "string"}, "latestCommitTime": {"description": "Output only. The timestamp when the workspace was committed.", "format": "google-datetime", "readOnly": true, "type": "string"}, "name": {"description": "Full name of the workspace resource, in the form of: projects/{project}/locations/{location}/conversionWorkspaces/{conversion_workspace}.", "type": "string"}, "source": {"$ref": "DatabaseEngineInfo", "description": "Required. The source engine details."}, "sourceProvider": {"description": "Optional. The provider for the source database.", "enum": ["DATABASE_PROVIDER_UNSPECIFIED", "CLOUDSQL", "RDS", "AURORA", "ALLOYDB", "AZURE_DATABASE"], "enumDescriptions": ["Use this value for on-premise source database instances and ORACLE.", "Cloud SQL is the source instance provider.", "Amazon RDS is the source instance provider.", "Amazon Aurora is the source instance provider.", "AlloyDB for PostgreSQL is the source instance provider.", "Microsoft Azure Database for MySQL/PostgreSQL."], "type": "string"}, "updateTime": {"description": "Output only. The timestamp when the workspace resource was last updated.", "format": "google-datetime", "readOnly": true, "type": "string"}}, "type": "object"}, "ConversionWorkspaceInfo": {"description": "A conversion workspace's version.", "id": "ConversionWorkspaceInfo", "properties": {"commitId": {"description": "The commit ID of the conversion workspace.", "type": "string"}, "name": {"description": "The resource name (URI) of the conversion workspace.", "type": "string"}}, "type": "object"}, "ConvertConversionWorkspaceRequest": {"description": "Request message for 'ConvertConversionWorkspace' request.", "id": "ConvertConversionWorkspaceRequest", "properties": {"autoCommit": {"description": "Optional. Specifies whether the conversion workspace is to be committed automatically after the conversion.", "type": "boolean"}, "convertFullPath": {"description": "Optional. Automatically convert the full entity path for each entity specified by the filter. For example, if the filter specifies a table, that table schema (and database if there is one) will also be converted.", "type": "boolean"}, "filter": {"description": "Optional. Filter the entities to convert. Leaving this field empty will convert all of the entities. Supports Google AIP-160 style filtering.", "type": "string"}}, "type": "object"}, "ConvertJobDetails": {"description": "Details regarding a Convert background job.", "id": "ConvertJobDetails", "properties": {"filter": {"description": "Output only. AIP-160 based filter used to specify the entities to convert", "readOnly": true, "type": "string"}}, "type": "object"}, "ConvertRowIdToColumn": {"description": "Options to configure rule type ConvertROWIDToColumn. The rule is used to add column rowid to destination tables based on an Oracle rowid function/property. The rule filter field can refer to one or more entities. The rule scope can be one of: Table. This rule requires additional filter to be specified beyond the basic rule filter field, which is whether or not to work on tables which already have a primary key defined.", "id": "ConvertRowIdToColumn", "properties": {"onlyIfNoPrimaryKey": {"description": "Required. Only work on tables without primary key defined", "type": "boolean"}}, "type": "object"}, "DataCacheConfig": {"description": "Data cache is an optional feature available for Cloud SQL for MySQL Enterprise Plus edition only. For more information on data cache, see [Data cache overview](https://cloud.google.com/sql/help/mysql-data-cache) in Cloud SQL documentation.", "id": "DataCacheConfig", "properties": {"dataCacheEnabled": {"description": "Optional. Whether data cache is enabled for the instance.", "type": "boolean"}}, "type": "object"}, "DatabaseEngineInfo": {"description": "The type and version of a source or destination database.", "id": "DatabaseEngineInfo", "properties": {"engine": {"description": "Required. Engine type.", "enum": ["DATABASE_ENGINE_UNSPECIFIED", "MYSQL", "POSTGRESQL", "SQLSERVER", "ORACLE"], "enumDescriptions": ["The source database engine of the migration job is unknown.", "The source engine is MySQL.", "The source engine is PostgreSQL.", "The source engine is SQL Server.", "The source engine is Oracle."], "type": "string"}, "version": {"description": "Required. Engine version, for example \"12.c.1\".", "type": "string"}}, "type": "object"}, "DatabaseEntity": {"description": "The base entity type for all the database related entities. The message contains the entity name, the name of its parent, the entity type, and the specific details per entity type.", "id": "DatabaseEntity", "properties": {"database": {"$ref": "DatabaseInstanceEntity", "description": "Database."}, "databaseFunction": {"$ref": "FunctionEntity", "description": "Function."}, "databasePackage": {"$ref": "PackageEntity", "description": "Package."}, "entityDdl": {"description": "Details about the entity DDL script. Multiple DDL scripts are provided for child entities such as a table entity will have one DDL for the table with additional DDLs for each index, constraint and such.", "items": {"$ref": "EntityDdl"}, "type": "array"}, "entityType": {"description": "The type of the database entity (table, view, index, ...).", "enum": ["DATABASE_ENTITY_TYPE_UNSPECIFIED", "DATABASE_ENTITY_TYPE_SCHEMA", "DATABASE_ENTITY_TYPE_TABLE", "DATABASE_ENTITY_TYPE_COLUMN", "DATABASE_ENTITY_TYPE_CONSTRAINT", "DATABASE_ENTITY_TYPE_INDEX", "DATABASE_ENTITY_TYPE_TRIGGER", "DATABASE_ENTITY_TYPE_VIEW", "DATABASE_ENTITY_TYPE_SEQUENCE", "DATABASE_ENTITY_TYPE_STORED_PROCEDURE", "DATABASE_ENTITY_TYPE_FUNCTION", "DATABASE_ENTITY_TYPE_SYNONYM", "DATABASE_ENTITY_TYPE_DATABASE_PACKAGE", "DATABASE_ENTITY_TYPE_UDT", "DATABASE_ENTITY_TYPE_MATERIALIZED_VIEW", "DATABASE_ENTITY_TYPE_DATABASE"], "enumDescriptions": ["Unspecified database entity type.", "Schema.", "Table.", "Column.", "Constraint.", "Index.", "Trigger.", "View.", "Sequence.", "Stored Procedure.", "Function.", "Synonym.", "Package.", "UDT.", "Materialized View.", "Database."], "type": "string"}, "issues": {"description": "Details about the various issues found for the entity.", "items": {"$ref": "EntityIssue"}, "type": "array"}, "mappings": {"description": "Details about entity mappings. For source tree entities, this holds the draft entities which were generated by the mapping rules. For draft tree entities, this holds the source entities which were converted to form the draft entity. Destination entities will have no mapping details.", "items": {"$ref": "EntityMapping"}, "type": "array"}, "materializedView": {"$ref": "MaterializedViewEntity", "description": "Materialized view."}, "parentEntity": {"description": "The full name of the parent entity (e.g. schema name).", "type": "string"}, "schema": {"$ref": "SchemaEntity", "description": "Schema."}, "sequence": {"$ref": "SequenceEntity", "description": "Sequence."}, "shortName": {"description": "The short name (e.g. table name) of the entity.", "type": "string"}, "storedProcedure": {"$ref": "StoredProcedureEntity", "description": "Stored procedure."}, "synonym": {"$ref": "SynonymEntity", "description": "Synonym."}, "table": {"$ref": "TableEntity", "description": "Table."}, "tree": {"description": "The type of tree the entity belongs to.", "enum": ["TREE_TYPE_UNSPECIFIED", "SOURCE", "DRAFT", "DESTINATION"], "enumDescriptions": ["Tree type unspecified.", "Tree of entities loaded from a source database.", "Tree of entities converted from the source tree using the mapping rules.", "Tree of entities observed on the destination database."], "type": "string"}, "udt": {"$ref": "UDTEntity", "description": "UDT."}, "view": {"$ref": "ViewEntity", "description": "View."}}, "type": "object"}, "DatabaseInstanceEntity": {"description": "DatabaseInstance acts as a parent entity to other database entities.", "id": "DatabaseInstanceEntity", "properties": {"customFeatures": {"additionalProperties": {"description": "Properties of the object.", "type": "any"}, "description": "Custom engine specific features.", "type": "object"}}, "type": "object"}, "DatabaseType": {"description": "A message defining the database engine and provider.", "id": "DatabaseType", "properties": {"engine": {"description": "The database engine.", "enum": ["DATABASE_ENGINE_UNSPECIFIED", "MYSQL", "POSTGRESQL", "SQLSERVER", "ORACLE"], "enumDescriptions": ["The source database engine of the migration job is unknown.", "The source engine is MySQL.", "The source engine is PostgreSQL.", "The source engine is SQL Server.", "The source engine is Oracle."], "type": "string"}, "provider": {"description": "The database provider.", "enum": ["DATABASE_PROVIDER_UNSPECIFIED", "CLOUDSQL", "RDS", "AURORA", "ALLOYDB", "AZURE_DATABASE"], "enumDescriptions": ["Use this value for on-premise source database instances and ORACLE.", "Cloud SQL is the source instance provider.", "Amazon RDS is the source instance provider.", "Amazon Aurora is the source instance provider.", "AlloyDB for PostgreSQL is the source instance provider.", "Microsoft Azure Database for MySQL/PostgreSQL."], "type": "string"}}, "type": "object"}, "DemoteDestinationRequest": {"description": "Request message for 'DemoteDestination' request.", "id": "DemoteDestinationRequest", "properties": {}, "type": "object"}, "DescribeConversionWorkspaceRevisionsResponse": {"description": "Response message for 'DescribeConversionWorkspaceRevisions' request.", "id": "DescribeConversionWorkspaceRevisionsResponse", "properties": {"revisions": {"description": "The list of conversion workspace revisions.", "items": {"$ref": "ConversionWorkspace"}, "type": "array"}}, "type": "object"}, "DescribeDatabaseEntitiesResponse": {"description": "Response message for 'DescribeDatabaseEntities' request.", "id": "DescribeDatabaseEntitiesResponse", "properties": {"databaseEntities": {"description": "The list of database entities for the conversion workspace.", "items": {"$ref": "DatabaseEntity"}, "type": "array"}, "nextPageToken": {"description": "A token which can be sent as `page_token` to retrieve the next page. If this field is omitted, there are no subsequent pages.", "type": "string"}}, "type": "object"}, "DoubleComparisonFilter": {"description": "Filter based on relation between source value and compare value of type double in ConditionalColumnSetValue", "id": "DoubleComparisonFilter", "properties": {"value": {"description": "Required. Double compare value to be used", "format": "double", "type": "number"}, "valueComparison": {"description": "Required. Relation between source value and compare value", "enum": ["VALUE_COMPARISON_UNSPECIFIED", "VALUE_COMPARISON_IF_VALUE_SMALLER_THAN", "VALUE_COMPARISON_IF_VALUE_SMALLER_EQUAL_THAN", "VALUE_COMPARISON_IF_VALUE_LARGER_THAN", "VALUE_COMPARISON_IF_VALUE_LARGER_EQUAL_THAN"], "enumDescriptions": ["Value comparison unspecified.", "Value is smaller than the Compare value.", "Value is smaller or equal than the Compare value.", "Value is larger than the Compare value.", "Value is larger or equal than the Compare value."], "type": "string"}}, "type": "object"}, "DumpFlag": {"description": "Dump flag definition.", "id": "DumpFlag", "properties": {"name": {"description": "The name of the flag", "type": "string"}, "value": {"description": "The value of the flag.", "type": "string"}}, "type": "object"}, "DumpFlags": {"description": "Dump flags definition.", "id": "DumpFlags", "properties": {"dumpFlags": {"description": "The flags for the initial dump.", "items": {"$ref": "DumpFlag"}, "type": "array"}}, "type": "object"}, "Empty": {"description": "A generic empty message that you can re-use to avoid defining duplicated empty messages in your APIs. A typical example is to use it as the request or the response type of an API method. For instance: service Foo { rpc Bar(google.protobuf.Empty) returns (google.protobuf.Empty); }", "id": "Empty", "properties": {}, "type": "object"}, "EncryptionConfig": {"description": "EncryptionConfig describes the encryption config of a cluster that is encrypted with a CMEK (customer-managed encryption key).", "id": "EncryptionConfig", "properties": {"kmsKeyName": {"description": "The fully-qualified resource name of the KMS key. Each Cloud KMS key is regionalized and has the following format: projects/[PROJECT]/locations/[REGION]/keyRings/[RING]/cryptoKeys/[KEY_NAME]", "type": "string"}}, "type": "object"}, "EntityDdl": {"description": "A single DDL statement for a specific entity", "id": "EntityDdl", "properties": {"ddl": {"description": "The actual ddl code.", "type": "string"}, "ddlKind": {"description": "The DDL Kind selected for apply, or UNSPECIFIED if the entity wasn't converted yet.", "enum": ["DDL_KIND_UNSPECIFIED", "SOURCE", "DETERMINISTIC", "AI", "USER_EDIT"], "enumDescriptions": ["The kind of the DDL is unknown.", "DDL of the source entity", "Deterministic converted DDL", "Gemini AI converted DDL", "User edited DDL"], "type": "string"}, "ddlType": {"description": "Type of DDL (<PERSON><PERSON>, Alter).", "type": "string"}, "editedDdlKind": {"description": "If ddl_kind is USER_EDIT, this holds the DDL kind of the original content - DETERMINISTIC or AI. Otherwise, this is DDL_KIND_UNSPECIFIED.", "enum": ["DDL_KIND_UNSPECIFIED", "SOURCE", "DETERMINISTIC", "AI", "USER_EDIT"], "enumDescriptions": ["The kind of the DDL is unknown.", "DDL of the source entity", "Deterministic converted DDL", "Gemini AI converted DDL", "User edited DDL"], "type": "string"}, "entity": {"description": "The name of the database entity the ddl refers to.", "type": "string"}, "entityType": {"description": "The entity type (if the DDL is for a sub entity).", "enum": ["DATABASE_ENTITY_TYPE_UNSPECIFIED", "DATABASE_ENTITY_TYPE_SCHEMA", "DATABASE_ENTITY_TYPE_TABLE", "DATABASE_ENTITY_TYPE_COLUMN", "DATABASE_ENTITY_TYPE_CONSTRAINT", "DATABASE_ENTITY_TYPE_INDEX", "DATABASE_ENTITY_TYPE_TRIGGER", "DATABASE_ENTITY_TYPE_VIEW", "DATABASE_ENTITY_TYPE_SEQUENCE", "DATABASE_ENTITY_TYPE_STORED_PROCEDURE", "DATABASE_ENTITY_TYPE_FUNCTION", "DATABASE_ENTITY_TYPE_SYNONYM", "DATABASE_ENTITY_TYPE_DATABASE_PACKAGE", "DATABASE_ENTITY_TYPE_UDT", "DATABASE_ENTITY_TYPE_MATERIALIZED_VIEW", "DATABASE_ENTITY_TYPE_DATABASE"], "enumDescriptions": ["Unspecified database entity type.", "Schema.", "Table.", "Column.", "Constraint.", "Index.", "Trigger.", "View.", "Sequence.", "Stored Procedure.", "Function.", "Synonym.", "Package.", "UDT.", "Materialized View.", "Database."], "type": "string"}, "issueId": {"description": "EntityIssues found for this ddl.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "EntityIssue": {"description": "Issue related to the entity.", "id": "EntityIssue", "properties": {"code": {"description": "Error/Warning code", "type": "string"}, "ddl": {"description": "The ddl which caused the issue, if relevant.", "type": "string"}, "entityType": {"description": "The entity type (if the DDL is for a sub entity).", "enum": ["DATABASE_ENTITY_TYPE_UNSPECIFIED", "DATABASE_ENTITY_TYPE_SCHEMA", "DATABASE_ENTITY_TYPE_TABLE", "DATABASE_ENTITY_TYPE_COLUMN", "DATABASE_ENTITY_TYPE_CONSTRAINT", "DATABASE_ENTITY_TYPE_INDEX", "DATABASE_ENTITY_TYPE_TRIGGER", "DATABASE_ENTITY_TYPE_VIEW", "DATABASE_ENTITY_TYPE_SEQUENCE", "DATABASE_ENTITY_TYPE_STORED_PROCEDURE", "DATABASE_ENTITY_TYPE_FUNCTION", "DATABASE_ENTITY_TYPE_SYNONYM", "DATABASE_ENTITY_TYPE_DATABASE_PACKAGE", "DATABASE_ENTITY_TYPE_UDT", "DATABASE_ENTITY_TYPE_MATERIALIZED_VIEW", "DATABASE_ENTITY_TYPE_DATABASE"], "enumDescriptions": ["Unspecified database entity type.", "Schema.", "Table.", "Column.", "Constraint.", "Index.", "Trigger.", "View.", "Sequence.", "Stored Procedure.", "Function.", "Synonym.", "Package.", "UDT.", "Materialized View.", "Database."], "type": "string"}, "id": {"description": "Unique Issue ID.", "type": "string"}, "message": {"description": "Issue detailed message", "type": "string"}, "position": {"$ref": "Position", "description": "The position of the issue found, if relevant."}, "severity": {"description": "Severity of the issue", "enum": ["ISSUE_SEVERITY_UNSPECIFIED", "ISSUE_SEVERITY_INFO", "ISSUE_SEVERITY_WARNING", "ISSUE_SEVERITY_ERROR"], "enumDescriptions": ["Unspecified issue severity", "Info", "Warning", "Error"], "type": "string"}, "type": {"description": "The type of the issue.", "enum": ["ISSUE_TYPE_UNSPECIFIED", "ISSUE_TYPE_DDL", "ISSUE_TYPE_APPLY", "ISSUE_TYPE_CONVERT"], "enumDescriptions": ["Unspecified issue type.", "Issue originated from the DDL", "Issue originated during the apply process", "Issue originated during the convert process"], "type": "string"}}, "type": "object"}, "EntityMapping": {"description": "Details of the mappings of a database entity.", "id": "EntityMapping", "properties": {"draftEntity": {"description": "Target entity full name. The draft entity can also include a column, index or constraint using the same naming notation schema.table.column.", "type": "string"}, "draftType": {"description": "Type of draft entity.", "enum": ["DATABASE_ENTITY_TYPE_UNSPECIFIED", "DATABASE_ENTITY_TYPE_SCHEMA", "DATABASE_ENTITY_TYPE_TABLE", "DATABASE_ENTITY_TYPE_COLUMN", "DATABASE_ENTITY_TYPE_CONSTRAINT", "DATABASE_ENTITY_TYPE_INDEX", "DATABASE_ENTITY_TYPE_TRIGGER", "DATABASE_ENTITY_TYPE_VIEW", "DATABASE_ENTITY_TYPE_SEQUENCE", "DATABASE_ENTITY_TYPE_STORED_PROCEDURE", "DATABASE_ENTITY_TYPE_FUNCTION", "DATABASE_ENTITY_TYPE_SYNONYM", "DATABASE_ENTITY_TYPE_DATABASE_PACKAGE", "DATABASE_ENTITY_TYPE_UDT", "DATABASE_ENTITY_TYPE_MATERIALIZED_VIEW", "DATABASE_ENTITY_TYPE_DATABASE"], "enumDescriptions": ["Unspecified database entity type.", "Schema.", "Table.", "Column.", "Constraint.", "Index.", "Trigger.", "View.", "Sequence.", "Stored Procedure.", "Function.", "Synonym.", "Package.", "UDT.", "Materialized View.", "Database."], "type": "string"}, "mappingLog": {"description": "Entity mapping log entries. Multiple rules can be effective and contribute changes to a converted entity, such as a rule can handle the entity name, another rule can handle an entity type. In addition, rules which did not change the entity are also logged along with the reason preventing them to do so.", "items": {"$ref": "EntityMappingLogEntry"}, "type": "array"}, "sourceEntity": {"description": "Source entity full name. The source entity can also be a column, index or constraint using the same naming notation schema.table.column.", "type": "string"}, "sourceType": {"description": "Type of source entity.", "enum": ["DATABASE_ENTITY_TYPE_UNSPECIFIED", "DATABASE_ENTITY_TYPE_SCHEMA", "DATABASE_ENTITY_TYPE_TABLE", "DATABASE_ENTITY_TYPE_COLUMN", "DATABASE_ENTITY_TYPE_CONSTRAINT", "DATABASE_ENTITY_TYPE_INDEX", "DATABASE_ENTITY_TYPE_TRIGGER", "DATABASE_ENTITY_TYPE_VIEW", "DATABASE_ENTITY_TYPE_SEQUENCE", "DATABASE_ENTITY_TYPE_STORED_PROCEDURE", "DATABASE_ENTITY_TYPE_FUNCTION", "DATABASE_ENTITY_TYPE_SYNONYM", "DATABASE_ENTITY_TYPE_DATABASE_PACKAGE", "DATABASE_ENTITY_TYPE_UDT", "DATABASE_ENTITY_TYPE_MATERIALIZED_VIEW", "DATABASE_ENTITY_TYPE_DATABASE"], "enumDescriptions": ["Unspecified database entity type.", "Schema.", "Table.", "Column.", "Constraint.", "Index.", "Trigger.", "View.", "Sequence.", "Stored Procedure.", "Function.", "Synonym.", "Package.", "UDT.", "Materialized View.", "Database."], "type": "string"}}, "type": "object"}, "EntityMappingLogEntry": {"description": "A single record of a rule which was used for a mapping.", "id": "EntityMappingLogEntry", "properties": {"mappingComment": {"description": "Comment.", "type": "string"}, "ruleId": {"description": "Which rule caused this log entry.", "type": "string"}, "ruleRevisionId": {"description": "Rule revision ID.", "type": "string"}}, "type": "object"}, "EntityMove": {"description": "Options to configure rule type EntityMove. The rule is used to move an entity to a new schema. The rule filter field can refer to one or more entities. The rule scope can be one of: Table, Column, Constraint, Index, View, Function, Stored Procedure, Materialized View, Sequence, UDT", "id": "EntityMove", "properties": {"newSchema": {"description": "Required. The new schema", "type": "string"}}, "type": "object"}, "Expr": {"description": "Represents a textual expression in the Common Expression Language (CEL) syntax. CEL is a C-like expression language. The syntax and semantics of CEL are documented at https://github.com/google/cel-spec. Example (Comparison): title: \"Summary size limit\" description: \"Determines if a summary is less than 100 chars\" expression: \"document.summary.size() < 100\" Example (Equality): title: \"Requestor is owner\" description: \"Determines if requestor is the document owner\" expression: \"document.owner == request.auth.claims.email\" Example (Logic): title: \"Public documents\" description: \"Determine whether the document should be publicly visible\" expression: \"document.type != 'private' && document.type != 'internal'\" Example (Data Manipulation): title: \"Notification string\" description: \"Create a notification string with a timestamp.\" expression: \"'New message received at ' + string(document.create_time)\" The exact variables and functions that may be referenced within an expression are determined by the service that evaluates it. See the service documentation for additional information.", "id": "Expr", "properties": {"description": {"description": "Optional. Description of the expression. This is a longer text which describes the expression, e.g. when hovered over it in a UI.", "type": "string"}, "expression": {"description": "Textual representation of an expression in Common Expression Language syntax.", "type": "string"}, "location": {"description": "Optional. String indicating the location of the expression for error reporting, e.g. a file name and a position in the file.", "type": "string"}, "title": {"description": "Optional. Title for the expression, i.e. a short string describing its purpose. This can be used e.g. in UIs which allow to enter the expression.", "type": "string"}}, "type": "object"}, "FetchStaticIpsResponse": {"description": "Response message for a 'FetchStaticIps' request.", "id": "FetchStaticIpsResponse", "properties": {"nextPageToken": {"description": "A token that can be sent as `page_token` to retrieve the next page. If this field is omitted, there are no subsequent pages.", "type": "string"}, "staticIps": {"description": "List of static IPs.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "FilterTableColumns": {"description": "Options to configure rule type FilterTableColumns. The rule is used to filter the list of columns to include or exclude from a table. The rule filter field can refer to one entity. The rule scope can be: Table Only one of the two lists can be specified for the rule.", "id": "FilterTableColumns", "properties": {"excludeColumns": {"description": "Optional. List of columns to be excluded for a particular table.", "items": {"type": "string"}, "type": "array"}, "includeColumns": {"description": "Optional. List of columns to be included for a particular table.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "ForwardSshTunnelConnectivity": {"description": "Forward SSH Tunnel connectivity.", "id": "ForwardSshTunnelConnectivity", "properties": {"hostname": {"description": "Required. Hostname for the SSH tunnel.", "type": "string"}, "password": {"description": "Input only. SSH password.", "type": "string"}, "port": {"description": "Port for the SSH tunnel, default value is 22.", "format": "int32", "type": "integer"}, "privateKey": {"description": "Input only. SSH private key.", "type": "string"}, "username": {"description": "Required. Username for the SSH tunnel.", "type": "string"}}, "type": "object"}, "FunctionEntity": {"description": "Function's parent is a schema.", "id": "FunctionEntity", "properties": {"customFeatures": {"additionalProperties": {"description": "Properties of the object.", "type": "any"}, "description": "Custom engine specific features.", "type": "object"}, "sqlCode": {"description": "The SQL code which creates the function.", "type": "string"}}, "type": "object"}, "GenerateSshScriptRequest": {"description": "Request message for 'GenerateSshScript' request.", "id": "GenerateSshScriptRequest", "properties": {"vm": {"description": "Required. Bastion VM Instance name to use or to create.", "type": "string"}, "vmCreationConfig": {"$ref": "VmCreationConfig", "description": "The VM creation configuration"}, "vmPort": {"description": "The port that will be open on the bastion host.", "format": "int32", "type": "integer"}, "vmSelectionConfig": {"$ref": "VmSelectionConfig", "description": "The VM selection configuration"}}, "type": "object"}, "GenerateTcpProxyScriptRequest": {"description": "Request message for 'GenerateTcpProxyScript' request.", "id": "GenerateTcpProxyScriptRequest", "properties": {"vmMachineType": {"description": "Required. The type of the Compute instance that will host the proxy.", "type": "string"}, "vmName": {"description": "Required. The name of the Compute instance that will host the proxy.", "type": "string"}, "vmSubnet": {"description": "Required. The name of the subnet the Compute instance will use for private connectivity. Must be supplied in the form of projects/{project}/regions/{region}/subnetworks/{subnetwork}. Note: the region for the subnet must match the Compute instance region.", "type": "string"}, "vmZone": {"description": "Optional. The Google Cloud Platform zone to create the VM in. The fully qualified name of the zone must be specified, including the region name, for example \"us-central1-b\". If not specified, uses the \"-b\" zone of the destination Connection Profile's region.", "type": "string"}}, "type": "object"}, "GoogleCloudClouddmsV1OperationMetadata": {"description": "Represents the metadata of the long-running operation.", "id": "GoogleCloudClouddmsV1OperationMetadata", "properties": {"apiVersion": {"description": "Output only. API version used to start the operation.", "readOnly": true, "type": "string"}, "createTime": {"description": "Output only. The time the operation was created.", "format": "google-datetime", "readOnly": true, "type": "string"}, "endTime": {"description": "Output only. The time the operation finished running.", "format": "google-datetime", "readOnly": true, "type": "string"}, "metadata": {"additionalProperties": {"type": "string"}, "description": "Output only. Additional metadata that is returned by the backend for the operation.", "readOnly": true, "type": "object"}, "requestedCancellation": {"description": "Output only. Identifies whether the user has requested cancellation of the operation. Operations that have successfully been cancelled have google.longrunning.Operation.error value with a google.rpc.Status.code of 1, corresponding to `Code.CANCELLED`.", "readOnly": true, "type": "boolean"}, "statusMessage": {"description": "Output only. Human-readable status of the operation, if any.", "readOnly": true, "type": "string"}, "target": {"description": "Output only. Server-defined resource path for the target of the operation.", "readOnly": true, "type": "string"}, "verb": {"description": "Output only. Name of the verb executed by the operation.", "readOnly": true, "type": "string"}}, "type": "object"}, "ImportMappingRulesRequest": {"description": "Request message for 'ImportMappingRules' request.", "id": "ImportMappingRulesRequest", "properties": {"autoCommit": {"description": "Required. Should the conversion workspace be committed automatically after the import operation.", "type": "boolean"}, "rulesFiles": {"description": "Required. One or more rules files.", "items": {"$ref": "RulesFile"}, "type": "array"}, "rulesFormat": {"description": "Required. The format of the rules content file.", "enum": ["IMPORT_RULES_FILE_FORMAT_UNSPECIFIED", "IMPORT_RULES_FILE_FORMAT_HARBOUR_BRIDGE_SESSION_FILE", "IMPORT_RULES_FILE_FORMAT_ORATOPG_CONFIG_FILE"], "enumDescriptions": ["Unspecified rules format.", "HarbourBridge session file.", "Ora2Pg configuration file."], "type": "string"}}, "type": "object"}, "ImportRulesJobDetails": {"description": "Details regarding an Import Rules background job.", "id": "ImportRulesJobDetails", "properties": {"fileFormat": {"description": "Output only. The requested file format.", "enum": ["IMPORT_RULES_FILE_FORMAT_UNSPECIFIED", "IMPORT_RULES_FILE_FORMAT_HARBOUR_BRIDGE_SESSION_FILE", "IMPORT_RULES_FILE_FORMAT_ORATOPG_CONFIG_FILE"], "enumDescriptions": ["Unspecified rules format.", "HarbourBridge session file.", "Ora2Pg configuration file."], "readOnly": true, "type": "string"}, "files": {"description": "Output only. File names used for the import rules job.", "items": {"type": "string"}, "readOnly": true, "type": "array"}}, "type": "object"}, "IndexEntity": {"description": "Index is not used as an independent entity, it is retrieved as part of a Table entity.", "id": "IndexEntity", "properties": {"customFeatures": {"additionalProperties": {"description": "Properties of the object.", "type": "any"}, "description": "Custom engine specific features.", "type": "object"}, "name": {"description": "The name of the index.", "type": "string"}, "tableColumns": {"description": "Table columns used as part of the Index, for example B-TREE index should list the columns which constitutes the index.", "items": {"type": "string"}, "type": "array"}, "tableColumnsDescending": {"description": "For each table_column, mark whether it's sorting order is ascending (false) or descending (true). If no value is defined, assume all columns are sorted in ascending order. Otherwise, the number of items must match that of table_columns with each value specifying the direction of the matched column by its index.", "items": {"type": "boolean"}, "type": "array"}, "type": {"description": "Type of index, for example B-TREE.", "type": "string"}, "unique": {"description": "Boolean value indicating whether the index is unique.", "type": "boolean"}}, "type": "object"}, "InstanceNetworkConfig": {"description": "Metadata related to instance level network configuration.", "id": "InstanceNetworkConfig", "properties": {"authorizedExternalNetworks": {"description": "Optional. A list of external network authorized to access this instance.", "items": {"$ref": "AuthorizedNetwork"}, "type": "array"}, "enableOutboundPublicIp": {"description": "Optional. Enabling an outbound public IP address to support a database server sending requests out into the internet.", "type": "boolean"}, "enablePublicIp": {"description": "Optional. Enabling public ip for the instance.", "type": "boolean"}}, "type": "object"}, "IntComparisonFilter": {"description": "Filter based on relation between source value and compare value of type integer in ConditionalColumnSetValue", "id": "IntComparisonFilter", "properties": {"value": {"description": "Required. Integer compare value to be used", "format": "int64", "type": "string"}, "valueComparison": {"description": "Required. Relation between source value and compare value", "enum": ["VALUE_COMPARISON_UNSPECIFIED", "VALUE_COMPARISON_IF_VALUE_SMALLER_THAN", "VALUE_COMPARISON_IF_VALUE_SMALLER_EQUAL_THAN", "VALUE_COMPARISON_IF_VALUE_LARGER_THAN", "VALUE_COMPARISON_IF_VALUE_LARGER_EQUAL_THAN"], "enumDescriptions": ["Value comparison unspecified.", "Value is smaller than the Compare value.", "Value is smaller or equal than the Compare value.", "Value is larger than the Compare value.", "Value is larger or equal than the Compare value."], "type": "string"}}, "type": "object"}, "ListConnectionProfilesResponse": {"description": "Response message for 'ListConnectionProfiles' request.", "id": "ListConnectionProfilesResponse", "properties": {"connectionProfiles": {"description": "The response list of connection profiles.", "items": {"$ref": "ConnectionProfile"}, "type": "array"}, "nextPageToken": {"description": "A token which can be sent as `page_token` to retrieve the next page. If this field is omitted, there are no subsequent pages.", "type": "string"}, "unreachable": {"description": "Locations that could not be reached.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "ListConversionWorkspacesResponse": {"description": "Response message for 'ListConversionWorkspaces' request.", "id": "ListConversionWorkspacesResponse", "properties": {"conversionWorkspaces": {"description": "The list of conversion workspace objects.", "items": {"$ref": "ConversionWorkspace"}, "type": "array"}, "nextPageToken": {"description": "A token which can be sent as `page_token` to retrieve the next page. If this field is omitted, there are no subsequent pages.", "type": "string"}, "unreachable": {"description": "Locations that could not be reached.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "ListLocationsResponse": {"description": "The response message for Locations.ListLocations.", "id": "ListLocationsResponse", "properties": {"locations": {"description": "A list of locations that matches the specified filter in the request.", "items": {"$ref": "Location"}, "type": "array"}, "nextPageToken": {"description": "The standard List next-page token.", "type": "string"}}, "type": "object"}, "ListMappingRulesResponse": {"description": "Response message for 'ListMappingRulesRequest' request.", "id": "ListMappingRulesResponse", "properties": {"mappingRules": {"description": "The list of conversion workspace mapping rules.", "items": {"$ref": "MappingRule"}, "type": "array"}, "nextPageToken": {"description": "A token which can be sent as `page_token` to retrieve the next page. If this field is omitted, there are no subsequent pages.", "type": "string"}}, "type": "object"}, "ListMigrationJobObjectsResponse": {"description": "Response containing the objects for a migration job.", "id": "ListMigrationJobObjectsResponse", "properties": {"migrationJobObjects": {"description": "List of migration job objects.", "items": {"$ref": "MigrationJobObject"}, "type": "array"}, "nextPageToken": {"description": "A token, which can be sent as `page_token` to retrieve the next page.", "type": "string"}}, "type": "object"}, "ListMigrationJobsResponse": {"description": "Response message for 'ListMigrationJobs' request.", "id": "ListMigrationJobsResponse", "properties": {"migrationJobs": {"description": "The list of migration jobs objects.", "items": {"$ref": "<PERSON><PERSON><PERSON><PERSON>"}, "type": "array"}, "nextPageToken": {"description": "A token which can be sent as `page_token` to retrieve the next page. If this field is omitted, there are no subsequent pages.", "type": "string"}, "unreachable": {"description": "Locations that could not be reached.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "ListOperationsResponse": {"description": "The response message for Operations.ListOperations.", "id": "ListOperationsResponse", "properties": {"nextPageToken": {"description": "The standard List next-page token.", "type": "string"}, "operations": {"description": "A list of operations that matches the specified filter in the request.", "items": {"$ref": "Operation"}, "type": "array"}}, "type": "object"}, "ListPrivateConnectionsResponse": {"description": "Response message for 'ListPrivateConnections' request.", "id": "ListPrivateConnectionsResponse", "properties": {"nextPageToken": {"description": "A token which can be sent as `page_token` to retrieve the next page. If this field is omitted, there are no subsequent pages.", "type": "string"}, "privateConnections": {"description": "List of private connections.", "items": {"$ref": "PrivateConnection"}, "type": "array"}, "unreachable": {"description": "Locations that could not be reached.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "Location": {"description": "A resource that represents a Google Cloud location.", "id": "Location", "properties": {"displayName": {"description": "The friendly name for this location, typically a nearby city name. For example, \"Tokyo\".", "type": "string"}, "labels": {"additionalProperties": {"type": "string"}, "description": "Cross-service attributes for the location. For example {\"cloud.googleapis.com/region\": \"us-east1\"}", "type": "object"}, "locationId": {"description": "The canonical id for this location. For example: `\"us-east1\"`.", "type": "string"}, "metadata": {"additionalProperties": {"description": "Properties of the object. Contains field @type with type URL.", "type": "any"}, "description": "Service-specific metadata. For example the available capacity at the given location.", "type": "object"}, "name": {"description": "Resource name for the location, which may vary between implementations. For example: `\"projects/example-project/locations/us-east1\"`", "type": "string"}}, "type": "object"}, "LogFileDirectories": {"description": "Configuration to specify the Oracle directories to access the log files.", "id": "LogFileDirectories", "properties": {"archivedLogDirectory": {"description": "Required. Oracle directory for archived logs.", "type": "string"}, "onlineLogDirectory": {"description": "Required. Oracle directory for online logs.", "type": "string"}}, "type": "object"}, "LogMiner": {"description": "Configuration to use LogMiner CDC method.", "id": "LogMiner", "properties": {}, "type": "object"}, "LookupMigrationJobObjectRequest": {"description": "Request for looking up a specific migration job object by its source object identifier.", "id": "LookupMigrationJobObjectRequest", "properties": {"sourceObjectIdentifier": {"$ref": "SourceObjectIdentifier", "description": "Required. The source object identifier which maps to the migration job object."}}, "type": "object"}, "MachineConfig": {"description": "MachineConfig describes the configuration of a machine.", "id": "MachineConfig", "properties": {"cpuCount": {"description": "The number of CPU's in the VM instance.", "format": "int32", "type": "integer"}, "machineType": {"description": "Optional. Machine type of the VM instance. E.g. \"n2-highmem-4\", \"n2-highmem-8\", \"c4a-highmem-4-lssd\". cpu_count must match the number of vCPUs in the machine type.", "type": "string"}}, "type": "object"}, "MappingRule": {"description": "Definition of a transformation that is to be applied to a group of entities in the source schema. Several such transformations can be applied to an entity sequentially to define the corresponding entity in the target schema.", "id": "MappingRule", "properties": {"conditionalColumnSetValue": {"$ref": "ConditionalColumnSetValue", "description": "Optional. Rule to specify how the data contained in a column should be transformed (such as trimmed, rounded, etc) provided that the data meets certain criteria."}, "convertRowidColumn": {"$ref": "ConvertRowIdToColumn", "description": "Optional. Rule to specify how multiple tables should be converted with an additional rowid column."}, "displayName": {"description": "Optional. A human readable name", "type": "string"}, "entityMove": {"$ref": "EntityMove", "description": "Optional. Rule to specify how multiple entities should be relocated into a different schema."}, "filter": {"$ref": "MappingRuleFilter", "description": "Required. The rule filter"}, "filterTableColumns": {"$ref": "FilterTableColumns", "description": "Optional. Rule to specify the list of columns to include or exclude from a table."}, "multiColumnDataTypeChange": {"$ref": "MultiColumnDatatypeChange", "description": "Optional. Rule to specify how multiple columns should be converted to a different data type."}, "multiEntityRename": {"$ref": "MultiEntityRename", "description": "Optional. Rule to specify how multiple entities should be renamed."}, "name": {"description": "Full name of the mapping rule resource, in the form of: projects/{project}/locations/{location}/conversionWorkspaces/{set}/mappingRule/{rule}.", "type": "string"}, "revisionCreateTime": {"description": "Output only. The timestamp that the revision was created.", "format": "google-datetime", "readOnly": true, "type": "string"}, "revisionId": {"description": "Output only. The revision ID of the mapping rule. A new revision is committed whenever the mapping rule is changed in any way. The format is an 8-character hexadecimal string.", "readOnly": true, "type": "string"}, "ruleOrder": {"description": "Required. The order in which the rule is applied. Lower order rules are applied before higher value rules so they may end up being overridden.", "format": "int64", "type": "string"}, "ruleScope": {"description": "Required. The rule scope", "enum": ["DATABASE_ENTITY_TYPE_UNSPECIFIED", "DATABASE_ENTITY_TYPE_SCHEMA", "DATABASE_ENTITY_TYPE_TABLE", "DATABASE_ENTITY_TYPE_COLUMN", "DATABASE_ENTITY_TYPE_CONSTRAINT", "DATABASE_ENTITY_TYPE_INDEX", "DATABASE_ENTITY_TYPE_TRIGGER", "DATABASE_ENTITY_TYPE_VIEW", "DATABASE_ENTITY_TYPE_SEQUENCE", "DATABASE_ENTITY_TYPE_STORED_PROCEDURE", "DATABASE_ENTITY_TYPE_FUNCTION", "DATABASE_ENTITY_TYPE_SYNONYM", "DATABASE_ENTITY_TYPE_DATABASE_PACKAGE", "DATABASE_ENTITY_TYPE_UDT", "DATABASE_ENTITY_TYPE_MATERIALIZED_VIEW", "DATABASE_ENTITY_TYPE_DATABASE"], "enumDescriptions": ["Unspecified database entity type.", "Schema.", "Table.", "Column.", "Constraint.", "Index.", "Trigger.", "View.", "Sequence.", "Stored Procedure.", "Function.", "Synonym.", "Package.", "UDT.", "Materialized View.", "Database."], "type": "string"}, "setTablePrimaryKey": {"$ref": "SetTablePrimaryKey", "description": "Optional. Rule to specify the primary key for a table"}, "singleColumnChange": {"$ref": "SingleColumnChange", "description": "Optional. Rule to specify how a single column is converted."}, "singleEntityRename": {"$ref": "SingleEntityRename", "description": "Optional. Rule to specify how a single entity should be renamed."}, "singlePackageChange": {"$ref": "SinglePackageChange", "description": "Optional. Rule to specify how a single package is converted."}, "sourceSqlChange": {"$ref": "SourceSqlChange", "description": "Optional. Rule to change the sql code for an entity, for example, function, procedure."}, "state": {"description": "Optional. The mapping rule state", "enum": ["STATE_UNSPECIFIED", "ENABLED", "DISABLED", "DELETED"], "enumDescriptions": ["The state of the mapping rule is unknown.", "The rule is enabled.", "The rule is disabled.", "The rule is logically deleted."], "type": "string"}}, "type": "object"}, "MappingRuleFilter": {"description": "A filter defining the entities that a mapping rule should be applied to. When more than one field is specified, the rule is applied only to entities which match all the fields.", "id": "MappingRuleFilter", "properties": {"entities": {"description": "Optional. The rule should be applied to specific entities defined by their fully qualified names.", "items": {"type": "string"}, "type": "array"}, "entityNameContains": {"description": "Optional. The rule should be applied to entities whose non-qualified name contains the given string.", "type": "string"}, "entityNamePrefix": {"description": "Optional. The rule should be applied to entities whose non-qualified name starts with the given prefix.", "type": "string"}, "entityNameSuffix": {"description": "Optional. The rule should be applied to entities whose non-qualified name ends with the given suffix.", "type": "string"}, "parentEntity": {"description": "Optional. The rule should be applied to entities whose parent entity (fully qualified name) matches the given value. For example, if the rule applies to a table entity, the expected value should be a schema (schema). If the rule applies to a column or index entity, the expected value can be either a schema (schema) or a table (schema.table)", "type": "string"}}, "type": "object"}, "MaterializedViewEntity": {"description": "MaterializedView's parent is a schema.", "id": "MaterializedViewEntity", "properties": {"customFeatures": {"additionalProperties": {"description": "Properties of the object.", "type": "any"}, "description": "Custom engine specific features.", "type": "object"}, "indices": {"description": "View indices.", "items": {"$ref": "IndexEntity"}, "type": "array"}, "sqlCode": {"description": "The SQL code which creates the view.", "type": "string"}}, "type": "object"}, "MigrationJob": {"description": "Represents a Database Migration Service migration job object.", "id": "<PERSON><PERSON><PERSON><PERSON>", "properties": {"cmekKeyName": {"description": "The CMEK (customer-managed encryption key) fully qualified key name used for the migration job. This field supports all migration jobs types except for: * Mysql to Mysql (use the cmek field in the cloudsql connection profile instead). * PostrgeSQL to PostgreSQL (use the cmek field in the cloudsql connection profile instead). * PostgreSQL to AlloyDB (use the kms_key_name field in the alloydb connection profile instead). Each Cloud CMEK key has the following format: projects/[PROJECT]/locations/[REGION]/keyRings/[RING]/cryptoKeys/[KEY_NAME]", "type": "string"}, "conversionWorkspace": {"$ref": "ConversionWorkspaceInfo", "description": "The conversion workspace used by the migration."}, "createTime": {"description": "Output only. The timestamp when the migration job resource was created. A timestamp in RFC3339 UTC \"Zulu\" format, accurate to nanoseconds. Example: \"2014-10-02T15:01:23.045123456Z\".", "format": "google-datetime", "readOnly": true, "type": "string"}, "destination": {"description": "Required. The resource name (URI) of the destination connection profile.", "type": "string"}, "destinationDatabase": {"$ref": "DatabaseType", "description": "The database engine type and provider of the destination."}, "displayName": {"description": "The migration job display name.", "type": "string"}, "dumpFlags": {"$ref": "DumpFlags", "description": "The initial dump flags. This field and the \"dump_path\" field are mutually exclusive."}, "dumpPath": {"description": "The path to the dump file in Google Cloud Storage, in the format: (gs://[BUCKET_NAME]/[OBJECT_NAME]). This field and the \"dump_flags\" field are mutually exclusive.", "type": "string"}, "dumpType": {"description": "Optional. The type of the data dump. Supported for MySQL to CloudSQL for MySQL migrations only.", "enum": ["DUMP_TYPE_UNSPECIFIED", "LOGICAL", "PHYSICAL"], "enumDescriptions": ["If not specified, defaults to LOGICAL", "Logical dump.", "Physical file-based dump. Supported for MySQL to CloudSQL for MySQL migrations only."], "type": "string"}, "duration": {"description": "Output only. The duration of the migration job (in seconds). A duration in seconds with up to nine fractional digits, terminated by 's'. Example: \"3.5s\".", "format": "google-duration", "readOnly": true, "type": "string"}, "endTime": {"description": "Output only. If the migration job is completed, the time when it was completed.", "format": "google-datetime", "readOnly": true, "type": "string"}, "error": {"$ref": "Status", "description": "Output only. The error details in case of state FAILED.", "readOnly": true}, "filter": {"description": "This field can be used to select the entities to migrate as part of the migration job. It uses AIP-160 notation to select a subset of the entities configured on the associated conversion-workspace. This field should not be set on migration-jobs that are not associated with a conversion workspace.", "type": "string"}, "labels": {"additionalProperties": {"type": "string"}, "description": "The resource labels for migration job to use to annotate any related underlying resources such as Compute Engine VMs. An object containing a list of \"key\": \"value\" pairs. Example: `{ \"name\": \"wrench\", \"mass\": \"1.3kg\", \"count\": \"3\" }`.", "type": "object"}, "name": {"description": "The name (URI) of this migration job resource, in the form of: projects/{project}/locations/{location}/migrationJobs/{migrationJob}.", "type": "string"}, "objectsConfig": {"$ref": "MigrationJobObjectsConfig", "description": "Optional. The objects that need to be migrated."}, "oracleToPostgresConfig": {"$ref": "OracleToPostgresConfig", "description": "Configuration for heterogeneous **Oracle to Cloud SQL for PostgreSQL** and **Oracle to AlloyDB for PostgreSQL** migrations."}, "performanceConfig": {"$ref": "PerformanceConfig", "description": "Optional. Data dump parallelism settings used by the migration."}, "phase": {"description": "Output only. The current migration job phase.", "enum": ["PHASE_UNSPECIFIED", "FULL_DUMP", "CDC", "PROMOTE_IN_PROGRESS", "WAITING_FOR_SOURCE_WRITES_TO_STOP", "PREPARING_THE_DUMP", "READY_FOR_PROMOTE"], "enumDescriptions": ["The phase of the migration job is unknown.", "The migration job is in the full dump phase.", "The migration job is CDC phase.", "The migration job is running the promote phase.", "Only RDS flow - waiting for source writes to stop", "Only RDS flow - the sources writes stopped, waiting for dump to begin", "The migration job is ready to be promoted."], "readOnly": true, "type": "string"}, "reverseSshConnectivity": {"$ref": "ReverseSshConnectivity", "description": "The details needed to communicate to the source over Reverse SSH tunnel connectivity."}, "satisfiesPzi": {"description": "Output only. Reserved for future use.", "readOnly": true, "type": "boolean"}, "satisfiesPzs": {"description": "Output only. Reserved for future use.", "readOnly": true, "type": "boolean"}, "source": {"description": "Required. The resource name (URI) of the source connection profile.", "type": "string"}, "sourceDatabase": {"$ref": "DatabaseType", "description": "The database engine type and provider of the source."}, "sqlserverHomogeneousMigrationJobConfig": {"$ref": "SqlServerHomogeneousMigrationJobConfig", "description": "Optional. Configuration for SQL Server homogeneous migration."}, "sqlserverToPostgresConfig": {"$ref": "SqlServerToPostgresConfig", "description": "Configuration for heterogeneous **SQL Server to Cloud SQL for PostgreSQL** migrations."}, "state": {"description": "The current migration job state.", "enum": ["STATE_UNSPECIFIED", "MAINTENANCE", "DRAFT", "CREATING", "NOT_STARTED", "RUNNING", "FAILED", "COMPLETED", "DELETING", "STOPPING", "STOPPED", "DELETED", "UPDATING", "STARTING", "RESTARTING", "RESUMING"], "enumDescriptions": ["The state of the migration job is unknown.", "The migration job is down for maintenance.", "The migration job is in draft mode and no resources are created.", "The migration job is being created.", "The migration job is created and not started.", "The migration job is running.", "The migration job failed.", "The migration job has been completed.", "The migration job is being deleted.", "The migration job is being stopped.", "The migration job is currently stopped.", "The migration job has been deleted.", "The migration job is being updated.", "The migration job is starting.", "The migration job is restarting.", "The migration job is resuming."], "type": "string"}, "staticIpConnectivity": {"$ref": "StaticIpConnectivity", "description": "static ip connectivity data (default, no additional details needed)."}, "type": {"description": "Required. The migration job type.", "enum": ["TYPE_UNSPECIFIED", "ONE_TIME", "CONTINUOUS"], "enumDescriptions": ["The type of the migration job is unknown.", "The migration job is a one time migration.", "The migration job is a continuous migration."], "type": "string"}, "updateTime": {"description": "Output only. The timestamp when the migration job resource was last updated. A timestamp in RFC3339 UTC \"Zulu\" format, accurate to nanoseconds. Example: \"2014-10-02T15:01:23.045123456Z\".", "format": "google-datetime", "readOnly": true, "type": "string"}, "vpcPeeringConnectivity": {"$ref": "VpcPeeringConnectivity", "description": "The details of the VPC network that the source database is located in."}}, "type": "object"}, "MigrationJobObject": {"description": "A specific Migration Job Object (e.g. a specifc DB Table)", "id": "MigrationJobObject", "properties": {"createTime": {"description": "Output only. The creation time of the migration job object.", "format": "google-datetime", "readOnly": true, "type": "string"}, "error": {"$ref": "Status", "description": "Output only. The error details in case of failure.", "readOnly": true}, "name": {"description": "The object's name.", "type": "string"}, "phase": {"description": "Output only. The phase of the migration job object.", "enum": ["PHASE_UNSPECIFIED", "FULL_DUMP", "CDC", "READY_FOR_PROMOTE", "PROMOTE_IN_PROGRESS", "PROMOTED", "DIFF_BACKUP"], "enumDescriptions": ["The phase of the migration job is unknown.", "The migration job object is in the full dump phase.", "The migration job object is in CDC phase.", "The migration job object is ready to be promoted.", "The migration job object is in running the promote phase.", "The migration job is promoted.", "The migration job object is in the differential backup phase."], "readOnly": true, "type": "string"}, "sourceObject": {"$ref": "SourceObjectIdentifier", "description": "The object identifier in the data source."}, "state": {"description": "The state of the migration job object.", "enum": ["STATE_UNSPECIFIED", "NOT_STARTED", "RUNNING", "STOPPING", "STOPPED", "RESTARTING", "FAILED", "REMOVING", "NOT_SELECTED", "COMPLETED"], "enumDescriptions": ["The state of the migration job object is unknown.", "The migration job object is not started.", "The migration job object is running.", "The migration job object is being stopped.", "The migration job object is currently stopped.", "The migration job object is restarting.", "The migration job object failed.", "The migration job object is deleting.", "The migration job object is not selected for migration.", "The migration job object is completed."], "type": "string"}, "updateTime": {"description": "Output only. The last update time of the migration job object.", "format": "google-datetime", "readOnly": true, "type": "string"}}, "type": "object"}, "MigrationJobObjectsConfig": {"description": "Configuration for the objects to be migrated.", "id": "MigrationJobObjectsConfig", "properties": {"sourceObjectsConfig": {"$ref": "SourceObjectsConfig", "description": "The list of the migration job objects."}}, "type": "object"}, "MigrationJobVerificationError": {"description": "Error message of a verification Migration job.", "id": "MigrationJobVerificationError", "properties": {"errorCode": {"description": "Output only. An instance of ErrorCode specifying the error that occurred.", "enum": ["ERROR_CODE_UNSPECIFIED", "CONNECTION_FAILURE", "AUTHENTICATION_FAILURE", "INVALID_CONNECTION_PROFILE_CONFIG", "VERSION_INCOMPATIBILITY", "CONNECTION_PROFILE_TYPES_INCOMPATIBILITY", "NO_PGLOGICAL_INSTALLED", "PGLOGICAL_NODE_ALREADY_EXISTS", "INVALID_WAL_LEVEL", "INVALID_SHARED_PRELOAD_LIBRARY", "INSUFFICIENT_MAX_REPLICATION_SLOTS", "INSUFFICIENT_MAX_WAL_SENDERS", "INSUFFICIENT_MAX_WORKER_PROCESSES", "UNSUPPORTED_EXTENSIONS", "UNSUPPORTED_MIGRATION_TYPE", "INVALID_RDS_LOGICAL_REPLICATION", "UNSUPPORTED_GTID_MODE", "UNSUPPORTED_TABLE_DEFINITION", "UNSUPPORTED_DEFINER", "CANT_RESTART_RUNNING_MIGRATION", "SOURCE_ALREADY_SETUP", "TABLES_WITH_LIMITED_SUPPORT", "UNSUPPORTED_DATABASE_LOCALE", "UNSUPPORTED_DATABASE_FDW_CONFIG", "ERROR_RDBMS", "SOURCE_SIZE_EXCEEDS_THRESHOLD", "EXISTING_CONFLICTING_DATABASES", "PARALLEL_IMPORT_INSUFFICIENT_PRIVILEGE", "EXISTING_DATA", "SOURCE_MAX_SUBSCRIPTIONS"], "enumDescriptions": ["An unknown error occurred", "We failed to connect to one of the connection profile.", "We failed to authenticate to one of the connection profile.", "One of the involved connection profiles has an invalid configuration.", "The versions of the source and the destination are incompatible.", "The types of the source and the destination are incompatible.", "No pglogical extension installed on databases, applicable for postgres.", "pglogical node already exists on databases, applicable for postgres.", "The value of parameter wal_level is not set to logical.", "The value of parameter shared_preload_libraries does not include pglogical.", "The value of parameter max_replication_slots is not sufficient.", "The value of parameter max_wal_senders is not sufficient.", "The value of parameter max_worker_processes is not sufficient.", "Extensions installed are either not supported or having unsupported versions.", "Unsupported migration type.", "Invalid RDS logical replication.", "The gtid_mode is not supported, applicable for MySQL.", "The table definition is not support due to missing primary key or replica identity.", "The definer is not supported.", "Migration is already running at the time of restart request.", "The source already has a replication setup.", "The source has tables with limited support. E.g. PostgreSQL tables without primary keys.", "The source uses an unsupported locale.", "The source uses an unsupported Foreign Data Wrapper configuration.", "There was an underlying RDBMS error.", "The source DB size in Bytes exceeds a certain threshold. The migration might require an increase of quota, or might not be supported.", "The destination DB contains existing databases that are conflicting with those in the source DB.", "Insufficient privilege to enable the parallelism configuration.", "The destination instance contains existing data or user defined entities (for example databases, tables, or functions). You can only migrate to empty instances. Clear your destination instance and retry the migration job.", "The migration job is configured to use max number of subscriptions to migrate data from the source to the destination."], "readOnly": true, "type": "string"}, "errorDetailMessage": {"description": "Output only. A specific detailed error message, if supplied by the engine.", "readOnly": true, "type": "string"}, "errorMessage": {"description": "Output only. A formatted message with further details about the error and a CTA.", "readOnly": true, "type": "string"}}, "type": "object"}, "MultiColumnDatatypeChange": {"description": "Options to configure rule type MultiColumnDatatypeChange. The rule is used to change the data type and associated properties of multiple columns at once. The rule filter field can refer to one or more entities. The rule scope can be one of:Column. This rule requires additional filters to be specified beyond the basic rule filter field, which is the source data type, but the rule supports additional filtering capabilities such as the minimum and maximum field length. All additional filters which are specified are required to be met in order for the rule to be applied (logical AND between the fields).", "id": "MultiColumnDatatypeChange", "properties": {"customFeatures": {"additionalProperties": {"description": "Properties of the object.", "type": "any"}, "description": "Optional. Custom engine specific features.", "type": "object"}, "newDataType": {"description": "Required. New data type.", "type": "string"}, "overrideFractionalSecondsPrecision": {"description": "Optional. Column fractional seconds precision - used only for timestamp based datatypes - if not specified and relevant uses the source column fractional seconds precision.", "format": "int32", "type": "integer"}, "overrideLength": {"description": "Optional. Column length - e.g. varchar (50) - if not specified and relevant uses the source column length.", "format": "int64", "type": "string"}, "overridePrecision": {"description": "Optional. Column precision - when relevant - if not specified and relevant uses the source column precision.", "format": "int32", "type": "integer"}, "overrideScale": {"description": "Optional. Column scale - when relevant - if not specified and relevant uses the source column scale.", "format": "int32", "type": "integer"}, "sourceDataTypeFilter": {"description": "Required. Filter on source data type.", "type": "string"}, "sourceNumericFilter": {"$ref": "SourceNumericFilter", "description": "Optional. Filter for fixed point number data types such as NUMERIC/NUMBER."}, "sourceTextFilter": {"$ref": "SourceTextFilter", "description": "Optional. Filter for text-based data types like varchar."}}, "type": "object"}, "MultiEntityRename": {"description": "Options to configure rule type MultiEntityRename. The rule is used to rename multiple entities. The rule filter field can refer to one or more entities. The rule scope can be one of: Database, Schema, Table, Column, Constraint, Index, View, Function, Stored Procedure, Materialized View, Sequence, UDT", "id": "MultiEntityRename", "properties": {"newNamePattern": {"description": "Optional. The pattern used to generate the new entity's name. This pattern must include the characters '{name}', which will be replaced with the name of the original entity. For example, the pattern 't_{name}' for an entity name jobs would be converted to 't_jobs'. If unspecified, the default value for this field is '{name}'", "type": "string"}, "sourceNameTransformation": {"description": "Optional. Additional transformation that can be done on the source entity name before it is being used by the new_name_pattern, for example lower case. If no transformation is desired, use NO_TRANSFORMATION", "enum": ["ENTITY_NAME_TRANSFORMATION_UNSPECIFIED", "ENTITY_NAME_TRANSFORMATION_NO_TRANSFORMATION", "ENTITY_NAME_TRANSFORMATION_LOWER_CASE", "ENTITY_NAME_TRANSFORMATION_UPPER_CASE", "ENTITY_NAME_TRANSFORMATION_CAPITALIZED_CASE"], "enumDescriptions": ["Entity name transformation unspecified.", "No transformation.", "Transform to lower case.", "Transform to upper case.", "Transform to capitalized case."], "type": "string"}}, "type": "object"}, "MySqlConnectionProfile": {"description": "Specifies connection parameters required specifically for MySQL databases.", "id": "MySqlConnectionProfile", "properties": {"cloudSqlId": {"description": "If the source is a Cloud SQL database, use this field to provide the Cloud SQL instance ID of the source.", "type": "string"}, "host": {"description": "Required. The IP or hostname of the source MySQL database.", "type": "string"}, "password": {"description": "Required. Input only. The password for the user that Database Migration Service will be using to connect to the database. This field is not returned on request, and the value is encrypted when stored in Database Migration Service.", "type": "string"}, "passwordSet": {"description": "Output only. Indicates If this connection profile password is stored.", "readOnly": true, "type": "boolean"}, "port": {"description": "Required. The network port of the source MySQL database.", "format": "int32", "type": "integer"}, "ssl": {"$ref": "SslConfig", "description": "SSL configuration for the destination to connect to the source database."}, "username": {"description": "Required. The username that Database Migration Service will use to connect to the database. The value is encrypted when stored in Database Migration Service.", "type": "string"}}, "type": "object"}, "Operation": {"description": "This resource represents a long-running operation that is the result of a network API call.", "id": "Operation", "properties": {"done": {"description": "If the value is `false`, it means the operation is still in progress. If `true`, the operation is completed, and either `error` or `response` is available.", "type": "boolean"}, "error": {"$ref": "Status", "description": "The error result of the operation in case of failure or cancellation."}, "metadata": {"additionalProperties": {"description": "Properties of the object. Contains field @type with type URL.", "type": "any"}, "description": "Service-specific metadata associated with the operation. It typically contains progress information and common metadata such as create time. Some services might not provide such metadata. Any method that returns a long-running operation should document the metadata type, if any.", "type": "object"}, "name": {"description": "The server-assigned name, which is only unique within the same service that originally returns it. If you use the default HTTP mapping, the `name` should be a resource name ending with `operations/{unique_id}`.", "type": "string"}, "response": {"additionalProperties": {"description": "Properties of the object. Contains field @type with type URL.", "type": "any"}, "description": "The normal, successful response of the operation. If the original method returns no data on success, such as `Delete`, the response is `google.protobuf.Empty`. If the original method is standard `Get`/`Create`/`Update`, the response should be the resource. For other methods, the response should have the type `XxxResponse`, where `Xxx` is the original method name. For example, if the original method name is `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.", "type": "object"}}, "type": "object"}, "OracleAsmConfig": {"description": "Configuration for Oracle Automatic Storage Management (ASM) connection.", "id": "OracleAsmConfig", "properties": {"asmService": {"description": "Required. ASM service name for the Oracle ASM connection.", "type": "string"}, "hostname": {"description": "Required. Hostname for the Oracle ASM connection.", "type": "string"}, "password": {"description": "Required. Input only. Password for the Oracle ASM connection.", "type": "string"}, "passwordSet": {"description": "Output only. Indicates whether a new password is included in the request.", "readOnly": true, "type": "boolean"}, "port": {"description": "Required. Port for the Oracle ASM connection.", "format": "int32", "type": "integer"}, "ssl": {"$ref": "SslConfig", "description": "Optional. SSL configuration for the Oracle connection."}, "username": {"description": "Required. Username for the Oracle ASM connection.", "type": "string"}}, "type": "object"}, "OracleAsmLogFileAccess": {"description": "Configuration to use Oracle ASM to access the log files.", "id": "OracleAsmLogFileAccess", "properties": {}, "type": "object"}, "OracleConnectionProfile": {"description": "Specifies connection parameters required specifically for Oracle databases.", "id": "OracleConnectionProfile", "properties": {"databaseService": {"description": "Required. Database service for the Oracle connection.", "type": "string"}, "forwardSshConnectivity": {"$ref": "ForwardSshTunnelConnectivity", "description": "Forward SSH tunnel connectivity."}, "host": {"description": "Required. The IP or hostname of the source Oracle database.", "type": "string"}, "oracleAsmConfig": {"$ref": "OracleAsmConfig", "description": "Optional. Configuration for Oracle ASM connection."}, "password": {"description": "Required. Input only. The password for the user that Database Migration Service will be using to connect to the database. This field is not returned on request, and the value is encrypted when stored in Database Migration Service.", "type": "string"}, "passwordSet": {"description": "Output only. Indicates whether a new password is included in the request.", "readOnly": true, "type": "boolean"}, "port": {"description": "Required. The network port of the source Oracle database.", "format": "int32", "type": "integer"}, "privateConnectivity": {"$ref": "PrivateConnectivity", "description": "Private connectivity."}, "ssl": {"$ref": "SslConfig", "description": "SSL configuration for the connection to the source Oracle database. * Only `SERVER_ONLY` configuration is supported for Oracle SSL. * SSL is supported for Oracle versions 12 and above."}, "staticServiceIpConnectivity": {"$ref": "StaticServiceIpConnectivity", "description": "Static Service IP connectivity."}, "username": {"description": "Required. The username that Database Migration Service will use to connect to the database. The value is encrypted when stored in Database Migration Service.", "type": "string"}}, "type": "object"}, "OracleSourceConfig": {"description": "Configuration for Oracle as a source in a migration.", "id": "OracleSourceConfig", "properties": {"binaryLogParser": {"$ref": "BinaryLog<PERSON><PERSON>er", "description": "Use Binary Log Parser."}, "cdcStartPosition": {"description": "Optional. The schema change number (SCN) to start CDC data migration from.", "format": "int64", "type": "string"}, "logMiner": {"$ref": "LogMiner", "description": "Use LogMiner."}, "maxConcurrentCdcConnections": {"description": "Optional. Maximum number of connections Database Migration Service will open to the source for CDC phase.", "format": "int32", "type": "integer"}, "maxConcurrentFullDumpConnections": {"description": "Optional. Maximum number of connections Database Migration Service will open to the source for full dump phase.", "format": "int32", "type": "integer"}, "skipFullDump": {"description": "Optional. Whether to skip full dump or not.", "type": "boolean"}}, "type": "object"}, "OracleToPostgresConfig": {"description": "Configuration for heterogeneous **Oracle to Cloud SQL for PostgreSQL** and **Oracle to AlloyDB for PostgreSQL** migrations.", "id": "OracleToPostgresConfig", "properties": {"oracleSourceConfig": {"$ref": "OracleSourceConfig", "description": "Optional. Configuration for Oracle source."}, "postgresDestinationConfig": {"$ref": "PostgresDestinationConfig", "description": "Optional. Configuration for Postgres destination."}}, "type": "object"}, "PackageEntity": {"description": "Package's parent is a schema.", "id": "PackageEntity", "properties": {"customFeatures": {"additionalProperties": {"description": "Properties of the object.", "type": "any"}, "description": "Custom engine specific features.", "type": "object"}, "packageBody": {"description": "The SQL code which creates the package body. If the package specification has cursors or subprograms, then the package body is mandatory.", "type": "string"}, "packageSqlCode": {"description": "The SQL code which creates the package.", "type": "string"}}, "type": "object"}, "PerformanceConfig": {"description": "Performance configuration definition.", "id": "PerformanceConfig", "properties": {"dumpParallelLevel": {"description": "Initial dump parallelism level.", "enum": ["DUMP_PARALLEL_LEVEL_UNSPECIFIED", "MIN", "OPTIMAL", "MAX"], "enumDescriptions": ["Unknown dump parallel level. Will be defaulted to OPTIMAL.", "Minimal parallel level.", "Optimal parallel level.", "Maximum parallel level."], "type": "string"}}, "type": "object"}, "Policy": {"description": "An Identity and Access Management (IAM) policy, which specifies access controls for Google Cloud resources. A `Policy` is a collection of `bindings`. A `binding` binds one or more `members`, or principals, to a single `role`. Principals can be user accounts, service accounts, Google groups, and domains (such as G Suite). A `role` is a named list of permissions; each `role` can be an IAM predefined role or a user-created custom role. For some types of Google Cloud resources, a `binding` can also specify a `condition`, which is a logical expression that allows access to a resource only if the expression evaluates to `true`. A condition can add constraints based on attributes of the request, the resource, or both. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies). **JSON example:** ``` { \"bindings\": [ { \"role\": \"roles/resourcemanager.organizationAdmin\", \"members\": [ \"user:<EMAIL>\", \"group:<EMAIL>\", \"domain:google.com\", \"serviceAccount:<EMAIL>\" ] }, { \"role\": \"roles/resourcemanager.organizationViewer\", \"members\": [ \"user:<EMAIL>\" ], \"condition\": { \"title\": \"expirable access\", \"description\": \"Does not grant access after Sep 2020\", \"expression\": \"request.time < timestamp('2020-10-01T00:00:00.000Z')\", } } ], \"etag\": \"BwWWja0YfJA=\", \"version\": 3 } ``` **YAML example:** ``` bindings: - members: - user:<EMAIL> - group:<EMAIL> - domain:google.com - serviceAccount:<EMAIL> role: roles/resourcemanager.organizationAdmin - members: - user:<EMAIL> role: roles/resourcemanager.organizationViewer condition: title: expirable access description: Does not grant access after Sep 2020 expression: request.time < timestamp('2020-10-01T00:00:00.000Z') etag: BwWWja0YfJA= version: 3 ``` For a description of IAM and its features, see the [IAM documentation](https://cloud.google.com/iam/docs/).", "id": "Policy", "properties": {"auditConfigs": {"description": "Specifies cloud audit logging configuration for this policy.", "items": {"$ref": "AuditConfig"}, "type": "array"}, "bindings": {"description": "Associates a list of `members`, or principals, with a `role`. Optionally, may specify a `condition` that determines how and when the `bindings` are applied. Each of the `bindings` must contain at least one principal. The `bindings` in a `Policy` can refer to up to 1,500 principals; up to 250 of these principals can be Google groups. Each occurrence of a principal counts towards these limits. For example, if the `bindings` grant 50 different roles to `user:<EMAIL>`, and not to any other principal, then you can add another 1,450 principals to the `bindings` in the `Policy`.", "items": {"$ref": "Binding"}, "type": "array"}, "etag": {"description": "`etag` is used for optimistic concurrency control as a way to help prevent simultaneous updates of a policy from overwriting each other. It is strongly suggested that systems make use of the `etag` in the read-modify-write cycle to perform policy updates in order to avoid race conditions: An `etag` is returned in the response to `getIamPolicy`, and systems are expected to put that etag in the request to `setIamPolicy` to ensure that their change will be applied to the same version of the policy. **Important:** If you use IAM Conditions, you must include the `etag` field whenever you call `setIamPolicy`. If you omit this field, then IAM allows you to overwrite a version `3` policy with a version `1` policy, and all of the conditions in the version `3` policy are lost.", "format": "byte", "type": "string"}, "version": {"description": "Specifies the format of the policy. Valid values are `0`, `1`, and `3`. Requests that specify an invalid value are rejected. Any operation that affects conditional role bindings must specify version `3`. This requirement applies to the following operations: * Getting a policy that includes a conditional role binding * Adding a conditional role binding to a policy * Changing a conditional role binding in a policy * Removing any role binding, with or without a condition, from a policy that includes conditions **Important:** If you use IAM Conditions, you must include the `etag` field whenever you call `setIamPolicy`. If you omit this field, then IAM allows you to overwrite a version `3` policy with a version `1` policy, and all of the conditions in the version `3` policy are lost. If a policy does not include any conditions, operations on that policy may specify any valid version or leave the field unset. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies).", "format": "int32", "type": "integer"}}, "type": "object"}, "Position": {"description": "Issue position.", "id": "Position", "properties": {"column": {"description": "Issue column number", "format": "int32", "type": "integer"}, "length": {"description": "Issue length", "format": "int32", "type": "integer"}, "line": {"description": "Issue line number", "format": "int32", "type": "integer"}, "offset": {"description": "Issue offset", "format": "int32", "type": "integer"}}, "type": "object"}, "PostgreSqlConnectionProfile": {"description": "Specifies connection parameters required specifically for PostgreSQL databases.", "id": "PostgreSqlConnectionProfile", "properties": {"alloydbClusterId": {"description": "Optional. If the destination is an AlloyDB database, use this field to provide the AlloyDB cluster ID.", "type": "string"}, "cloudSqlId": {"description": "If the source is a Cloud SQL database, use this field to provide the Cloud SQL instance ID of the source.", "type": "string"}, "database": {"description": "Optional. The name of the specific database within the host.", "type": "string"}, "host": {"description": "Required. The IP or hostname of the source PostgreSQL database.", "type": "string"}, "networkArchitecture": {"description": "Output only. If the source is a Cloud SQL database, this field indicates the network architecture it's associated with.", "enum": ["NETWORK_ARCHITECTURE_UNSPECIFIED", "NETWORK_ARCHITECTURE_OLD_CSQL_PRODUCER", "NETWORK_ARCHITECTURE_NEW_CSQL_PRODUCER"], "enumDescriptions": ["", "Instance is in Cloud SQL's old producer network architecture.", "Instance is in Cloud SQL's new producer network architecture."], "readOnly": true, "type": "string"}, "password": {"description": "Required. Input only. The password for the user that Database Migration Service will be using to connect to the database. This field is not returned on request, and the value is encrypted when stored in Database Migration Service.", "type": "string"}, "passwordSet": {"description": "Output only. Indicates If this connection profile password is stored.", "readOnly": true, "type": "boolean"}, "port": {"description": "Required. The network port of the source PostgreSQL database.", "format": "int32", "type": "integer"}, "privateServiceConnectConnectivity": {"$ref": "PrivateServiceConnectConnectivity", "description": "Private service connect connectivity."}, "ssl": {"$ref": "SslConfig", "description": "SSL configuration for the destination to connect to the source database."}, "staticIpConnectivity": {"$ref": "StaticIpConnectivity", "description": "Static ip connectivity data (default, no additional details needed)."}, "username": {"description": "Required. The username that Database Migration Service will use to connect to the database. The value is encrypted when stored in Database Migration Service.", "type": "string"}}, "type": "object"}, "PostgresDestinationConfig": {"description": "Configuration for Postgres as a destination in a migration.", "id": "PostgresDestinationConfig", "properties": {"maxConcurrentConnections": {"description": "Optional. Maximum number of connections Database Migration Service will open to the destination for data migration.", "format": "int32", "type": "integer"}, "transactionTimeout": {"description": "Optional. Timeout for data migration transactions.", "format": "google-duration", "type": "string"}}, "type": "object"}, "PrimaryInstanceSettings": {"description": "Settings for the cluster's primary instance", "id": "PrimaryInstanceSettings", "properties": {"databaseFlags": {"additionalProperties": {"type": "string"}, "description": "Database flags to pass to AlloyDB when DMS is creating the AlloyDB cluster and instances. See the AlloyDB documentation for how these can be used.", "type": "object"}, "id": {"description": "Required. The ID of the AlloyDB primary instance. The ID must satisfy the regex expression \"[a-z0-9-]+\".", "type": "string"}, "instanceNetworkConfig": {"$ref": "InstanceNetworkConfig", "description": "Optional. Metadata related to instance level network configuration."}, "labels": {"additionalProperties": {"type": "string"}, "description": "Labels for the AlloyDB primary instance created by DMS. An object containing a list of 'key', 'value' pairs.", "type": "object"}, "machineConfig": {"$ref": "MachineConfig", "description": "Configuration for the machines that host the underlying database engine."}, "outboundPublicIpAddresses": {"description": "Output only. All outbound public IP addresses configured for the instance.", "items": {"type": "string"}, "readOnly": true, "type": "array"}, "privateIp": {"description": "Output only. The private IP address for the Instance. This is the connection endpoint for an end-user application.", "readOnly": true, "type": "string"}}, "type": "object"}, "PrivateConnection": {"description": "The PrivateConnection resource is used to establish private connectivity with the customer's network.", "id": "PrivateConnection", "properties": {"createTime": {"description": "Output only. The create time of the resource.", "format": "google-datetime", "readOnly": true, "type": "string"}, "displayName": {"description": "The private connection display name.", "type": "string"}, "error": {"$ref": "Status", "description": "Output only. The error details in case of state FAILED.", "readOnly": true}, "labels": {"additionalProperties": {"type": "string"}, "description": "The resource labels for private connections to use to annotate any related underlying resources such as Compute Engine VMs. An object containing a list of \"key\": \"value\" pairs. Example: `{ \"name\": \"wrench\", \"mass\": \"1.3kg\", \"count\": \"3\" }`.", "type": "object"}, "name": {"description": "The name of the resource.", "type": "string"}, "satisfiesPzi": {"description": "Output only. Reserved for future use.", "readOnly": true, "type": "boolean"}, "satisfiesPzs": {"description": "Output only. Reserved for future use.", "readOnly": true, "type": "boolean"}, "state": {"description": "Output only. The state of the private connection.", "enum": ["STATE_UNSPECIFIED", "CREATING", "CREATED", "FAILED", "DELETING", "FAILED_TO_DELETE", "DELETED"], "enumDescriptions": ["", "The private connection is in creation state - creating resources.", "The private connection has been created with all of its resources.", "The private connection creation has failed.", "The private connection is being deleted.", "Delete request has failed, resource is in invalid state.", "The private connection has been deleted."], "readOnly": true, "type": "string"}, "updateTime": {"description": "Output only. The last update time of the resource.", "format": "google-datetime", "readOnly": true, "type": "string"}, "vpcPeeringConfig": {"$ref": "VpcPeeringConfig", "description": "VPC peering configuration."}}, "type": "object"}, "PrivateConnectivity": {"description": "Private Connectivity.", "id": "PrivateConnectivity", "properties": {"privateConnection": {"description": "Required. The resource name (URI) of the private connection.", "type": "string"}}, "type": "object"}, "PrivateServiceConnectConnectivity": {"description": "[Private Service Connect connectivity](https://cloud.google.com/vpc/docs/private-service-connect#service-attachments)", "id": "PrivateServiceConnectConnectivity", "properties": {"serviceAttachment": {"description": "Required. A service attachment that exposes a database, and has the following format: projects/{project}/regions/{region}/serviceAttachments/{service_attachment_name}", "type": "string"}}, "type": "object"}, "PromoteMigrationJobRequest": {"description": "Request message for 'PromoteMigrationJob' request.", "id": "PromoteMigrationJobRequest", "properties": {"objectsFilter": {"$ref": "MigrationJobObjectsConfig", "description": "Optional. The object filter to apply to the migration job."}}, "type": "object"}, "RestartMigrationJobRequest": {"description": "Request message for 'RestartMigrationJob' request.", "id": "RestartMigrationJobRequest", "properties": {"objectsFilter": {"$ref": "MigrationJobObjectsConfig", "description": "Optional. The object filter to apply to the migration job."}, "restartFailedObjects": {"description": "Optional. If true, only failed objects will be restarted.", "type": "boolean"}, "skipValidation": {"description": "Optional. Restart the migration job without running prior configuration verification. Defaults to `false`.", "type": "boolean"}}, "type": "object"}, "ResumeMigrationJobRequest": {"description": "Request message for 'ResumeMigrationJob' request.", "id": "ResumeMigrationJobRequest", "properties": {"skipValidation": {"description": "Optional. Resume the migration job without running prior configuration verification. Defaults to `false`.", "type": "boolean"}}, "type": "object"}, "ReverseSshConnectivity": {"description": "The details needed to configure a reverse SSH tunnel between the source and destination databases. These details will be used when calling the generateSshScript method (see https://cloud.google.com/database-migration/docs/reference/rest/v1/projects.locations.migrationJobs/generateSshScript) to produce the script that will help set up the reverse SSH tunnel, and to set up the VPC peering between the Cloud SQL private network and the VPC.", "id": "ReverseSshConnectivity", "properties": {"vm": {"description": "The name of the virtual machine (Compute Engine) used as the bastion server for the SSH tunnel.", "type": "string"}, "vmIp": {"description": "Required. The IP of the virtual machine (Compute Engine) used as the bastion server for the SSH tunnel.", "type": "string"}, "vmPort": {"description": "Required. The forwarding port of the virtual machine (Compute Engine) used as the bastion server for the SSH tunnel.", "format": "int32", "type": "integer"}, "vpc": {"description": "The name of the VPC to peer with the Cloud SQL private network.", "type": "string"}}, "type": "object"}, "RollbackConversionWorkspaceRequest": {"description": "Request message for 'RollbackConversionWorkspace' request.", "id": "RollbackConversionWorkspaceRequest", "properties": {}, "type": "object"}, "RoundToScale": {"description": "This allows the data to change scale, for example if the source is 2 digits after the decimal point, specify round to scale value = 2. If for example the value needs to be converted to an integer, use round to scale value = 0.", "id": "RoundToScale", "properties": {"scale": {"description": "Required. Scale value to be used", "format": "int32", "type": "integer"}}, "type": "object"}, "RulesFile": {"description": "Details of a single rules file.", "id": "RulesFile", "properties": {"rulesContent": {"description": "Required. The text content of the rules that needs to be converted.", "type": "string"}, "rulesSourceFilename": {"description": "Required. The filename of the rules that needs to be converted. The filename is used mainly so that future logs of the import rules job contain it, and can therefore be searched by it.", "type": "string"}}, "type": "object"}, "SchemaEntity": {"description": "Schema typically has no parent entity, but can have a parent entity DatabaseInstance (for database engines which support it). For some database engines, the terms schema and user can be used interchangeably when they refer to a namespace or a collection of other database entities. Can store additional information which is schema specific.", "id": "SchemaEntity", "properties": {"customFeatures": {"additionalProperties": {"description": "Properties of the object.", "type": "any"}, "description": "Custom engine specific features.", "type": "object"}}, "type": "object"}, "SearchBackgroundJobsResponse": {"description": "Response message for 'SearchBackgroundJobs' request.", "id": "SearchBackgroundJobsResponse", "properties": {"jobs": {"description": "The list of conversion workspace mapping rules.", "items": {"$ref": "BackgroundJobLogEntry"}, "type": "array"}}, "type": "object"}, "SeedConversionWorkspaceRequest": {"description": "Request message for 'SeedConversionWorkspace' request.", "id": "SeedConversionWorkspaceRequest", "properties": {"autoCommit": {"description": "Should the conversion workspace be committed automatically after the seed operation.", "type": "boolean"}, "destinationConnectionProfile": {"description": "Optional. Fully qualified (Uri) name of the destination connection profile.", "type": "string"}, "sourceConnectionProfile": {"description": "Optional. Fully qualified (Uri) name of the source connection profile.", "type": "string"}}, "type": "object"}, "SeedJobDetails": {"description": "Details regarding a Seed background job.", "id": "SeedJobDetails", "properties": {"connectionProfile": {"description": "Output only. The connection profile which was used for the seed job.", "readOnly": true, "type": "string"}}, "type": "object"}, "SequenceEntity": {"description": "<PERSON><PERSON>'s parent is a schema.", "id": "SequenceEntity", "properties": {"cache": {"description": "Indicates number of entries to cache / precreate.", "format": "int64", "type": "string"}, "customFeatures": {"additionalProperties": {"description": "Properties of the object.", "type": "any"}, "description": "Custom engine specific features.", "type": "object"}, "cycle": {"description": "Indicates whether the sequence value should cycle through.", "type": "boolean"}, "increment": {"description": "Increment value for the sequence.", "format": "int64", "type": "string"}, "maxValue": {"description": "Maximum number for the sequence represented as bytes to accommodate large. numbers", "format": "byte", "type": "string"}, "minValue": {"description": "Minimum number for the sequence represented as bytes to accommodate large. numbers", "format": "byte", "type": "string"}, "startValue": {"description": "Start number for the sequence represented as bytes to accommodate large. numbers", "format": "byte", "type": "string"}}, "type": "object"}, "SetIamPolicyRequest": {"description": "Request message for `SetIamPolicy` method.", "id": "SetIamPolicyRequest", "properties": {"policy": {"$ref": "Policy", "description": "REQUIRED: The complete policy to be applied to the `resource`. The size of the policy is limited to a few 10s of KB. An empty policy is a valid policy but certain Google Cloud services (such as Projects) might reject them."}, "updateMask": {"description": "OPTIONAL: A FieldMask specifying which fields of the policy to modify. Only the fields in the mask will be modified. If no mask is provided, the following default mask is used: `paths: \"bindings, etag\"`", "format": "google-fieldmask", "type": "string"}}, "type": "object"}, "SetTablePrimaryKey": {"description": "Options to configure rule type SetTablePrimaryKey. The rule is used to specify the columns and name to configure/alter the primary key of a table. The rule filter field can refer to one entity. The rule scope can be one of: Table.", "id": "SetTablePrimaryKey", "properties": {"primaryKey": {"description": "Optional. Name for the primary key", "type": "string"}, "primaryKeyColumns": {"description": "Required. List of column names for the primary key", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "SingleColumnChange": {"description": "Options to configure rule type SingleColumnChange. The rule is used to change the properties of a column. The rule filter field can refer to one entity. The rule scope can be one of: Column. When using this rule, if a field is not specified than the destination column's configuration will be the same as the one in the source column..", "id": "SingleColumnChange", "properties": {"array": {"description": "Optional. Is the column of array type.", "type": "boolean"}, "arrayLength": {"description": "Optional. The length of the array, only relevant if the column type is an array.", "format": "int32", "type": "integer"}, "autoGenerated": {"description": "Optional. Is the column auto-generated/identity.", "type": "boolean"}, "charset": {"description": "Optional. Charset override - instead of table level charset.", "type": "string"}, "collation": {"description": "Optional. Collation override - instead of table level collation.", "type": "string"}, "comment": {"description": "Optional. Comment associated with the column.", "type": "string"}, "customFeatures": {"additionalProperties": {"description": "Properties of the object.", "type": "any"}, "description": "Optional. Custom engine specific features.", "type": "object"}, "dataType": {"description": "Optional. Column data type name.", "type": "string"}, "fractionalSecondsPrecision": {"description": "Optional. Column fractional seconds precision - e.g. 2 as in timestamp (2) - when relevant.", "format": "int32", "type": "integer"}, "length": {"description": "Optional. Column length - e.g. 50 as in varchar (50) - when relevant.", "format": "int64", "type": "string"}, "nullable": {"description": "Optional. Is the column nullable.", "type": "boolean"}, "precision": {"description": "Optional. Column precision - e.g. 8 as in double (8,2) - when relevant.", "format": "int32", "type": "integer"}, "scale": {"description": "Optional. Column scale - e.g. 2 as in double (8,2) - when relevant.", "format": "int32", "type": "integer"}, "setValues": {"description": "Optional. Specifies the list of values allowed in the column.", "items": {"type": "string"}, "type": "array"}, "udt": {"description": "Optional. Is the column a UDT (User-defined Type).", "type": "boolean"}}, "type": "object"}, "SingleEntityRename": {"description": "Options to configure rule type SingleEntityRename. The rule is used to rename an entity. The rule filter field can refer to only one entity. The rule scope can be one of: Database, Schema, Table, Column, Constraint, Index, View, Function, Stored Procedure, Materialized View, Sequence, UDT, Synonym", "id": "SingleEntityRename", "properties": {"newName": {"description": "Required. The new name of the destination entity", "type": "string"}}, "type": "object"}, "SinglePackageChange": {"description": "Options to configure rule type SinglePackageChange. The rule is used to alter the sql code for a package entities. The rule filter field can refer to one entity. The rule scope can be: Package", "id": "SinglePackageChange", "properties": {"packageBody": {"description": "Optional. Sql code for package body", "type": "string"}, "packageDescription": {"description": "Optional. Sql code for package description", "type": "string"}}, "type": "object"}, "SourceNumericFilter": {"description": "Filter for fixed point number data types such as NUMERIC/NUMBER", "id": "SourceNumericFilter", "properties": {"numericFilterOption": {"description": "Required. <PERSON>um to set the option defining the datatypes numeric filter has to be applied to", "enum": ["NUMERIC_FILTER_OPTION_UNSPECIFIED", "NUMERIC_FILTER_OPTION_ALL", "NUMERIC_FILTER_OPTION_LIMIT", "NUMERIC_FILTER_OPTION_LIMITLESS"], "enumDescriptions": ["Numeric filter option unspecified", "Numeric filter option that matches all numeric columns.", "Numeric filter option that matches columns having numeric datatypes with specified precision and scale within the limited range of filter.", "Numeric filter option that matches only the numeric columns with no precision and scale specified."], "type": "string"}, "sourceMaxPrecisionFilter": {"description": "Optional. The filter will match columns with precision smaller than or equal to this number.", "format": "int32", "type": "integer"}, "sourceMaxScaleFilter": {"description": "Optional. The filter will match columns with scale smaller than or equal to this number.", "format": "int32", "type": "integer"}, "sourceMinPrecisionFilter": {"description": "Optional. The filter will match columns with precision greater than or equal to this number.", "format": "int32", "type": "integer"}, "sourceMinScaleFilter": {"description": "Optional. The filter will match columns with scale greater than or equal to this number.", "format": "int32", "type": "integer"}}, "type": "object"}, "SourceObjectConfig": {"description": "Config for a single migration job object.", "id": "SourceObjectConfig", "properties": {"objectIdentifier": {"$ref": "SourceObjectIdentifier", "description": "Optional. The object identifier."}}, "type": "object"}, "SourceObjectIdentifier": {"description": "An identifier for the Migration Job Object.", "id": "SourceObjectIdentifier", "properties": {"database": {"description": "Optional. The database name. This will be required only if the object uses a database name as part of its unique identifier.", "type": "string"}, "schema": {"description": "Optional. The schema name. This will be required only if the object uses a schema name as part of its unique identifier.", "type": "string"}, "table": {"description": "Optional. The table name. This will be required only if the object is a level below database or schema.", "type": "string"}, "type": {"description": "Required. The type of the migration job object.", "enum": ["MIGRATION_JOB_OBJECT_TYPE_UNSPECIFIED", "DATABASE", "SCHEMA", "TABLE"], "enumDescriptions": ["The type of the migration job object is unknown.", "The migration job object is a database.", "The migration job object is a schema.", "The migration job object is a table."], "type": "string"}}, "type": "object"}, "SourceObjectsConfig": {"description": "List of configurations for the source objects to be migrated.", "id": "SourceObjectsConfig", "properties": {"objectConfigs": {"description": "Optional. The list of the objects to be migrated.", "items": {"$ref": "SourceObjectConfig"}, "type": "array"}, "objectsSelectionType": {"description": "Optional. The objects selection type of the migration job.", "enum": ["OBJECTS_SELECTION_TYPE_UNSPECIFIED", "ALL_OBJECTS", "SPECIFIED_OBJECTS"], "enumDescriptions": ["The type of the objects selection is unknown, indicating that the migration job is at instance level.", "Migrate all of the objects.", "Migrate specific objects."], "type": "string"}}, "type": "object"}, "SourceSqlChange": {"description": "Options to configure rule type SourceSqlChange. The rule is used to alter the sql code for database entities. The rule filter field can refer to one entity. The rule scope can be: StoredProcedure, Function, Trigger, View", "id": "SourceSqlChange", "properties": {"sqlCode": {"description": "Required. Sql code for source (stored procedure, function, trigger or view)", "type": "string"}}, "type": "object"}, "SourceTextFilter": {"description": "Filter for text-based data types like varchar.", "id": "SourceTextFilter", "properties": {"sourceMaxLengthFilter": {"description": "Optional. The filter will match columns with length smaller than or equal to this number.", "format": "int64", "type": "string"}, "sourceMinLengthFilter": {"description": "Optional. The filter will match columns with length greater than or equal to this number.", "format": "int64", "type": "string"}}, "type": "object"}, "SqlAclEntry": {"description": "An entry for an Access Control list.", "id": "SqlAclEntry", "properties": {"expireTime": {"description": "The time when this access control entry expires in [RFC 3339](https://tools.ietf.org/html/rfc3339) format, for example: `2012-11-15T16:19:00.094Z`.", "format": "google-datetime", "type": "string"}, "label": {"description": "A label to identify this entry.", "type": "string"}, "ttl": {"description": "Input only. The time-to-leave of this access control entry.", "format": "google-duration", "type": "string"}, "value": {"description": "The allowlisted value for the access control list.", "type": "string"}}, "type": "object"}, "SqlIpConfig": {"description": "IP Management configuration.", "id": "SqlIpConfig", "properties": {"allocatedIpRange": {"description": "Optional. The name of the allocated IP address range for the private IP Cloud SQL instance. This name refers to an already allocated IP range address. If set, the instance IP address will be created in the allocated range. Note that this IP address range can't be modified after the instance is created. If you change the VPC when configuring connectivity settings for the migration job, this field is not relevant.", "type": "string"}, "authorizedNetworks": {"description": "The list of external networks that are allowed to connect to the instance using the IP. See https://en.wikipedia.org/wiki/CIDR_notation#CIDR_notation, also known as 'slash' notation (e.g. `*************/24`).", "items": {"$ref": "SqlAclEntry"}, "type": "array"}, "enableIpv4": {"description": "Whether the instance should be assigned an IPv4 address or not.", "type": "boolean"}, "privateNetwork": {"description": "The resource link for the VPC network from which the Cloud SQL instance is accessible for private IP. For example, `projects/myProject/global/networks/default`. This setting can be updated, but it cannot be removed after it is set.", "type": "string"}, "requireSsl": {"description": "Whether SSL connections over IP should be enforced or not.", "type": "boolean"}}, "type": "object"}, "SqlServerBackups": {"description": "Specifies the backup details in Cloud Storage for homogeneous migration to Cloud SQL for SQL Server.", "id": "SqlServerBackups", "properties": {"gcsBucket": {"description": "Required. The Cloud Storage bucket that stores backups for all replicated databases.", "type": "string"}, "gcsPrefix": {"description": "Optional. Cloud Storage path inside the bucket that stores backups.", "type": "string"}}, "type": "object"}, "SqlServerConnectionProfile": {"description": "Specifies connection parameters required specifically for SQL Server databases.", "id": "SqlServerConnectionProfile", "properties": {"backups": {"$ref": "SqlServerBackups", "description": "The backup details in Cloud Storage for homogeneous migration to Cloud SQL for SQL Server."}, "cloudSqlId": {"description": "If the source is a Cloud SQL database, use this field to provide the Cloud SQL instance ID of the source.", "type": "string"}, "database": {"description": "Required. The name of the specific database within the host.", "type": "string"}, "forwardSshConnectivity": {"$ref": "ForwardSshTunnelConnectivity", "description": "Forward SSH tunnel connectivity."}, "host": {"description": "Required. The IP or hostname of the source SQL Server database.", "type": "string"}, "password": {"description": "Required. Input only. The password for the user that Database Migration Service will be using to connect to the database. This field is not returned on request, and the value is encrypted when stored in Database Migration Service.", "type": "string"}, "passwordSet": {"description": "Output only. Indicates whether a new password is included in the request.", "readOnly": true, "type": "boolean"}, "port": {"description": "Required. The network port of the source SQL Server database.", "format": "int32", "type": "integer"}, "privateConnectivity": {"$ref": "PrivateConnectivity", "description": "Private connectivity."}, "privateServiceConnectConnectivity": {"$ref": "PrivateServiceConnectConnectivity", "description": "Private Service Connect connectivity."}, "ssl": {"$ref": "SslConfig", "description": "SSL configuration for the destination to connect to the source database."}, "staticIpConnectivity": {"$ref": "StaticIpConnectivity", "description": "Static IP connectivity data (default, no additional details needed)."}, "username": {"description": "Required. The username that Database Migration Service will use to connect to the database. The value is encrypted when stored in Database Migration Service.", "type": "string"}}, "type": "object"}, "SqlServerDatabaseBackup": {"description": "Specifies the backup details for a single database in Cloud Storage for homogeneous migration to Cloud SQL for SQL Server.", "id": "SqlServerDatabaseBackup", "properties": {"database": {"description": "Required. Name of a SQL Server database for which to define backup configuration.", "type": "string"}, "encryptionOptions": {"$ref": "SqlServerEncryptionOptions", "description": "Optional. Encryption settings for the database. Required if provided database backups are encrypted. Encryption settings include path to certificate, path to certificate private key, and key password."}}, "type": "object"}, "SqlServerEncryptionOptions": {"description": "Encryption settings for the SQL Server database.", "id": "SqlServerEncryptionOptions", "properties": {"certPath": {"description": "Required. Path to the Certificate (.cer) in Cloud Storage, in the form `gs://bucketName/fileName`. The instance must have write permissions to the bucket and read access to the file.", "type": "string"}, "pvkPassword": {"description": "Required. Input only. Password that encrypts the private key.", "type": "string"}, "pvkPath": {"description": "Required. Path to the Certificate Private Key (.pvk) in Cloud Storage, in the form `gs://bucketName/fileName`. The instance must have write permissions to the bucket and read access to the file.", "type": "string"}}, "type": "object"}, "SqlServerHomogeneousMigrationJobConfig": {"description": "Configuration for homogeneous migration to Cloud SQL for SQL Server.", "id": "SqlServerHomogeneousMigrationJobConfig", "properties": {"backupFilePattern": {"description": "Required. Pattern that describes the default backup naming strategy. The specified pattern should ensure lexicographical order of backups. The pattern must define one of the following capture group sets: Capture group set #1 yy/yyyy - year, 2 or 4 digits mm - month number, 1-12 dd - day of month, 1-31 hh - hour of day, 00-23 mi - minutes, 00-59 ss - seconds, 00-59 Example: For backup file TestDB_20230802_155400.trn, use pattern: (?.*)_backup_(?\\d{4})(?\\d{2})(?\\d{2})_(?\\d{2})(?\\d{2})(?\\d{2}).trn Capture group set #2 timestamp - unix timestamp Example: For backup file TestDB.1691448254.trn, use pattern: (?.*)\\.(?\\d*).trn or (?.*)\\.(?\\d*).trn", "type": "string"}, "databaseBackups": {"description": "Required. Backup details per database in Cloud Storage.", "items": {"$ref": "SqlServerDatabaseBackup"}, "type": "array"}, "promoteWhenReady": {"description": "Optional. Promote databases when ready.", "type": "boolean"}, "useDiffBackup": {"description": "Optional. Enable differential backups.", "type": "boolean"}}, "type": "object"}, "SqlServerSourceConfig": {"description": "Configuration for SQL Server as a source in a migration.", "id": "SqlServerSourceConfig", "properties": {"cdcStartPosition": {"description": "Optional. The log sequence number (LSN) to start CDC data migration from.", "type": "string"}, "maxConcurrentCdcConnections": {"description": "Optional. Maximum number of connections Database Migration Service will open to the source for CDC phase.", "format": "int32", "type": "integer"}, "maxConcurrentFullDumpConnections": {"description": "Optional. Maximum number of connections Database Migration Service will open to the source for full dump phase.", "format": "int32", "type": "integer"}, "skipFullDump": {"description": "Optional. Whether to skip full dump or not.", "type": "boolean"}}, "type": "object"}, "SqlServerToPostgresConfig": {"description": "Configuration for heterogeneous **SQL Server to Cloud SQL for PostgreSQL** migrations.", "id": "SqlServerToPostgresConfig", "properties": {"postgresDestinationConfig": {"$ref": "PostgresDestinationConfig", "description": "Optional. Configuration for Postgres destination."}, "sqlserverSourceConfig": {"$ref": "SqlServerSourceConfig", "description": "Optional. Configuration for SQL Server source."}}, "type": "object"}, "SshScript": {"description": "Response message for 'GenerateSshScript' request.", "id": "SshScript", "properties": {"script": {"description": "The ssh configuration script.", "type": "string"}}, "type": "object"}, "SslConfig": {"description": "SSL configuration information.", "id": "SslConfig", "properties": {"caCertificate": {"description": "Required. Input only. The x509 PEM-encoded certificate of the CA that signed the source database server's certificate. The replica will use this certificate to verify it's connecting to the right host.", "type": "string"}, "clientCertificate": {"description": "Input only. The x509 PEM-encoded certificate that will be used by the replica to authenticate against the source database server.If this field is used then the 'client_key' field is mandatory.", "type": "string"}, "clientKey": {"description": "Input only. The unencrypted PKCS#1 or PKCS#8 PEM-encoded private key associated with the Client Certificate. If this field is used then the 'client_certificate' field is mandatory.", "type": "string"}, "sslFlags": {"additionalProperties": {"type": "string"}, "description": "Optional. SSL flags used for establishing SSL connection to the source database. Only source specific flags are supported. An object containing a list of \"key\": \"value\" pairs. Example: { \"server_certificate_hostname\": \"server.com\"}.", "type": "object"}, "type": {"description": "Optional. The ssl config type according to 'client_key', 'client_certificate' and 'ca_certificate'.", "enum": ["SSL_TYPE_UNSPECIFIED", "SERVER_ONLY", "SERVER_CLIENT", "REQUIRED", "NONE"], "enumDescriptions": ["Unspecified.", "Only 'ca_certificate' specified.", "Both server ('ca_certificate'), and client ('client_key', 'client_certificate') specified.", "Mandates SSL encryption for all connections. This doesn’t require certificate verification.", "Connection is not encrypted."], "type": "string"}}, "type": "object"}, "StartMigrationJobRequest": {"description": "Request message for 'StartMigrationJob' request.", "id": "StartMigrationJobRequest", "properties": {"skipValidation": {"description": "Optional. Start the migration job without running prior configuration verification. Defaults to `false`.", "type": "boolean"}}, "type": "object"}, "StaticIpConnectivity": {"description": "The source database will allow incoming connections from the public IP of the destination database. You can retrieve the public IP of the Cloud SQL instance from the Cloud SQL console or using Cloud SQL APIs. No additional configuration is required.", "id": "StaticIpConnectivity", "properties": {}, "type": "object"}, "StaticServiceIpConnectivity": {"description": "Static IP address connectivity configured on service project.", "id": "StaticServiceIpConnectivity", "properties": {}, "type": "object"}, "Status": {"description": "The `Status` type defines a logical error model that is suitable for different programming environments, including REST APIs and RPC APIs. It is used by [gRPC](https://github.com/grpc). Each `Status` message contains three pieces of data: error code, error message, and error details. You can find out more about this error model and how to work with it in the [API Design Guide](https://cloud.google.com/apis/design/errors).", "id": "Status", "properties": {"code": {"description": "The status code, which should be an enum value of google.rpc.Code.", "format": "int32", "type": "integer"}, "details": {"description": "A list of messages that carry the error details. There is a common set of message types for APIs to use.", "items": {"additionalProperties": {"description": "Properties of the object. Contains field @type with type URL.", "type": "any"}, "type": "object"}, "type": "array"}, "message": {"description": "A developer-facing error message, which should be in English. Any user-facing error message should be localized and sent in the google.rpc.Status.details field, or localized by the client.", "type": "string"}}, "type": "object"}, "StopMigrationJobRequest": {"description": "Request message for 'StopMigrationJob' request.", "id": "StopMigrationJobRequest", "properties": {}, "type": "object"}, "StoredProcedureEntity": {"description": "Stored procedure's parent is a schema.", "id": "StoredProcedureEntity", "properties": {"customFeatures": {"additionalProperties": {"description": "Properties of the object.", "type": "any"}, "description": "Custom engine specific features.", "type": "object"}, "sqlCode": {"description": "The SQL code which creates the stored procedure.", "type": "string"}}, "type": "object"}, "SynonymEntity": {"description": "Synonym's parent is a schema.", "id": "SynonymEntity", "properties": {"customFeatures": {"additionalProperties": {"description": "Properties of the object.", "type": "any"}, "description": "Custom engine specific features.", "type": "object"}, "sourceEntity": {"description": "The name of the entity for which the synonym is being created (the source).", "type": "string"}, "sourceType": {"description": "The type of the entity for which the synonym is being created (usually a table or a sequence).", "enum": ["DATABASE_ENTITY_TYPE_UNSPECIFIED", "DATABASE_ENTITY_TYPE_SCHEMA", "DATABASE_ENTITY_TYPE_TABLE", "DATABASE_ENTITY_TYPE_COLUMN", "DATABASE_ENTITY_TYPE_CONSTRAINT", "DATABASE_ENTITY_TYPE_INDEX", "DATABASE_ENTITY_TYPE_TRIGGER", "DATABASE_ENTITY_TYPE_VIEW", "DATABASE_ENTITY_TYPE_SEQUENCE", "DATABASE_ENTITY_TYPE_STORED_PROCEDURE", "DATABASE_ENTITY_TYPE_FUNCTION", "DATABASE_ENTITY_TYPE_SYNONYM", "DATABASE_ENTITY_TYPE_DATABASE_PACKAGE", "DATABASE_ENTITY_TYPE_UDT", "DATABASE_ENTITY_TYPE_MATERIALIZED_VIEW", "DATABASE_ENTITY_TYPE_DATABASE"], "enumDescriptions": ["Unspecified database entity type.", "Schema.", "Table.", "Column.", "Constraint.", "Index.", "Trigger.", "View.", "Sequence.", "Stored Procedure.", "Function.", "Synonym.", "Package.", "UDT.", "Materialized View.", "Database."], "type": "string"}}, "type": "object"}, "TableEntity": {"description": "<PERSON>'s parent is a schema.", "id": "TableEntity", "properties": {"columns": {"description": "Table columns.", "items": {"$ref": "ColumnEntity"}, "type": "array"}, "comment": {"description": "Comment associated with the table.", "type": "string"}, "constraints": {"description": "Table constraints.", "items": {"$ref": "ConstraintEntity"}, "type": "array"}, "customFeatures": {"additionalProperties": {"description": "Properties of the object.", "type": "any"}, "description": "Custom engine specific features.", "type": "object"}, "indices": {"description": "Table indices.", "items": {"$ref": "IndexEntity"}, "type": "array"}, "triggers": {"description": "Table triggers.", "items": {"$ref": "Trigger<PERSON><PERSON>ty"}, "type": "array"}}, "type": "object"}, "TcpProxyScript": {"description": "Response message for 'GenerateTcpProxyScript' request.", "id": "TcpProxyScript", "properties": {"script": {"description": "The TCP Proxy configuration script.", "type": "string"}}, "type": "object"}, "TestIamPermissionsRequest": {"description": "Request message for `TestIamPermissions` method.", "id": "TestIamPermissionsRequest", "properties": {"permissions": {"description": "The set of permissions to check for the `resource`. Permissions with wildcards (such as `*` or `storage.*`) are not allowed. For more information see [IAM Overview](https://cloud.google.com/iam/docs/overview#permissions).", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "TestIamPermissionsResponse": {"description": "Response message for `TestIamPermissions` method.", "id": "TestIamPermissionsResponse", "properties": {"permissions": {"description": "A subset of `TestPermissionsRequest.permissions` that the caller is allowed.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "TriggerEntity": {"description": "Trigger is not used as an independent entity, it is retrieved as part of a Table entity.", "id": "Trigger<PERSON><PERSON>ty", "properties": {"customFeatures": {"additionalProperties": {"description": "Properties of the object.", "type": "any"}, "description": "Custom engine specific features.", "type": "object"}, "name": {"description": "The name of the trigger.", "type": "string"}, "sqlCode": {"description": "The SQL code which creates the trigger.", "type": "string"}, "triggerType": {"description": "Indicates when the trigger fires, for example BEFORE STATEMENT, AFTER EACH ROW.", "type": "string"}, "triggeringEvents": {"description": "The DML, DDL, or database events that fire the trigger, for example INSERT, UPDATE.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "UDTEntity": {"description": "UDT's parent is a schema.", "id": "UDTEntity", "properties": {"customFeatures": {"additionalProperties": {"description": "Properties of the object.", "type": "any"}, "description": "Custom engine specific features.", "type": "object"}, "udtBody": {"description": "The SQL code which creates the udt body.", "type": "string"}, "udtSqlCode": {"description": "The SQL code which creates the udt.", "type": "string"}}, "type": "object"}, "UserPassword": {"description": "The username/password for a database user. Used for specifying initial users at cluster creation time.", "id": "UserPassword", "properties": {"password": {"description": "The initial password for the user.", "type": "string"}, "passwordSet": {"description": "Output only. Indicates if the initial_user.password field has been set.", "readOnly": true, "type": "boolean"}, "user": {"description": "The database username.", "type": "string"}}, "type": "object"}, "ValueListFilter": {"description": "A list of values to filter by in ConditionalColumnSetValue", "id": "ValueListFilter", "properties": {"ignoreCase": {"description": "Required. Whether to ignore case when filtering by values. Defaults to false", "type": "boolean"}, "valuePresentList": {"description": "Required. Indicates whether the filter matches rows with values that are present in the list or those with values not present in it.", "enum": ["VALUE_PRESENT_IN_LIST_UNSPECIFIED", "VALUE_PRESENT_IN_LIST_IF_VALUE_LIST", "VALUE_PRESENT_IN_LIST_IF_VALUE_NOT_LIST"], "enumDescriptions": ["Value present in list unspecified", "If the source value is in the supplied list at value_list", "If the source value is not in the supplied list at value_list"], "type": "string"}, "values": {"description": "Required. The list to be used to filter by", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "ValueTransformation": {"description": "Description of data transformation during migration as part of the ConditionalColumnSetValue.", "id": "ValueTransformation", "properties": {"applyHash": {"$ref": "ApplyHash", "description": "Optional. Applies a hash function on the data"}, "assignMaxValue": {"$ref": "Empty", "description": "Optional. Set to max_value - if integer or numeric, will use int.maxvalue, etc"}, "assignMinValue": {"$ref": "Empty", "description": "Optional. Set to min_value - if integer or numeric, will use int.minvalue, etc"}, "assignNull": {"$ref": "Empty", "description": "Optional. Set to null"}, "assignSpecificValue": {"$ref": "AssignSpecificValue", "description": "Optional. Set to a specific value (value is converted to fit the target data type)"}, "doubleComparison": {"$ref": "DoubleComparisonFilter", "description": "Optional. Filter on relation between source value and compare value of type double."}, "intComparison": {"$ref": "IntComparisonFilter", "description": "Optional. Filter on relation between source value and compare value of type integer."}, "isNull": {"$ref": "Empty", "description": "Optional. Value is null"}, "roundScale": {"$ref": "RoundToScale", "description": "Optional. Allows the data to change scale"}, "valueList": {"$ref": "ValueListFilter", "description": "Optional. Value is found in the specified list."}}, "type": "object"}, "VerifyMigrationJobRequest": {"description": "Request message for 'VerifyMigrationJob' request.", "id": "VerifyMigrationJobRequest", "properties": {"migrationJob": {"$ref": "<PERSON><PERSON><PERSON><PERSON>", "description": "Optional. The changed migration job parameters to verify. It will not update the migration job."}, "updateMask": {"description": "Optional. Field mask is used to specify the changed fields to be verified. It will not update the migration job.", "format": "google-fieldmask", "type": "string"}}, "type": "object"}, "ViewEntity": {"description": "<PERSON>'s parent is a schema.", "id": "ViewEntity", "properties": {"constraints": {"description": "View constraints.", "items": {"$ref": "ConstraintEntity"}, "type": "array"}, "customFeatures": {"additionalProperties": {"description": "Properties of the object.", "type": "any"}, "description": "Custom engine specific features.", "type": "object"}, "sqlCode": {"description": "The SQL code which creates the view.", "type": "string"}}, "type": "object"}, "VmCreationConfig": {"description": "VM creation configuration message", "id": "VmCreationConfig", "properties": {"subnet": {"description": "The subnet name the vm needs to be created in.", "type": "string"}, "vmMachineType": {"description": "Required. VM instance machine type to create.", "type": "string"}, "vmZone": {"description": "The Google Cloud Platform zone to create the VM in.", "type": "string"}}, "type": "object"}, "VmSelectionConfig": {"description": "VM selection configuration message", "id": "VmSelectionConfig", "properties": {"vmZone": {"description": "Required. The Google Cloud Platform zone the VM is located.", "type": "string"}}, "type": "object"}, "VpcPeeringConfig": {"description": "The VPC peering configuration is used to create VPC peering with the consumer's VPC.", "id": "VpcPeeringConfig", "properties": {"subnet": {"description": "Required. A free subnet for peering. (CIDR of /29)", "type": "string"}, "vpcName": {"description": "Required. Fully qualified name of the VPC that Database Migration Service will peer to.", "type": "string"}}, "type": "object"}, "VpcPeeringConnectivity": {"description": "The details of the VPC where the source database is located in Google Cloud. We will use this information to set up the VPC peering connection between Cloud SQL and this VPC.", "id": "VpcPeeringConnectivity", "properties": {"vpc": {"description": "The name of the VPC network to peer with the Cloud SQL private network.", "type": "string"}}, "type": "object"}}, "servicePath": "", "title": "Database Migration API", "version": "v1", "version_module": true}