{% extends "base.html" %}

{% block title %}Inscription - SABTRANS{% endblock %}

{% block content %}
<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-8 col-lg-6">
            <div class="card shadow">
                <div class="card-header bg-success text-white text-center py-3">
                    <h3 class="mb-0">
                        <i class="fas fa-user-plus me-2"></i>Inscription
                    </h3>
                </div>
                <div class="card-body p-4">
                    <form method="POST">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="first_name" class="form-label">
                                    <i class="fas fa-user me-1"></i>Prénom *
                                </label>
                                <input type="text" class="form-control" id="first_name" name="first_name" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="last_name" class="form-label">
                                    <i class="fas fa-user me-1"></i>Nom *
                                </label>
                                <input type="text" class="form-control" id="last_name" name="last_name" required>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="email" class="form-label">
                                <i class="fas fa-envelope me-1"></i>Email *
                            </label>
                            <input type="email" class="form-control" id="email" name="email" required>
                        </div>
                        
                        <div class="mb-3">
                            <label for="username" class="form-label">
                                <i class="fas fa-at me-1"></i>Nom d'utilisateur *
                            </label>
                            <input type="text" class="form-control" id="username" name="username" required>
                            <div class="form-text">Au moins 3 caractères</div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="phone" class="form-label">
                                <i class="fas fa-phone me-1"></i>Téléphone
                            </label>
                            <input type="tel" class="form-control" id="phone" name="phone">
                        </div>
                        
                        <div class="mb-3">
                            <label for="user_type" class="form-label">
                                <i class="fas fa-briefcase me-1"></i>Type de compte *
                            </label>
                            <select class="form-select" id="user_type" name="user_type" required>
                                <option value="">Sélectionnez votre profil</option>
                                <option value="transporteur">Transporteur</option>
                                <option value="expediteur">Expéditeur</option>
                                <option value="chauffeur">Chauffeur</option>
                            </select>
                        </div>
                        
                        <div class="mb-3">
                            <label for="company_name" class="form-label">
                                <i class="fas fa-building me-1"></i>Nom de l'entreprise
                            </label>
                            <input type="text" class="form-control" id="company_name" name="company_name">
                            <div class="form-text">Optionnel pour les chauffeurs</div>
                        </div>

                        <!-- Localisation -->
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="country_id" class="form-label">
                                    <i class="fas fa-globe me-1"></i>Pays *
                                </label>
                                <select class="form-select" id="country_id" name="country_id" required>
                                    <option value="">Sélectionnez votre pays</option>
                                </select>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="department_id" class="form-label">
                                    <i class="fas fa-map-marker-alt me-1"></i><span id="department_label">Département</span> *
                                </label>
                                <select class="form-select" id="department_id" name="department_id" required disabled>
                                    <option value="">Sélectionnez d'abord un pays</option>
                                </select>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="city" class="form-label">
                                <i class="fas fa-city me-1"></i>Ville
                            </label>
                            <input type="text" class="form-control" id="city" name="city" placeholder="Paris, Alger, Casablanca...">
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="password" class="form-label">
                                    <i class="fas fa-lock me-1"></i>Mot de passe *
                                </label>
                                <input type="password" class="form-control" id="password" name="password" required>
                                <div class="form-text">
                                    8 caractères min, avec majuscule, minuscule et chiffre
                                </div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="confirm_password" class="form-label">
                                    <i class="fas fa-lock me-1"></i>Confirmer le mot de passe *
                                </label>
                                <input type="password" class="form-control" id="confirm_password" name="confirm_password" required>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="preferred_currency_id" class="form-label">
                                    <i class="fas fa-coins me-1"></i>Devise préférée
                                </label>
                                <select class="form-select" id="preferred_currency_id" name="preferred_currency_id">
                                    <option value="">Sélectionnez votre devise</option>
                                </select>
                                <div class="form-text">
                                    <i class="fas fa-info-circle me-1"></i>
                                    Devise utilisée par défaut pour vos transactions
                                </div>
                            </div>
                        </div>

                        <div class="mb-3 form-check">
                            <input type="checkbox" class="form-check-input" id="terms" required>
                            <label class="form-check-label" for="terms">
                                J'accepte les <a href="#" class="text-decoration-none">conditions d'utilisation</a> 
                                et la <a href="#" class="text-decoration-none">politique de confidentialité</a>
                            </label>
                        </div>
                        
                        <div class="d-grid">
                            <button type="submit" class="btn btn-success btn-lg">
                                <i class="fas fa-user-plus me-2"></i>Créer mon compte
                            </button>
                        </div>
                    </form>
                </div>
                <div class="card-footer text-center py-3">
                    <p class="mb-0">
                        Déjà un compte ? 
                        <a href="{{ url_for('auth.login') }}" class="text-decoration-none">
                            <i class="fas fa-sign-in-alt me-1"></i>Se connecter
                        </a>
                    </p>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="{{ url_for('static', filename='js/location.js') }}"></script>
<script src="{{ url_for('static', filename='js/currency.js') }}"></script>
{% endblock %}
