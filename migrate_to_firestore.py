#!/usr/bin/env python3
"""
Script de migration des données MySQL vers Firestore
Transfère toutes les données de SABTRANS vers Firebase
"""

import sys
import os
import json
from datetime import datetime

# Ajouter le répertoire courant au path Python
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Imports pour l'application existante
from app import create_app
from database import db
from models.user import User
from models.freight import FreightOffer, FreightProposal, Mission
from models.location import Country, Department, Currency

# Imports Firebase
import firebase_admin
from firebase_admin import credentials, firestore

def init_firebase():
    """Initialiser Firebase Admin SDK"""
    try:
        # Utiliser les credentials par défaut ou un fichier de service
        if not firebase_admin._apps:
            # En production, utiliser les credentials par défaut
            cred = credentials.ApplicationDefault()
            firebase_admin.initialize_app(cred)
        
        return firestore.client()
    except Exception as e:
        print(f"❌ Erreur lors de l'initialisation Firebase: {e}")
        print("💡 Assurez-vous d'avoir configuré les credentials Firebase")
        return None

def migrate_currencies(db_firestore, app):
    """Migrer les devises vers Firestore"""
    print("💰 Migration des devises...")
    
    with app.app_context():
        try:
            currencies = Currency.query.all()
            batch = db_firestore.batch()
            
            for currency in currencies:
                doc_ref = db_firestore.collection('currencies').document(str(currency.id))
                currency_data = {
                    'code': currency.code,
                    'name': currency.name,
                    'symbol': currency.symbol,
                    'created_at': datetime.utcnow()
                }
                batch.set(doc_ref, currency_data)
            
            batch.commit()
            print(f"✅ {len(currencies)} devises migrées")
            return True
            
        except Exception as e:
            print(f"❌ Erreur lors de la migration des devises: {e}")
            return False

def migrate_countries(db_firestore, app):
    """Migrer les pays vers Firestore"""
    print("🌍 Migration des pays...")
    
    with app.app_context():
        try:
            countries = Country.query.all()
            batch = db_firestore.batch()
            
            for country in countries:
                doc_ref = db_firestore.collection('countries').document(str(country.id))
                country_data = {
                    'code': country.code,
                    'name': country.name,
                    'currency_id': country.currency_id,
                    'subdivision_type': country.subdivision_type,
                    'created_at': datetime.utcnow()
                }
                batch.set(doc_ref, country_data)
            
            batch.commit()
            print(f"✅ {len(countries)} pays migrés")
            return True
            
        except Exception as e:
            print(f"❌ Erreur lors de la migration des pays: {e}")
            return False

def migrate_departments(db_firestore, app):
    """Migrer les départements vers Firestore"""
    print("🏛️ Migration des départements...")
    
    with app.app_context():
        try:
            departments = Department.query.all()
            
            # Traiter par lots de 500 (limite Firestore)
            batch_size = 500
            total_migrated = 0
            
            for i in range(0, len(departments), batch_size):
                batch = db_firestore.batch()
                batch_departments = departments[i:i + batch_size]
                
                for dept in batch_departments:
                    doc_ref = db_firestore.collection('departments').document(str(dept.id))
                    dept_data = {
                        'country_id': dept.country_id,
                        'code': dept.code,
                        'name_fr': dept.name_fr,
                        'type': dept.type,
                        'created_at': datetime.utcnow()
                    }
                    batch.set(doc_ref, dept_data)
                
                batch.commit()
                total_migrated += len(batch_departments)
                print(f"   📦 {total_migrated}/{len(departments)} départements migrés")
            
            print(f"✅ {len(departments)} départements migrés")
            return True
            
        except Exception as e:
            print(f"❌ Erreur lors de la migration des départements: {e}")
            return False

def migrate_users(db_firestore, app):
    """Migrer les utilisateurs vers Firestore"""
    print("👥 Migration des utilisateurs...")
    
    with app.app_context():
        try:
            users = User.query.all()
            batch = db_firestore.batch()
            
            for user in users:
                doc_ref = db_firestore.collection('users').document(str(user.id))
                user_data = {
                    'email': user.email,
                    'username': user.username,
                    'password_hash': user.password_hash,
                    'first_name': user.first_name,
                    'last_name': user.last_name,
                    'user_type': user.user_type,
                    'phone': user.phone,
                    'company_name': user.company_name,
                    'address': user.address,
                    'city': user.city,
                    'postal_code': user.postal_code,
                    'country_id': user.country_id,
                    'department_id': user.department_id,
                    'preferred_currency_id': user.preferred_currency_id,
                    'is_active': user.is_active,
                    'created_at': user.created_at or datetime.utcnow(),
                    'updated_at': user.updated_at or datetime.utcnow()
                }
                batch.set(doc_ref, user_data)
            
            batch.commit()
            print(f"✅ {len(users)} utilisateurs migrés")
            return True
            
        except Exception as e:
            print(f"❌ Erreur lors de la migration des utilisateurs: {e}")
            return False

def migrate_freight_offers(db_firestore, app):
    """Migrer les offres de fret vers Firestore"""
    print("🚛 Migration des offres de fret...")
    
    with app.app_context():
        try:
            offers = FreightOffer.query.all()
            batch = db_firestore.batch()
            
            for offer in offers:
                doc_ref = db_firestore.collection('freight_offers').document(str(offer.id))
                offer_data = {
                    'user_id': str(offer.user_id),
                    'title': offer.title,
                    'description': offer.description,
                    'cargo_type': offer.cargo_type,
                    'weight': float(offer.weight) if offer.weight else None,
                    'volume': float(offer.volume) if offer.volume else None,
                    'pickup_address': offer.pickup_address,
                    'pickup_city': offer.pickup_city,
                    'pickup_postal_code': offer.pickup_postal_code,
                    'pickup_country_id': offer.pickup_country_id,
                    'pickup_department_id': offer.pickup_department_id,
                    'delivery_address': offer.delivery_address,
                    'delivery_city': offer.delivery_city,
                    'delivery_postal_code': offer.delivery_postal_code,
                    'delivery_country_id': offer.delivery_country_id,
                    'delivery_department_id': offer.delivery_department_id,
                    'pickup_date': offer.pickup_date,
                    'delivery_date': offer.delivery_date,
                    'price': float(offer.price) if offer.price else None,
                    'currency_id': offer.currency_id,
                    'price_type': offer.price_type.value if offer.price_type else 'negociable',
                    'is_urgent': offer.is_urgent,
                    'is_fragile': offer.is_fragile,
                    'requires_insurance': offer.requires_insurance,
                    'special_instructions': offer.special_instructions,
                    'status': offer.status.value if offer.status else 'active',
                    'created_at': offer.created_at or datetime.utcnow(),
                    'updated_at': offer.updated_at or datetime.utcnow()
                }
                batch.set(doc_ref, offer_data)
            
            batch.commit()
            print(f"✅ {len(offers)} offres de fret migrées")
            return True
            
        except Exception as e:
            print(f"❌ Erreur lors de la migration des offres de fret: {e}")
            return False

def migrate_freight_proposals(db_firestore, app):
    """Migrer les propositions de fret vers Firestore"""
    print("💼 Migration des propositions de fret...")
    
    with app.app_context():
        try:
            proposals = FreightProposal.query.all()
            batch = db_firestore.batch()
            
            for proposal in proposals:
                doc_ref = db_firestore.collection('freight_proposals').document(str(proposal.id))
                proposal_data = {
                    'offer_id': str(proposal.offer_id),
                    'transporter_id': str(proposal.transporter_id),
                    'proposed_price': float(proposal.proposed_price),
                    'currency_id': proposal.currency_id,
                    'price_type': proposal.price_type.value if proposal.price_type else 'fixe',
                    'estimated_duration': proposal.estimated_duration,
                    'vehicle_type': proposal.vehicle_type,
                    'additional_services': proposal.additional_services,
                    'comments': proposal.comments,
                    'status': proposal.status.value if proposal.status else 'pending',
                    'created_at': proposal.created_at or datetime.utcnow(),
                    'updated_at': proposal.updated_at or datetime.utcnow()
                }
                batch.set(doc_ref, proposal_data)
            
            batch.commit()
            print(f"✅ {len(proposals)} propositions de fret migrées")
            return True
            
        except Exception as e:
            print(f"❌ Erreur lors de la migration des propositions de fret: {e}")
            return False

def main():
    """Fonction principale de migration"""
    print("🚀 Démarrage de la migration SABTRANS vers Firestore...\n")
    
    # Initialiser Firebase
    db_firestore = init_firebase()
    if not db_firestore:
        print("❌ Impossible d'initialiser Firebase")
        return False
    
    # Initialiser l'application Flask
    app = create_app()
    
    # Ordre de migration (important pour les dépendances)
    migrations = [
        ("Devises", migrate_currencies),
        ("Pays", migrate_countries),
        ("Départements", migrate_departments),
        ("Utilisateurs", migrate_users),
        ("Offres de fret", migrate_freight_offers),
        ("Propositions de fret", migrate_freight_proposals)
    ]
    
    results = []
    for name, migration_func in migrations:
        print(f"\n📋 Migration: {name}")
        try:
            result = migration_func(db_firestore, app)
            results.append(result)
            if result:
                print(f"✅ {name} migrés avec succès")
            else:
                print(f"❌ Échec de la migration: {name}")
        except Exception as e:
            print(f"❌ Erreur inattendue lors de la migration {name}: {e}")
            results.append(False)
    
    # Résumé final
    print("\n" + "="*60)
    if all(results):
        print("🎉 SUCCÈS: Toutes les données ont été migrées vers Firestore!")
        print("\n📝 Prochaines étapes:")
        print("1. Configurer les credentials Firebase dans votre projet")
        print("2. Déployer les Cloud Functions: firebase deploy --only functions")
        print("3. Déployer le frontend: firebase deploy --only hosting")
        print("4. Tester l'application sur Firebase")
    else:
        failed_migrations = [name for (name, _), result in zip(migrations, results) if not result]
        print(f"❌ ÉCHEC: Certaines migrations ont échoué: {', '.join(failed_migrations)}")
        print("Vérifiez les erreurs ci-dessus et relancez la migration")
    print("="*60)
    
    return all(results)

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
