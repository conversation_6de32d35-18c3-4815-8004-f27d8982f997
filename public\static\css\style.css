/* SABTRANS Custom Styles */

/* Couleurs principales selon le cahier des charges */
:root {
    --primary-color: #003366;    /* Bleu foncé */
    --secondary-color: #90EE90;  /* Vert clair */
    --accent-color: #FFA500;     /* Orange */
    --background-color: #F0F0F0; /* Gris clair */
    --text-color: #333333;       /* Gris foncé */
    --white: #FFFFFF;
    --success: #28a745;
    --warning: #ffc107;
    --danger: #dc3545;
    --info: #17a2b8;
}

/* Override Bootstrap primary color */
.bg-primary {
    background-color: var(--primary-color) !important;
}

.btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-primary:hover {
    background-color: #002244;
    border-color: #002244;
}

.text-primary {
    color: var(--primary-color) !important;
}

.border-primary {
    border-color: var(--primary-color) !important;
}

/* Custom button styles */
.btn-success {
    background-color: var(--secondary-color);
    border-color: var(--secondary-color);
    color: var(--text-color);
}

.btn-success:hover {
    background-color: #7FDD7F;
    border-color: #7FDD7F;
    color: var(--text-color);
}

.btn-warning {
    background-color: var(--accent-color);
    border-color: var(--accent-color);
    color: var(--white);
}

.btn-warning:hover {
    background-color: #E6940A;
    border-color: #E6940A;
}

/* Body and general styles */
body {
    background-color: var(--background-color);
    color: var(--text-color);
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* Hero section */
.hero-section {
    background: linear-gradient(135deg, var(--primary-color) 0%, #004488 100%);
    min-height: 60vh;
    display: flex;
    align-items: center;
}

/* Cards */
.card {
    border: none;
    border-radius: 10px;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.card-header {
    border-radius: 10px 10px 0 0 !important;
}

/* Feature icons */
.feature-icon {
    width: 80px;
    height: 80px;
    font-size: 2rem;
}

/* Navigation */
.navbar-brand {
    font-size: 1.5rem;
    font-weight: bold;
}

.navbar-nav .nav-link {
    font-weight: 500;
    transition: color 0.3s ease;
}

.navbar-nav .nav-link:hover {
    color: var(--secondary-color) !important;
}

/* Badges */
.badge {
    font-size: 0.7rem;
}

/* Forms */
.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(0, 51, 102, 0.25);
}

.form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(0, 51, 102, 0.25);
}

/* Tables */
.table {
    background-color: var(--white);
}

.table th {
    background-color: var(--primary-color);
    color: var(--white);
    border: none;
}

/* Status badges */
.status-pending {
    background-color: var(--warning);
    color: var(--text-color);
}

.status-active {
    background-color: var(--success);
}

.status-completed {
    background-color: var(--info);
}

.status-cancelled {
    background-color: var(--danger);
}

/* Dashboard cards */
.dashboard-card {
    background: linear-gradient(135deg, var(--white) 0%, #f8f9fa 100%);
    border-left: 4px solid var(--primary-color);
}

.dashboard-card.success {
    border-left-color: var(--secondary-color);
}

.dashboard-card.warning {
    border-left-color: var(--accent-color);
}

.dashboard-card.info {
    border-left-color: var(--info);
}

/* Stats numbers */
.stat-number {
    font-size: 2.5rem;
    font-weight: bold;
    color: var(--primary-color);
}

/* Progress bars */
.progress {
    height: 8px;
    border-radius: 4px;
}

.progress-bar {
    background-color: var(--primary-color);
}

/* Freight offer cards */
.freight-card {
    transition: all 0.3s ease;
    border-left: 4px solid transparent;
}

.freight-card:hover {
    border-left-color: var(--primary-color);
    transform: translateX(5px);
}

.freight-card.urgent {
    border-left-color: var(--accent-color);
}

/* Document icons */
.document-icon {
    width: 48px;
    height: 48px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 8px;
    font-size: 1.5rem;
}

.document-icon.pdf {
    background-color: #dc3545;
    color: white;
}

.document-icon.image {
    background-color: #17a2b8;
    color: white;
}

.document-icon.doc {
    background-color: #007bff;
    color: white;
}

.document-icon.excel {
    background-color: #28a745;
    color: white;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .hero-section {
        min-height: 50vh;
        text-align: center;
    }
    
    .hero-section h1 {
        font-size: 2.5rem;
    }
    
    .stat-number {
        font-size: 2rem;
    }
    
    .feature-icon {
        width: 60px;
        height: 60px;
        font-size: 1.5rem;
    }
}

/* Loading spinner */
.spinner-border-sm {
    width: 1rem;
    height: 1rem;
}

/* Custom scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
    background: var(--primary-color);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #002244;
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in-up {
    animation: fadeInUp 0.6s ease-out;
}

/* Print styles */
@media print {
    .navbar,
    .btn,
    .card-footer {
        display: none !important;
    }
    
    .card {
        border: 1px solid #ddd !important;
        box-shadow: none !important;
    }
}
