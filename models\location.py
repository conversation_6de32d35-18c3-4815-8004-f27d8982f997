from datetime import datetime
from database import db

class Currency(db.Model):
    """Modèle pour les devises"""
    __tablename__ = 'currencies'
    
    id = db.Column(db.Integer, primary_key=True)
    code = db.Column(db.String(3), unique=True, nullable=False)  # EUR, DZD, USD, etc.
    name = db.Column(db.String(50), nullable=False)  # Euro, Dinar algérien, etc.
    symbol = db.Column(db.String(5), nullable=False)  # €, د.ج, $, etc.
    
    # Relations
    countries = db.relationship('Country', backref='currency', lazy='dynamic')
    
    def to_dict(self):
        return {
            'id': self.id,
            'code': self.code,
            'name': self.name,
            'symbol': self.symbol
        }
    
    def __repr__(self):
        return f'<Currency {self.code}>'


class Country(db.Model):
    """Modèle pour les pays"""
    __tablename__ = 'countries'
    
    id = db.Column(db.Integer, primary_key=True)
    code = db.Column(db.String(2), unique=True, nullable=False)  # FR, DZ, etc.
    name = db.Column(db.String(100), nullable=False)  # France, Algérie, etc.
    name_fr = db.Column(db.String(100), nullable=False)  # Nom en français
    name_en = db.Column(db.String(100), nullable=False)  # Nom en anglais
    
    # Devise principale
    currency_id = db.Column(db.Integer, db.ForeignKey('currencies.id'), nullable=False)
    
    # Type de subdivision administrative
    subdivision_type = db.Column(db.String(20), nullable=False)  # département, wilaya, province, etc.
    subdivision_type_fr = db.Column(db.String(20), nullable=False)  # département, wilaya, province, etc.
    
    # Métadonnées
    phone_prefix = db.Column(db.String(10))  # +33, +213, etc.
    is_active = db.Column(db.Boolean, default=True)
    
    # Relations
    departments = db.relationship('Department', backref='country', lazy='dynamic', cascade='all, delete-orphan')
    users = db.relationship('User', backref='user_country', lazy='dynamic')
    
    def to_dict(self):
        return {
            'id': self.id,
            'code': self.code,
            'name': self.name,
            'name_fr': self.name_fr,
            'name_en': self.name_en,
            'currency': self.currency.to_dict() if self.currency else None,
            'subdivision_type': self.subdivision_type,
            'subdivision_type_fr': self.subdivision_type_fr,
            'phone_prefix': self.phone_prefix,
            'is_active': self.is_active
        }
    
    def __repr__(self):
        return f'<Country {self.name}>'


class Department(db.Model):
    """Modèle pour les départements/wilayas/provinces"""
    __tablename__ = 'departments'
    
    id = db.Column(db.Integer, primary_key=True)
    
    # Référence au pays
    country_id = db.Column(db.Integer, db.ForeignKey('countries.id'), nullable=False)
    
    # Informations de base
    code = db.Column(db.String(10), nullable=False)  # 75, 01, 69, etc. pour France ou codes wilayas pour Algérie
    name = db.Column(db.String(100), nullable=False)  # Paris, Alger, Lyon, etc.
    name_fr = db.Column(db.String(100), nullable=False)  # Nom en français
    
    # Métadonnées
    is_active = db.Column(db.Boolean, default=True)
    population = db.Column(db.Integer)  # Population (optionnel)
    area_km2 = db.Column(db.Float)  # Superficie en km² (optionnel)
    
    # Relations
    users = db.relationship('User', backref='user_department', lazy='dynamic')
    
    # Index unique sur country_id + code pour éviter les doublons
    __table_args__ = (db.UniqueConstraint('country_id', 'code', name='unique_country_department'),)
    
    def to_dict(self):
        return {
            'id': self.id,
            'country_id': self.country_id,
            'code': self.code,
            'name': self.name,
            'name_fr': self.name_fr,
            'is_active': self.is_active,
            'population': self.population,
            'area_km2': self.area_km2
        }
    
    def get_full_name(self):
        """Retourne le nom complet avec le code"""
        return f"{self.code} - {self.name}"
    
    def __repr__(self):
        return f'<Department {self.code} - {self.name}>'
