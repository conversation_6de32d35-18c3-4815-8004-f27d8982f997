/**
 * Location management JavaScript
 * Handles dynamic loading of countries, departments/wilayas, and currencies
 */

class LocationManager {
    constructor() {
        this.countries = [];
        this.departments = {};
        this.currencies = {};
        this.init();
    }

    async init() {
        await this.loadCountries();
        this.setupEventListeners();
        this.initializeExistingSelections();
    }

    async loadCountries() {
        try {
            const response = await fetch('/api/countries');
            const data = await response.json();
            
            if (data.success) {
                this.countries = data.countries;
                this.populateCountrySelects();
            } else {
                console.error('Error loading countries:', data.message);
            }
        } catch (error) {
            console.error('Error fetching countries:', error);
        }
    }

    populateCountrySelects() {
        const countrySelects = document.querySelectorAll('select[name="country_id"], select#country_id, select[name="pickup_country_id"], select#pickup_country_id, select[name="delivery_country_id"], select#delivery_country_id');

        countrySelects.forEach(select => {
            // Clear existing options except the first one
            const firstOption = select.querySelector('option[value=""]');
            select.innerHTML = '';
            if (firstOption) {
                select.appendChild(firstOption);
            }

            // Add country options
            this.countries.forEach(country => {
                const option = document.createElement('option');
                option.value = country.id;
                option.textContent = country.name_fr;
                option.dataset.code = country.code;
                option.dataset.subdivisionType = country.subdivision_type_fr;
                option.dataset.currency = JSON.stringify(country.currency);
                select.appendChild(option);
            });
        });
    }

    async loadDepartments(countryId, countryCode) {
        if (this.departments[countryId]) {
            return this.departments[countryId];
        }

        try {
            const response = await fetch(`/api/countries/${countryCode}/departments`);
            const data = await response.json();
            
            if (data.success) {
                this.departments[countryId] = data.departments;
                return data.departments;
            } else {
                console.error('Error loading departments:', data.message);
                return [];
            }
        } catch (error) {
            console.error('Error fetching departments:', error);
            return [];
        }
    }

    populateDepartmentSelect(departmentSelect, departments, subdivisionType) {
        // Clear existing options
        departmentSelect.innerHTML = '<option value="">Sélectionnez...</option>';

        // Update label if subdivision type is provided
        const departmentName = departmentSelect.name || departmentSelect.id;
        let labelSelector;

        if (departmentName.includes('pickup')) {
            labelSelector = '#pickup_department_label';
        } else if (departmentName.includes('delivery')) {
            labelSelector = '#delivery_department_label';
        } else {
            labelSelector = '#department_label, span#department_label';
        }

        const label = departmentSelect.closest('.mb-3')?.querySelector(`label ${labelSelector}`) ||
                     departmentSelect.closest('form')?.querySelector(labelSelector);

        if (label && subdivisionType) {
            label.textContent = subdivisionType.charAt(0).toUpperCase() + subdivisionType.slice(1);
        }

        // Add department options
        departments.forEach(dept => {
            const option = document.createElement('option');
            option.value = dept.id;
            option.textContent = `${dept.code} - ${dept.name_fr}`;
            departmentSelect.appendChild(option);
        });

        // Enable the select
        departmentSelect.disabled = false;
    }

    setupEventListeners() {
        // Handle country selection changes
        document.addEventListener('change', async (e) => {
            if (e.target.matches('select[name="country_id"], select#country_id, select[name="pickup_country_id"], select#pickup_country_id, select[name="delivery_country_id"], select#delivery_country_id')) {
                await this.handleCountryChange(e.target);
            }
        });

        // Handle form submissions to validate location data
        document.addEventListener('submit', (e) => {
            if (e.target.matches('form')) {
                this.validateLocationData(e.target);
            }
        });
    }

    async handleCountryChange(countrySelect) {
        const countryId = countrySelect.value;
        const selectedOption = countrySelect.querySelector(`option[value="${countryId}"]`);
        
        // Find corresponding department select
        const departmentSelect = this.findDepartmentSelect(countrySelect);
        
        if (!departmentSelect) {
            return;
        }

        if (!countryId) {
            // No country selected
            departmentSelect.innerHTML = '<option value="">Sélectionnez d\'abord un pays</option>';
            departmentSelect.disabled = true;
            return;
        }

        // Show loading state
        departmentSelect.innerHTML = '<option value="">Chargement...</option>';
        departmentSelect.disabled = true;

        // Get country data
        const countryCode = selectedOption.dataset.code;
        const subdivisionType = selectedOption.dataset.subdivisionType;
        
        // Load departments
        const departments = await this.loadDepartments(countryId, countryCode);
        
        // Populate department select
        this.populateDepartmentSelect(departmentSelect, departments, subdivisionType);

        // Update currency information if there's a currency display element
        this.updateCurrencyDisplay(selectedOption.dataset.currency);
    }

    findDepartmentSelect(countrySelect) {
        // Determine the corresponding department select based on the country select name/id
        const countryName = countrySelect.name || countrySelect.id;
        let departmentSelector;

        if (countryName.includes('pickup')) {
            departmentSelector = 'select[name="pickup_department_id"], select#pickup_department_id';
        } else if (countryName.includes('delivery')) {
            departmentSelector = 'select[name="delivery_department_id"], select#delivery_department_id';
        } else {
            departmentSelector = 'select[name="department_id"], select#department_id';
        }

        // Look for department select in the same form or container
        const form = countrySelect.closest('form');
        if (form) {
            return form.querySelector(departmentSelector);
        }

        // Fallback: look in the same container
        const container = countrySelect.closest('.row, .card-body, .modal-body');
        if (container) {
            return container.querySelector(departmentSelector);
        }

        return null;
    }

    updateCurrencyDisplay(currencyJson) {
        if (!currencyJson) return;
        
        try {
            const currency = JSON.parse(currencyJson);
            const currencyDisplays = document.querySelectorAll('.currency-display, [data-currency-display]');
            
            currencyDisplays.forEach(display => {
                display.textContent = `${currency.name} (${currency.symbol})`;
                display.dataset.currencyCode = currency.code;
                display.dataset.currencySymbol = currency.symbol;
            });
        } catch (error) {
            console.error('Error parsing currency data:', error);
        }
    }

    initializeExistingSelections() {
        // Initialize any pre-selected values (for edit forms)
        const countrySelects = document.querySelectorAll('select[name="country_id"], select#country_id, select[name="pickup_country_id"], select#pickup_country_id, select[name="delivery_country_id"], select#delivery_country_id');

        countrySelects.forEach(async (select) => {
            const preselectedCountryId = select.dataset.preselected || select.value;

            if (preselectedCountryId) {
                select.value = preselectedCountryId;
                await this.handleCountryChange(select);

                // If there's a pre-selected department, select it
                const departmentSelect = this.findDepartmentSelect(select);
                const preselectedDeptId = departmentSelect?.dataset.preselected;

                if (preselectedDeptId && departmentSelect) {
                    // Wait a bit for departments to load
                    setTimeout(() => {
                        departmentSelect.value = preselectedDeptId;
                    }, 500);
                }
            }
        });
    }

    validateLocationData(form) {
        const countrySelect = form.querySelector('select[name="country_id"]');
        const departmentSelect = form.querySelector('select[name="department_id"]');
        
        if (countrySelect && departmentSelect) {
            const countryId = countrySelect.value;
            const departmentId = departmentSelect.value;
            
            // Basic validation
            if (countryId && !departmentId) {
                alert('Veuillez sélectionner un département/wilaya.');
                departmentSelect.focus();
                return false;
            }
        }
        
        return true;
    }

    // Utility method to get selected location data
    getSelectedLocationData(form) {
        const countrySelect = form.querySelector('select[name="country_id"]');
        const departmentSelect = form.querySelector('select[name="department_id"]');
        
        if (!countrySelect || !departmentSelect) {
            return null;
        }
        
        const countryOption = countrySelect.querySelector(`option[value="${countrySelect.value}"]`);
        const departmentOption = departmentSelect.querySelector(`option[value="${departmentSelect.value}"]`);
        
        return {
            country: {
                id: countrySelect.value,
                code: countryOption?.dataset.code,
                name: countryOption?.textContent,
                subdivisionType: countryOption?.dataset.subdivisionType,
                currency: countryOption?.dataset.currency ? JSON.parse(countryOption.dataset.currency) : null
            },
            department: {
                id: departmentSelect.value,
                name: departmentOption?.textContent
            }
        };
    }
}

// Initialize location manager when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.locationManager = new LocationManager();
});

// Export for use in other scripts
if (typeof module !== 'undefined' && module.exports) {
    module.exports = LocationManager;
}
