{% extends "base.html" %}

{% block title %}Dashboard Administrateur - SABTRANS{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col">
            <h1 class="h3 text-primary">
                <i class="fas fa-cogs me-2"></i>Dashboard Administrateur
            </h1>
            <p class="text-muted">Vue d'ensemble de la plateforme SABTRANS</p>
        </div>
    </div>

    <!-- Statistiques principales -->
    <div class="row mb-4">
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card dashboard-card h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title text-muted">Utilisateurs Totaux</h6>
                            <h2 class="stat-number">{{ total_users }}</h2>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-users fa-2x text-primary"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card dashboard-card success h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title text-muted">Offres Totales</h6>
                            <h2 class="stat-number text-success">{{ total_offers }}</h2>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-exchange-alt fa-2x text-success"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card dashboard-card warning h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title text-muted">Missions Totales</h6>
                            <h2 class="stat-number text-warning">{{ total_missions }}</h2>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-tasks fa-2x text-warning"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card dashboard-card info h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title text-muted">Missions Actives</h6>
                            <h2 class="stat-number text-info">{{ active_missions }}</h2>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-shipping-fast fa-2x text-info"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row mb-4">
        <!-- Répartition des utilisateurs -->
        <div class="col-lg-6 mb-4">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-chart-pie me-2"></i>Répartition des Utilisateurs
                    </h5>
                </div>
                <div class="card-body">
                    {% if user_stats %}
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>Type</th>
                                        <th>Nombre</th>
                                        <th>Pourcentage</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for stat in user_stats %}
                                    <tr>
                                        <td>
                                            <span class="badge bg-secondary">{{ stat.user_type.title() }}</span>
                                        </td>
                                        <td><strong>{{ stat.count }}</strong></td>
                                        <td>
                                            {% set percentage = (stat.count / total_users * 100) if total_users > 0 else 0 %}
                                            <div class="progress" style="height: 20px;">
                                                <div class="progress-bar" role="progressbar" 
                                                     style="width: {{ percentage }}%">
                                                    {{ "%.1f"|format(percentage) }}%
                                                </div>
                                            </div>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% else %}
                        <p class="text-muted text-center">Aucune donnée disponible</p>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Statut des missions -->
        <div class="col-lg-6 mb-4">
            <div class="card">
                <div class="card-header bg-success text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-chart-bar me-2"></i>Statut des Missions
                    </h5>
                </div>
                <div class="card-body">
                    {% if mission_stats %}
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>Statut</th>
                                        <th>Nombre</th>
                                        <th>Répartition</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for stat in mission_stats %}
                                    <tr>
                                        <td>
                                            <span class="badge bg-{{ 'success' if stat.status == 'completed' else 'primary' }}">
                                                {{ stat.status.title() }}
                                            </span>
                                        </td>
                                        <td><strong>{{ stat.count }}</strong></td>
                                        <td>
                                            {% set percentage = (stat.count / total_missions * 100) if total_missions > 0 else 0 %}
                                            <div class="progress" style="height: 20px;">
                                                <div class="progress-bar bg-{{ 'success' if stat.status == 'completed' else 'primary' }}" 
                                                     role="progressbar" style="width: {{ percentage }}%">
                                                    {{ "%.1f"|format(percentage) }}%
                                                </div>
                                            </div>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% else %}
                        <p class="text-muted text-center">Aucune mission trouvée</p>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Offres récentes -->
        <div class="col-lg-6 mb-4">
            <div class="card">
                <div class="card-header bg-warning text-dark">
                    <h5 class="mb-0">
                        <i class="fas fa-clock me-2"></i>Offres Récentes
                    </h5>
                </div>
                <div class="card-body">
                    {% if recent_offers %}
                        <div class="list-group list-group-flush">
                            {% for offer in recent_offers %}
                            <div class="list-group-item d-flex justify-content-between align-items-start">
                                <div class="ms-2 me-auto">
                                    <div class="fw-bold">{{ offer.title }}</div>
                                    <small class="text-muted">
                                        {{ offer.pickup_city }} → {{ offer.delivery_city }}
                                    </small>
                                    <br>
                                    <small class="text-muted">
                                        Par {{ offer.user.company_name or offer.user.get_full_name() }}
                                    </small>
                                </div>
                                <div class="text-end">
                                    <span class="badge bg-{{ 'primary' if offer.offer_type == 'demande' else 'success' }}">
                                        {% if offer.offer_type %}
                                            {{ offer.offer_type.title() }}
                                        {% else %}
                                            Type non défini
                                        {% endif %}
                                    </span>
                                    <br>
                                    <small class="text-muted">
                                        {% if offer.created_at %}
                                            {{ offer.created_at.strftime('%d/%m %H:%M') }}
                                        {% else %}
                                            Date inconnue
                                        {% endif %}
                                    </small>
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                        <div class="text-center mt-3">
                            <a href="{{ url_for('freight.index') }}" class="btn btn-outline-primary btn-sm">
                                Voir toutes les offres
                            </a>
                        </div>
                    {% else %}
                        <p class="text-muted text-center">Aucune offre récente</p>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Missions récentes -->
        <div class="col-lg-6 mb-4">
            <div class="card">
                <div class="card-header bg-info text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-history me-2"></i>Missions Récentes
                    </h5>
                </div>
                <div class="card-body">
                    {% if recent_missions %}
                        <div class="list-group list-group-flush">
                            {% for mission in recent_missions %}
                            <div class="list-group-item d-flex justify-content-between align-items-start">
                                <div class="ms-2 me-auto">
                                    <div class="fw-bold">{{ mission.mission_number }}</div>
                                    <small class="text-muted">{{ mission.title }}</small>
                                    <br>
                                    <small class="text-muted">
                                        Transporteur: {{ mission.transporter.company_name or mission.transporter.get_full_name() }}
                                    </small>
                                </div>
                                <div class="text-end">
                                    <span class="badge bg-{{ 'success' if mission.status == 'completed' else 'warning' }}">
                                        {% if mission.status %}
                                            {{ mission.status.title() }}
                                        {% else %}
                                            Statut non défini
                                        {% endif %}
                                    </span>
                                    <br>
                                    <small class="text-muted">
                                        {% if mission.created_at %}
                                            {{ mission.created_at.strftime('%d/%m %H:%M') }}
                                        {% else %}
                                            Date inconnue
                                        {% endif %}
                                    </small>
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                        <div class="text-center mt-3">
                            <a href="#" class="btn btn-outline-info btn-sm">
                                Voir toutes les missions
                            </a>
                        </div>
                    {% else %}
                        <p class="text-muted text-center">Aucune mission récente</p>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Gestion des Enregistrements -->
    <div class="row mb-4">
        <!-- Gestion des Transporteurs -->
        <div class="col-lg-4 mb-4">
            <div class="card">
                <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-truck me-2"></i>Transporteurs
                    </h5>
                    <button class="btn btn-light btn-sm" onclick="showAddTransporterModal()">
                        <i class="fas fa-plus me-1"></i>Ajouter
                    </button>
                </div>
                <div class="card-body">
                    {% if transporters %}
                        <div class="list-group list-group-flush" style="max-height: 300px; overflow-y: auto;">
                            {% for transporter in transporters %}
                            <div class="list-group-item d-flex justify-content-between align-items-center">
                                <div>
                                    <strong>{{ transporter.get_full_name() }}</strong>
                                    {% if transporter.company_name %}
                                        <br><small class="text-muted">{{ transporter.company_name }}</small>
                                    {% endif %}
                                    <br><small class="text-muted">{{ transporter.email }}</small>
                                </div>
                                <div class="btn-group" role="group">
                                    <a href="{{ url_for('admin.user_detail', user_id=transporter.id) }}"
                                       class="btn btn-sm btn-outline-info" title="Voir détails">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <button class="btn btn-sm btn-outline-danger"
                                            onclick="deleteUser({{ transporter.id }}, '{{ transporter.get_full_name() }}')"
                                            title="Supprimer">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                        <div class="text-center mt-3">
                            <a href="{{ url_for('admin.users') }}?type=transporter" class="btn btn-outline-primary btn-sm">
                                Voir tous les transporteurs
                            </a>
                        </div>
                    {% else %}
                        <div class="text-center py-3">
                            <i class="fas fa-truck fa-2x text-muted mb-2"></i>
                            <p class="text-muted">Aucun transporteur</p>
                            <button class="btn btn-primary btn-sm" onclick="showAddTransporterModal()">
                                <i class="fas fa-plus me-1"></i>Ajouter le premier transporteur
                            </button>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Gestion des Expéditeurs -->
        <div class="col-lg-4 mb-4">
            <div class="card">
                <div class="card-header bg-success text-white d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-industry me-2"></i>Expéditeurs
                    </h5>
                    <button class="btn btn-light btn-sm" onclick="showAddShipperModal()">
                        <i class="fas fa-plus me-1"></i>Ajouter
                    </button>
                </div>
                <div class="card-body">
                    {% if shippers %}
                        <div class="list-group list-group-flush" style="max-height: 300px; overflow-y: auto;">
                            {% for shipper in shippers %}
                            <div class="list-group-item d-flex justify-content-between align-items-center">
                                <div>
                                    <strong>{{ shipper.get_full_name() }}</strong>
                                    {% if shipper.company_name %}
                                        <br><small class="text-muted">{{ shipper.company_name }}</small>
                                    {% endif %}
                                    <br><small class="text-muted">{{ shipper.email }}</small>
                                </div>
                                <div class="btn-group" role="group">
                                    <a href="{{ url_for('admin.user_detail', user_id=shipper.id) }}"
                                       class="btn btn-sm btn-outline-info" title="Voir détails">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <button class="btn btn-sm btn-outline-danger"
                                            onclick="deleteUser({{ shipper.id }}, '{{ shipper.get_full_name() }}')"
                                            title="Supprimer">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                        <div class="text-center mt-3">
                            <a href="{{ url_for('admin.users') }}?type=shipper" class="btn btn-outline-success btn-sm">
                                Voir tous les expéditeurs
                            </a>
                        </div>
                    {% else %}
                        <div class="text-center py-3">
                            <i class="fas fa-industry fa-2x text-muted mb-2"></i>
                            <p class="text-muted">Aucun expéditeur</p>
                            <button class="btn btn-success btn-sm" onclick="showAddShipperModal()">
                                <i class="fas fa-plus me-1"></i>Ajouter le premier expéditeur
                            </button>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Gestion des Offres -->
        <div class="col-lg-4 mb-4">
            <div class="card">
                <div class="card-header bg-warning text-dark d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-exchange-alt me-2"></i>Offres Récentes
                    </h5>
                    <button class="btn btn-dark btn-sm" onclick="showAddOfferModal()">
                        <i class="fas fa-plus me-1"></i>Ajouter
                    </button>
                </div>
                <div class="card-body">
                    {% if recent_offers %}
                        <div class="list-group list-group-flush" style="max-height: 300px; overflow-y: auto;">
                            {% for offer in recent_offers %}
                            <div class="list-group-item d-flex justify-content-between align-items-start">
                                <div class="flex-grow-1">
                                    <strong>{{ offer.title }}</strong>
                                    <br><small class="text-muted">{{ offer.pickup_city }} → {{ offer.delivery_city }}</small>
                                    <br><span class="badge bg-{{ 'primary' if offer.offer_type == 'demande' else 'success' }} badge-sm">
                                        {% if offer.offer_type %}
                                            {{ offer.offer_type.title() }}
                                        {% else %}
                                            Type non défini
                                        {% endif %}
                                    </span>
                                </div>
                                <div class="btn-group" role="group">
                                    <a href="{{ url_for('freight.detail', offer_id=offer.id) }}"
                                       class="btn btn-sm btn-outline-info" title="Voir détails">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <button class="btn btn-sm btn-outline-danger"
                                            onclick="deleteOffer({{ offer.id }}, '{{ offer.title }}')"
                                            title="Supprimer">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                        <div class="text-center mt-3">
                            <a href="{{ url_for('freight.index') }}" class="btn btn-outline-warning btn-sm">
                                Voir toutes les offres
                            </a>
                        </div>
                    {% else %}
                        <div class="text-center py-3">
                            <i class="fas fa-exchange-alt fa-2x text-muted mb-2"></i>
                            <p class="text-muted">Aucune offre</p>
                            <button class="btn btn-warning btn-sm" onclick="showAddOfferModal()">
                                <i class="fas fa-plus me-1"></i>Créer la première offre
                            </button>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Actions rapides -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-secondary text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-tools me-2"></i>Actions Rapides
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3 mb-3">
                            <div class="d-grid">
                                <a href="{{ url_for('admin.users') }}" class="btn btn-outline-primary">
                                    <i class="fas fa-users me-2"></i>Gérer les Utilisateurs
                                </a>
                            </div>
                        </div>
                        <div class="col-md-3 mb-3">
                            <div class="d-grid">
                                <a href="{{ url_for('freight.index') }}" class="btn btn-outline-success">
                                    <i class="fas fa-exchange-alt me-2"></i>Voir les Offres
                                </a>
                            </div>
                        </div>
                        <div class="col-md-3 mb-3">
                            <div class="d-grid">
                                <a href="{{ url_for('admin.freight_proposals') }}" class="btn btn-outline-warning">
                                    <i class="fas fa-handshake me-2"></i>Propositions
                                </a>
                            </div>
                        </div>
                        <div class="col-md-3 mb-3">
                            <div class="d-grid">
                                <a href="#" class="btn btn-outline-info">
                                    <i class="fas fa-cog me-2"></i>Configuration
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modales pour l'ajout d'enregistrements -->

<!-- Modal Ajouter Transporteur -->
<div class="modal fade" id="addTransporterModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title">
                    <i class="fas fa-truck me-2"></i>Ajouter un Transporteur
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <form id="addTransporterForm">
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label">Prénom *</label>
                            <input type="text" class="form-control" name="first_name" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">Nom *</label>
                            <input type="text" class="form-control" name="last_name" required>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label">Email *</label>
                            <input type="email" class="form-control" name="email" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">Téléphone</label>
                            <input type="tel" class="form-control" name="phone">
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label">Nom de l'entreprise</label>
                            <input type="text" class="form-control" name="company_name">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">Ville</label>
                            <input type="text" class="form-control" name="city">
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label">Pays *</label>
                            <select class="form-select" name="country_id" required>
                                <option value="">Sélectionnez le pays</option>
                            </select>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label"><span id="department_label">Département</span> *</label>
                            <select class="form-select" name="department_id" required disabled>
                                <option value="">Sélectionnez d'abord un pays</option>
                            </select>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Mot de passe *</label>
                        <input type="password" class="form-control" name="password" required>
                        <div class="form-text">Minimum 6 caractères</div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save me-1"></i>Créer le transporteur
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Modal Ajouter Expéditeur -->
<div class="modal fade" id="addShipperModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-success text-white">
                <h5 class="modal-title">
                    <i class="fas fa-industry me-2"></i>Ajouter un Expéditeur
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <form id="addShipperForm">
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label">Prénom *</label>
                            <input type="text" class="form-control" name="first_name" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">Nom *</label>
                            <input type="text" class="form-control" name="last_name" required>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label">Email *</label>
                            <input type="email" class="form-control" name="email" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">Téléphone</label>
                            <input type="tel" class="form-control" name="phone">
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label">Nom de l'entreprise</label>
                            <input type="text" class="form-control" name="company_name">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">Ville</label>
                            <input type="text" class="form-control" name="city">
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label">Pays *</label>
                            <select class="form-select" name="country_id" required>
                                <option value="">Sélectionnez le pays</option>
                            </select>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label"><span id="department_label">Département</span> *</label>
                            <select class="form-select" name="department_id" required disabled>
                                <option value="">Sélectionnez d'abord un pays</option>
                            </select>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Mot de passe *</label>
                        <input type="password" class="form-control" name="password" required>
                        <div class="form-text">Minimum 6 caractères</div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                    <button type="submit" class="btn btn-success">
                        <i class="fas fa-save me-1"></i>Créer l'expéditeur
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Modal Ajouter Offre -->
<div class="modal fade" id="addOfferModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-warning text-dark">
                <h5 class="modal-title">
                    <i class="fas fa-exchange-alt me-2"></i>Créer une Offre
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="addOfferForm">
                <div class="modal-body">
                    <div class="mb-3">
                        <label class="form-label">Titre de l'offre *</label>
                        <input type="text" class="form-control" name="title" required>
                    </div>
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label">Type d'offre *</label>
                            <select class="form-select" name="offer_type" required>
                                <option value="">Sélectionner...</option>
                                <option value="demande">Demande de transport</option>
                                <option value="offre">Offre de transport</option>
                            </select>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">Utilisateur *</label>
                            <select class="form-select" name="user_id" required>
                                <option value="">Sélectionner un utilisateur...</option>
                                {% for user in all_users %}
                                <option value="{{ user.id }}">
                                    {{ user.get_full_name() }}
                                    {% if user.company_name %}({{ user.company_name }}){% endif %}
                                    - {{ user.user_type.title() }}
                                </option>
                                {% endfor %}
                            </select>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label">Ville de départ *</label>
                            <input type="text" class="form-control" name="pickup_city" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">Ville d'arrivée *</label>
                            <input type="text" class="form-control" name="delivery_city" required>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label">Type de marchandise</label>
                            <input type="text" class="form-control" name="goods_type">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">Poids (kg)</label>
                            <input type="number" class="form-control" name="weight_kg" step="0.1">
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label">Date de collecte</label>
                            <input type="date" class="form-control" name="pickup_date">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">Prix proposé (€)</label>
                            <input type="number" class="form-control" name="price" step="0.01">
                        </div>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Description</label>
                        <textarea class="form-control" name="description" rows="3"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                    <button type="submit" class="btn btn-warning">
                        <i class="fas fa-save me-1"></i>Créer l'offre
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

{% endblock %}

{% block extra_js %}
<script>
// Gestion des modales
function showAddTransporterModal() {
    const modal = new bootstrap.Modal(document.getElementById('addTransporterModal'));
    modal.show();
}

function showAddShipperModal() {
    const modal = new bootstrap.Modal(document.getElementById('addShipperModal'));
    modal.show();
}

function showAddOfferModal() {
    const modal = new bootstrap.Modal(document.getElementById('addOfferModal'));
    modal.show();
}

// Gestion des formulaires d'ajout
document.getElementById('addTransporterForm').addEventListener('submit', function(e) {
    e.preventDefault();
    const formData = new FormData(this);
    formData.append('user_type', 'transporter');

    fetch('/admin/users/add', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification(data.message, 'success');
            bootstrap.Modal.getInstance(document.getElementById('addTransporterModal')).hide();
            setTimeout(() => location.reload(), 1500);
        } else {
            showNotification(data.message, 'error');
        }
    })
    .catch(error => {
        showNotification('Erreur lors de la création du transporteur', 'error');
    });
});

document.getElementById('addShipperForm').addEventListener('submit', function(e) {
    e.preventDefault();
    const formData = new FormData(this);
    formData.append('user_type', 'shipper');

    fetch('/admin/users/add', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification(data.message, 'success');
            bootstrap.Modal.getInstance(document.getElementById('addShipperModal')).hide();
            setTimeout(() => location.reload(), 1500);
        } else {
            showNotification(data.message, 'error');
        }
    })
    .catch(error => {
        showNotification('Erreur lors de la création de l\'expéditeur', 'error');
    });
});

document.getElementById('addOfferForm').addEventListener('submit', function(e) {
    e.preventDefault();
    const formData = new FormData(this);

    fetch('/admin/offers/add', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification(data.message, 'success');
            bootstrap.Modal.getInstance(document.getElementById('addOfferModal')).hide();
            setTimeout(() => location.reload(), 1500);
        } else {
            showNotification(data.message, 'error');
        }
    })
    .catch(error => {
        showNotification('Erreur lors de la création de l\'offre', 'error');
    });
});

// Fonctions de suppression
function deleteUser(userId, userName) {
    if (confirm(`Êtes-vous sûr de vouloir supprimer l'utilisateur "${userName}" ?\n\nCette action est irréversible et supprimera également toutes ses offres et missions associées.`)) {
        fetch(`/admin/users/${userId}/delete`, {
            method: 'DELETE',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showNotification(data.message, 'success');
                setTimeout(() => location.reload(), 1500);
            } else {
                showNotification(data.message, 'error');
            }
        })
        .catch(error => {
            showNotification('Erreur lors de la suppression', 'error');
        });
    }
}

function deleteOffer(offerId, offerTitle) {
    if (confirm(`Êtes-vous sûr de vouloir supprimer l'offre "${offerTitle}" ?\n\nCette action est irréversible et supprimera également toutes les propositions associées.`)) {
        fetch(`/admin/offers/${offerId}/delete`, {
            method: 'DELETE',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showNotification(data.message, 'success');
                setTimeout(() => location.reload(), 1500);
            } else {
                showNotification(data.message, 'error');
            }
        })
        .catch(error => {
            showNotification('Erreur lors de la suppression', 'error');
        });
    }
}

// Fonction pour afficher les notifications
function showNotification(message, type) {
    const alertClass = type === 'success' ? 'alert-success' :
                      type === 'error' ? 'alert-danger' :
                      type === 'info' ? 'alert-info' : 'alert-warning';

    const notification = `
        <div class="alert ${alertClass} alert-dismissible fade show position-fixed"
             style="top: 20px; right: 20px; z-index: 9999; min-width: 300px;" role="alert">
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;

    document.body.insertAdjacentHTML('beforeend', notification);

    // Auto-remove after 5 seconds
    setTimeout(() => {
        const alerts = document.querySelectorAll('.alert');
        if (alerts.length > 0) {
            alerts[alerts.length - 1].remove();
        }
    }, 5000);
}

// Actualisation automatique des statistiques
setInterval(function() {
    fetch('/dashboard/stats/api')
        .then(response => response.json())
        .then(data => {
            console.log('Statistiques mises à jour:', data);
            // Ici on pourrait mettre à jour les graphiques en temps réel
        })
        .catch(error => console.error('Erreur lors de la mise à jour:', error));
}, 60000); // Toutes les minutes
</script>
{% endblock %}
