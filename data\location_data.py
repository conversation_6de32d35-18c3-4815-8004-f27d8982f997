"""
Données de localisation : devises, pays et départements/wilayas
"""

# Devises
CURRENCIES = [
    {'code': 'EUR', 'name': 'Euro', 'symbol': '€'},
    {'code': 'DZD', 'name': 'Dinar algérien', 'symbol': 'د.ج'},
    {'code': 'MAD', 'name': 'Dirham marocain', 'symbol': 'د.م.'},
    {'code': 'TND', 'name': 'Dinar tunisien', 'symbol': 'د.ت'},
    {'code': 'USD', 'name': 'Dollar américain', 'symbol': '$'},
    {'code': 'GBP', 'name': 'Livre sterling', 'symbol': '£'},
    {'code': 'CHF', 'name': 'Franc suisse', 'symbol': 'CHF'},
    {'code': 'CAD', 'name': 'Dollar canadien', 'symbol': 'C$'},
]

# Pays avec leurs devises et types de subdivisions
COUNTRIES = [
    {
        'code': 'FR',
        'name': 'France',
        'name_fr': 'France',
        'name_en': 'France',
        'currency_code': 'EUR',
        'subdivision_type': 'département',
        'subdivision_type_fr': 'département',
        'phone_prefix': '+33'
    },
    {
        'code': 'DZ',
        'name': 'Algérie',
        'name_fr': 'Algérie',
        'name_en': 'Algeria',
        'currency_code': 'DZD',
        'subdivision_type': 'wilaya',
        'subdivision_type_fr': 'wilaya',
        'phone_prefix': '+213'
    },
    {
        'code': 'MA',
        'name': 'Maroc',
        'name_fr': 'Maroc',
        'name_en': 'Morocco',
        'currency_code': 'MAD',
        'subdivision_type': 'province',
        'subdivision_type_fr': 'province',
        'phone_prefix': '+212'
    },
    {
        'code': 'TN',
        'name': 'Tunisie',
        'name_fr': 'Tunisie',
        'name_en': 'Tunisia',
        'currency_code': 'TND',
        'subdivision_type': 'gouvernorat',
        'subdivision_type_fr': 'gouvernorat',
        'phone_prefix': '+216'
    },
    {
        'code': 'BE',
        'name': 'Belgique',
        'name_fr': 'Belgique',
        'name_en': 'Belgium',
        'currency_code': 'EUR',
        'subdivision_type': 'province',
        'subdivision_type_fr': 'province',
        'phone_prefix': '+32'
    },
    {
        'code': 'CH',
        'name': 'Suisse',
        'name_fr': 'Suisse',
        'name_en': 'Switzerland',
        'currency_code': 'CHF',
        'subdivision_type': 'canton',
        'subdivision_type_fr': 'canton',
        'phone_prefix': '+41'
    },
    {
        'code': 'LU',
        'name': 'Luxembourg',
        'name_fr': 'Luxembourg',
        'name_en': 'Luxembourg',
        'currency_code': 'EUR',
        'subdivision_type': 'canton',
        'subdivision_type_fr': 'canton',
        'phone_prefix': '+352'
    },
    {
        'code': 'ES',
        'name': 'Espagne',
        'name_fr': 'Espagne',
        'name_en': 'Spain',
        'currency_code': 'EUR',
        'subdivision_type': 'province',
        'subdivision_type_fr': 'province',
        'phone_prefix': '+34'
    },
    {
        'code': 'IT',
        'name': 'Italie',
        'name_fr': 'Italie',
        'name_en': 'Italy',
        'currency_code': 'EUR',
        'subdivision_type': 'province',
        'subdivision_type_fr': 'province',
        'phone_prefix': '+39'
    },
    {
        'code': 'DE',
        'name': 'Allemagne',
        'name_fr': 'Allemagne',
        'name_en': 'Germany',
        'currency_code': 'EUR',
        'subdivision_type': 'land',
        'subdivision_type_fr': 'land',
        'phone_prefix': '+49'
    }
]

# Départements français (sélection des principaux)
FRENCH_DEPARTMENTS = [
    {'code': '01', 'name': 'Ain', 'name_fr': 'Ain'},
    {'code': '02', 'name': 'Aisne', 'name_fr': 'Aisne'},
    {'code': '03', 'name': 'Allier', 'name_fr': 'Allier'},
    {'code': '04', 'name': 'Alpes-de-Haute-Provence', 'name_fr': 'Alpes-de-Haute-Provence'},
    {'code': '05', 'name': 'Hautes-Alpes', 'name_fr': 'Hautes-Alpes'},
    {'code': '06', 'name': 'Alpes-Maritimes', 'name_fr': 'Alpes-Maritimes'},
    {'code': '07', 'name': 'Ardèche', 'name_fr': 'Ardèche'},
    {'code': '08', 'name': 'Ardennes', 'name_fr': 'Ardennes'},
    {'code': '09', 'name': 'Ariège', 'name_fr': 'Ariège'},
    {'code': '10', 'name': 'Aube', 'name_fr': 'Aube'},
    {'code': '11', 'name': 'Aude', 'name_fr': 'Aude'},
    {'code': '12', 'name': 'Aveyron', 'name_fr': 'Aveyron'},
    {'code': '13', 'name': 'Bouches-du-Rhône', 'name_fr': 'Bouches-du-Rhône'},
    {'code': '14', 'name': 'Calvados', 'name_fr': 'Calvados'},
    {'code': '15', 'name': 'Cantal', 'name_fr': 'Cantal'},
    {'code': '16', 'name': 'Charente', 'name_fr': 'Charente'},
    {'code': '17', 'name': 'Charente-Maritime', 'name_fr': 'Charente-Maritime'},
    {'code': '18', 'name': 'Cher', 'name_fr': 'Cher'},
    {'code': '19', 'name': 'Corrèze', 'name_fr': 'Corrèze'},
    {'code': '21', 'name': 'Côte-d\'Or', 'name_fr': 'Côte-d\'Or'},
    {'code': '22', 'name': 'Côtes-d\'Armor', 'name_fr': 'Côtes-d\'Armor'},
    {'code': '23', 'name': 'Creuse', 'name_fr': 'Creuse'},
    {'code': '24', 'name': 'Dordogne', 'name_fr': 'Dordogne'},
    {'code': '25', 'name': 'Doubs', 'name_fr': 'Doubs'},
    {'code': '26', 'name': 'Drôme', 'name_fr': 'Drôme'},
    {'code': '27', 'name': 'Eure', 'name_fr': 'Eure'},
    {'code': '28', 'name': 'Eure-et-Loir', 'name_fr': 'Eure-et-Loir'},
    {'code': '29', 'name': 'Finistère', 'name_fr': 'Finistère'},
    {'code': '30', 'name': 'Gard', 'name_fr': 'Gard'},
    {'code': '31', 'name': 'Haute-Garonne', 'name_fr': 'Haute-Garonne'},
    {'code': '32', 'name': 'Gers', 'name_fr': 'Gers'},
    {'code': '33', 'name': 'Gironde', 'name_fr': 'Gironde'},
    {'code': '34', 'name': 'Hérault', 'name_fr': 'Hérault'},
    {'code': '35', 'name': 'Ille-et-Vilaine', 'name_fr': 'Ille-et-Vilaine'},
    {'code': '36', 'name': 'Indre', 'name_fr': 'Indre'},
    {'code': '37', 'name': 'Indre-et-Loire', 'name_fr': 'Indre-et-Loire'},
    {'code': '38', 'name': 'Isère', 'name_fr': 'Isère'},
    {'code': '39', 'name': 'Jura', 'name_fr': 'Jura'},
    {'code': '40', 'name': 'Landes', 'name_fr': 'Landes'},
    {'code': '41', 'name': 'Loir-et-Cher', 'name_fr': 'Loir-et-Cher'},
    {'code': '42', 'name': 'Loire', 'name_fr': 'Loire'},
    {'code': '43', 'name': 'Haute-Loire', 'name_fr': 'Haute-Loire'},
    {'code': '44', 'name': 'Loire-Atlantique', 'name_fr': 'Loire-Atlantique'},
    {'code': '45', 'name': 'Loiret', 'name_fr': 'Loiret'},
    {'code': '46', 'name': 'Lot', 'name_fr': 'Lot'},
    {'code': '47', 'name': 'Lot-et-Garonne', 'name_fr': 'Lot-et-Garonne'},
    {'code': '48', 'name': 'Lozère', 'name_fr': 'Lozère'},
    {'code': '49', 'name': 'Maine-et-Loire', 'name_fr': 'Maine-et-Loire'},
    {'code': '50', 'name': 'Manche', 'name_fr': 'Manche'},
    {'code': '51', 'name': 'Marne', 'name_fr': 'Marne'},
    {'code': '52', 'name': 'Haute-Marne', 'name_fr': 'Haute-Marne'},
    {'code': '53', 'name': 'Mayenne', 'name_fr': 'Mayenne'},
    {'code': '54', 'name': 'Meurthe-et-Moselle', 'name_fr': 'Meurthe-et-Moselle'},
    {'code': '55', 'name': 'Meuse', 'name_fr': 'Meuse'},
    {'code': '56', 'name': 'Morbihan', 'name_fr': 'Morbihan'},
    {'code': '57', 'name': 'Moselle', 'name_fr': 'Moselle'},
    {'code': '58', 'name': 'Nièvre', 'name_fr': 'Nièvre'},
    {'code': '59', 'name': 'Nord', 'name_fr': 'Nord'},
    {'code': '60', 'name': 'Oise', 'name_fr': 'Oise'},
    {'code': '61', 'name': 'Orne', 'name_fr': 'Orne'},
    {'code': '62', 'name': 'Pas-de-Calais', 'name_fr': 'Pas-de-Calais'},
    {'code': '63', 'name': 'Puy-de-Dôme', 'name_fr': 'Puy-de-Dôme'},
    {'code': '64', 'name': 'Pyrénées-Atlantiques', 'name_fr': 'Pyrénées-Atlantiques'},
    {'code': '65', 'name': 'Hautes-Pyrénées', 'name_fr': 'Hautes-Pyrénées'},
    {'code': '66', 'name': 'Pyrénées-Orientales', 'name_fr': 'Pyrénées-Orientales'},
    {'code': '67', 'name': 'Bas-Rhin', 'name_fr': 'Bas-Rhin'},
    {'code': '68', 'name': 'Haut-Rhin', 'name_fr': 'Haut-Rhin'},
    {'code': '69', 'name': 'Rhône', 'name_fr': 'Rhône'},
    {'code': '70', 'name': 'Haute-Saône', 'name_fr': 'Haute-Saône'},
    {'code': '71', 'name': 'Saône-et-Loire', 'name_fr': 'Saône-et-Loire'},
    {'code': '72', 'name': 'Sarthe', 'name_fr': 'Sarthe'},
    {'code': '73', 'name': 'Savoie', 'name_fr': 'Savoie'},
    {'code': '74', 'name': 'Haute-Savoie', 'name_fr': 'Haute-Savoie'},
    {'code': '75', 'name': 'Paris', 'name_fr': 'Paris'},
    {'code': '76', 'name': 'Seine-Maritime', 'name_fr': 'Seine-Maritime'},
    {'code': '77', 'name': 'Seine-et-Marne', 'name_fr': 'Seine-et-Marne'},
    {'code': '78', 'name': 'Yvelines', 'name_fr': 'Yvelines'},
    {'code': '79', 'name': 'Deux-Sèvres', 'name_fr': 'Deux-Sèvres'},
    {'code': '80', 'name': 'Somme', 'name_fr': 'Somme'},
    {'code': '81', 'name': 'Tarn', 'name_fr': 'Tarn'},
    {'code': '82', 'name': 'Tarn-et-Garonne', 'name_fr': 'Tarn-et-Garonne'},
    {'code': '83', 'name': 'Var', 'name_fr': 'Var'},
    {'code': '84', 'name': 'Vaucluse', 'name_fr': 'Vaucluse'},
    {'code': '85', 'name': 'Vendée', 'name_fr': 'Vendée'},
    {'code': '86', 'name': 'Vienne', 'name_fr': 'Vienne'},
    {'code': '87', 'name': 'Haute-Vienne', 'name_fr': 'Haute-Vienne'},
    {'code': '88', 'name': 'Vosges', 'name_fr': 'Vosges'},
    {'code': '89', 'name': 'Yonne', 'name_fr': 'Yonne'},
    {'code': '90', 'name': 'Territoire de Belfort', 'name_fr': 'Territoire de Belfort'},
    {'code': '91', 'name': 'Essonne', 'name_fr': 'Essonne'},
    {'code': '92', 'name': 'Hauts-de-Seine', 'name_fr': 'Hauts-de-Seine'},
    {'code': '93', 'name': 'Seine-Saint-Denis', 'name_fr': 'Seine-Saint-Denis'},
    {'code': '94', 'name': 'Val-de-Marne', 'name_fr': 'Val-de-Marne'},
    {'code': '95', 'name': 'Val-d\'Oise', 'name_fr': 'Val-d\'Oise'},
]

# Wilayas algériennes (48 wilayas)
ALGERIAN_WILAYAS = [
    {'code': '01', 'name': 'Adrar', 'name_fr': 'Adrar'},
    {'code': '02', 'name': 'Chlef', 'name_fr': 'Chlef'},
    {'code': '03', 'name': 'Laghouat', 'name_fr': 'Laghouat'},
    {'code': '04', 'name': 'Oum El Bouaghi', 'name_fr': 'Oum El Bouaghi'},
    {'code': '05', 'name': 'Batna', 'name_fr': 'Batna'},
    {'code': '06', 'name': 'Béjaïa', 'name_fr': 'Béjaïa'},
    {'code': '07', 'name': 'Biskra', 'name_fr': 'Biskra'},
    {'code': '08', 'name': 'Béchar', 'name_fr': 'Béchar'},
    {'code': '09', 'name': 'Blida', 'name_fr': 'Blida'},
    {'code': '10', 'name': 'Bouira', 'name_fr': 'Bouira'},
    {'code': '11', 'name': 'Tamanrasset', 'name_fr': 'Tamanrasset'},
    {'code': '12', 'name': 'Tébessa', 'name_fr': 'Tébessa'},
    {'code': '13', 'name': 'Tlemcen', 'name_fr': 'Tlemcen'},
    {'code': '14', 'name': 'Tiaret', 'name_fr': 'Tiaret'},
    {'code': '15', 'name': 'Tizi Ouzou', 'name_fr': 'Tizi Ouzou'},
    {'code': '16', 'name': 'Alger', 'name_fr': 'Alger'},
    {'code': '17', 'name': 'Djelfa', 'name_fr': 'Djelfa'},
    {'code': '18', 'name': 'Jijel', 'name_fr': 'Jijel'},
    {'code': '19', 'name': 'Sétif', 'name_fr': 'Sétif'},
    {'code': '20', 'name': 'Saïda', 'name_fr': 'Saïda'},
    {'code': '21', 'name': 'Skikda', 'name_fr': 'Skikda'},
    {'code': '22', 'name': 'Sidi Bel Abbès', 'name_fr': 'Sidi Bel Abbès'},
    {'code': '23', 'name': 'Annaba', 'name_fr': 'Annaba'},
    {'code': '24', 'name': 'Guelma', 'name_fr': 'Guelma'},
    {'code': '25', 'name': 'Constantine', 'name_fr': 'Constantine'},
    {'code': '26', 'name': 'Médéa', 'name_fr': 'Médéa'},
    {'code': '27', 'name': 'Mostaganem', 'name_fr': 'Mostaganem'},
    {'code': '28', 'name': 'M\'Sila', 'name_fr': 'M\'Sila'},
    {'code': '29', 'name': 'Mascara', 'name_fr': 'Mascara'},
    {'code': '30', 'name': 'Ouargla', 'name_fr': 'Ouargla'},
    {'code': '31', 'name': 'Oran', 'name_fr': 'Oran'},
    {'code': '32', 'name': 'El Bayadh', 'name_fr': 'El Bayadh'},
    {'code': '33', 'name': 'Illizi', 'name_fr': 'Illizi'},
    {'code': '34', 'name': 'Bordj Bou Arréridj', 'name_fr': 'Bordj Bou Arréridj'},
    {'code': '35', 'name': 'Boumerdès', 'name_fr': 'Boumerdès'},
    {'code': '36', 'name': 'El Tarf', 'name_fr': 'El Tarf'},
    {'code': '37', 'name': 'Tindouf', 'name_fr': 'Tindouf'},
    {'code': '38', 'name': 'Tissemsilt', 'name_fr': 'Tissemsilt'},
    {'code': '39', 'name': 'El Oued', 'name_fr': 'El Oued'},
    {'code': '40', 'name': 'Khenchela', 'name_fr': 'Khenchela'},
    {'code': '41', 'name': 'Souk Ahras', 'name_fr': 'Souk Ahras'},
    {'code': '42', 'name': 'Tipaza', 'name_fr': 'Tipaza'},
    {'code': '43', 'name': 'Mila', 'name_fr': 'Mila'},
    {'code': '44', 'name': 'Aïn Defla', 'name_fr': 'Aïn Defla'},
    {'code': '45', 'name': 'Naâma', 'name_fr': 'Naâma'},
    {'code': '46', 'name': 'Aïn Témouchent', 'name_fr': 'Aïn Témouchent'},
    {'code': '47', 'name': 'Ghardaïa', 'name_fr': 'Ghardaïa'},
    {'code': '48', 'name': 'Relizane', 'name_fr': 'Relizane'},
]

# Provinces marocaines (principales)
MOROCCAN_PROVINCES = [
    {'code': 'RAB', 'name': 'Rabat-Salé-Kénitra', 'name_fr': 'Rabat-Salé-Kénitra'},
    {'code': 'CAS', 'name': 'Casablanca-Settat', 'name_fr': 'Casablanca-Settat'},
    {'code': 'FES', 'name': 'Fès-Meknès', 'name_fr': 'Fès-Meknès'},
    {'code': 'MAR', 'name': 'Marrakech-Safi', 'name_fr': 'Marrakech-Safi'},
    {'code': 'TAN', 'name': 'Tanger-Tétouan-Al Hoceïma', 'name_fr': 'Tanger-Tétouan-Al Hoceïma'},
    {'code': 'ORI', 'name': 'Oriental', 'name_fr': 'Oriental'},
    {'code': 'SOU', 'name': 'Souss-Massa', 'name_fr': 'Souss-Massa'},
    {'code': 'DRA', 'name': 'Drâa-Tafilalet', 'name_fr': 'Drâa-Tafilalet'},
    {'code': 'BEN', 'name': 'Béni Mellal-Khénifra', 'name_fr': 'Béni Mellal-Khénifra'},
    {'code': 'LAA', 'name': 'Laâyoune-Sakia El Hamra', 'name_fr': 'Laâyoune-Sakia El Hamra'},
    {'code': 'DAK', 'name': 'Dakhla-Oued Ed-Dahab', 'name_fr': 'Dakhla-Oued Ed-Dahab'},
    {'code': 'GUE', 'name': 'Guelmim-Oued Noun', 'name_fr': 'Guelmim-Oued Noun'},
]

# Gouvernorats tunisiens
TUNISIAN_GOVERNORATES = [
    {'code': '11', 'name': 'Tunis', 'name_fr': 'Tunis'},
    {'code': '12', 'name': 'Ariana', 'name_fr': 'Ariana'},
    {'code': '13', 'name': 'Ben Arous', 'name_fr': 'Ben Arous'},
    {'code': '14', 'name': 'Manouba', 'name_fr': 'Manouba'},
    {'code': '21', 'name': 'Nabeul', 'name_fr': 'Nabeul'},
    {'code': '22', 'name': 'Zaghouan', 'name_fr': 'Zaghouan'},
    {'code': '23', 'name': 'Bizerte', 'name_fr': 'Bizerte'},
    {'code': '31', 'name': 'Béja', 'name_fr': 'Béja'},
    {'code': '32', 'name': 'Jendouba', 'name_fr': 'Jendouba'},
    {'code': '33', 'name': 'Kef', 'name_fr': 'Kef'},
    {'code': '34', 'name': 'Siliana', 'name_fr': 'Siliana'},
    {'code': '41', 'name': 'Kairouan', 'name_fr': 'Kairouan'},
    {'code': '42', 'name': 'Kasserine', 'name_fr': 'Kasserine'},
    {'code': '43', 'name': 'Sidi Bouzid', 'name_fr': 'Sidi Bouzid'},
    {'code': '51', 'name': 'Sousse', 'name_fr': 'Sousse'},
    {'code': '52', 'name': 'Monastir', 'name_fr': 'Monastir'},
    {'code': '53', 'name': 'Mahdia', 'name_fr': 'Mahdia'},
    {'code': '61', 'name': 'Sfax', 'name_fr': 'Sfax'},
    {'code': '71', 'name': 'Gafsa', 'name_fr': 'Gafsa'},
    {'code': '72', 'name': 'Tozeur', 'name_fr': 'Tozeur'},
    {'code': '73', 'name': 'Kebili', 'name_fr': 'Kebili'},
    {'code': '81', 'name': 'Gabès', 'name_fr': 'Gabès'},
    {'code': '82', 'name': 'Médenine', 'name_fr': 'Médenine'},
    {'code': '83', 'name': 'Tataouine', 'name_fr': 'Tataouine'},
]

# Mapping des départements par pays
DEPARTMENTS_BY_COUNTRY = {
    'FR': FRENCH_DEPARTMENTS,
    'DZ': ALGERIAN_WILAYAS,
    'MA': MOROCCAN_PROVINCES,
    'TN': TUNISIAN_GOVERNORATES,
    # Pour les autres pays, on peut ajouter des subdivisions simplifiées
    'BE': [
        {'code': 'BRU', 'name': 'Bruxelles-Capitale', 'name_fr': 'Bruxelles-Capitale'},
        {'code': 'VLG', 'name': 'Flandre', 'name_fr': 'Flandre'},
        {'code': 'WAL', 'name': 'Wallonie', 'name_fr': 'Wallonie'},
    ],
    'CH': [
        {'code': 'ZH', 'name': 'Zurich', 'name_fr': 'Zurich'},
        {'code': 'BE', 'name': 'Berne', 'name_fr': 'Berne'},
        {'code': 'LU', 'name': 'Lucerne', 'name_fr': 'Lucerne'},
        {'code': 'GE', 'name': 'Genève', 'name_fr': 'Genève'},
        {'code': 'VD', 'name': 'Vaud', 'name_fr': 'Vaud'},
        {'code': 'VS', 'name': 'Valais', 'name_fr': 'Valais'},
    ],
    'LU': [
        {'code': 'LU', 'name': 'Luxembourg', 'name_fr': 'Luxembourg'},
    ],
    'ES': [
        {'code': 'MAD', 'name': 'Madrid', 'name_fr': 'Madrid'},
        {'code': 'BCN', 'name': 'Barcelone', 'name_fr': 'Barcelone'},
        {'code': 'VAL', 'name': 'Valence', 'name_fr': 'Valence'},
        {'code': 'SEV', 'name': 'Séville', 'name_fr': 'Séville'},
    ],
    'IT': [
        {'code': 'ROM', 'name': 'Rome', 'name_fr': 'Rome'},
        {'code': 'MIL', 'name': 'Milan', 'name_fr': 'Milan'},
        {'code': 'NAP', 'name': 'Naples', 'name_fr': 'Naples'},
        {'code': 'TOR', 'name': 'Turin', 'name_fr': 'Turin'},
    ],
    'DE': [
        {'code': 'BER', 'name': 'Berlin', 'name_fr': 'Berlin'},
        {'code': 'BAY', 'name': 'Bavière', 'name_fr': 'Bavière'},
        {'code': 'NRW', 'name': 'Rhénanie-du-Nord-Westphalie', 'name_fr': 'Rhénanie-du-Nord-Westphalie'},
        {'code': 'BW', 'name': 'Bade-Wurtemberg', 'name_fr': 'Bade-Wurtemberg'},
    ]
}
