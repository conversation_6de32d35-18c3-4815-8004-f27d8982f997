{"indexes": [{"collectionGroup": "freight_offers", "queryScope": "COLLECTION", "fields": [{"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "created_at", "order": "DESCENDING"}]}, {"collectionGroup": "freight_offers", "queryScope": "COLLECTION", "fields": [{"fieldPath": "pickup_country_id", "order": "ASCENDING"}, {"fieldPath": "delivery_country_id", "order": "ASCENDING"}, {"fieldPath": "created_at", "order": "DESCENDING"}]}, {"collectionGroup": "freight_proposals", "queryScope": "COLLECTION", "fields": [{"fieldPath": "offer_id", "order": "ASCENDING"}, {"fieldPath": "proposed_price", "order": "ASCENDING"}]}, {"collectionGroup": "freight_proposals", "queryScope": "COLLECTION", "fields": [{"fieldPath": "transporter_id", "order": "ASCENDING"}, {"fieldPath": "created_at", "order": "DESCENDING"}]}, {"collectionGroup": "users", "queryScope": "COLLECTION", "fields": [{"fieldPath": "user_type", "order": "ASCENDING"}, {"fieldPath": "created_at", "order": "DESCENDING"}]}, {"collectionGroup": "departments", "queryScope": "COLLECTION", "fields": [{"fieldPath": "country_id", "order": "ASCENDING"}, {"fieldPath": "name_fr", "order": "ASCENDING"}]}], "fieldOverrides": []}