#!/usr/bin/env python3
"""
Script de test pour vérifier le déploiement Firebase de SABTRANS
Teste toutes les fonctionnalités principales en production
"""

import requests
import json
import time
from datetime import datetime

class FirebaseDeploymentTester:
    def __init__(self, base_url):
        self.base_url = base_url.rstrip('/')
        self.session = requests.Session()
        self.test_results = []
    
    def log_test(self, test_name, success, details=""):
        """Enregistrer le résultat d'un test"""
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{status} {test_name}")
        if details:
            print(f"   📋 {details}")
        
        self.test_results.append({
            'test': test_name,
            'success': success,
            'details': details,
            'timestamp': datetime.now().isoformat()
        })
    
    def test_homepage(self):
        """Tester la page d'accueil"""
        print("\n🏠 Test de la page d'accueil...")
        try:
            response = self.session.get(f"{self.base_url}/")
            success = response.status_code == 200
            details = f"Status: {response.status_code}"
            
            if success:
                # Vérifier le contenu
                content = response.text.lower()
                if 'sabtrans' in content and 'transport' in content:
                    details += " - Contenu correct"
                else:
                    success = False
                    details += " - Contenu incorrect"
            
            self.log_test("Page d'accueil", success, details)
            return success
            
        except Exception as e:
            self.log_test("Page d'accueil", False, f"Erreur: {e}")
            return False
    
    def test_api_countries(self):
        """Tester l'API des pays"""
        print("\n🌍 Test de l'API Countries...")
        try:
            response = self.session.get(f"{self.base_url}/api/countries")
            success = response.status_code == 200
            details = f"Status: {response.status_code}"
            
            if success:
                data = response.json()
                if data.get('success') and 'countries' in data:
                    countries = data['countries']
                    details += f" - {len(countries)} pays trouvés"
                    
                    # Vérifier la structure des données
                    if countries and all('code' in c and 'name' in c for c in countries):
                        details += " - Structure correcte"
                    else:
                        success = False
                        details += " - Structure incorrecte"
                else:
                    success = False
                    details += " - Format de réponse incorrect"
            
            self.log_test("API Countries", success, details)
            return success
            
        except Exception as e:
            self.log_test("API Countries", False, f"Erreur: {e}")
            return False
    
    def test_api_currencies(self):
        """Tester l'API des devises"""
        print("\n💰 Test de l'API Currencies...")
        try:
            response = self.session.get(f"{self.base_url}/api/currencies")
            success = response.status_code == 200
            details = f"Status: {response.status_code}"
            
            if success:
                data = response.json()
                if data.get('success') and 'currencies' in data:
                    currencies = data['currencies']
                    details += f" - {len(currencies)} devises trouvées"
                    
                    # Vérifier les devises principales
                    currency_codes = [c.get('code') for c in currencies]
                    required_currencies = ['EUR', 'DZD', 'USD']
                    missing = [code for code in required_currencies if code not in currency_codes]
                    
                    if not missing:
                        details += " - Devises principales présentes"
                    else:
                        success = False
                        details += f" - Devises manquantes: {missing}"
                else:
                    success = False
                    details += " - Format de réponse incorrect"
            
            self.log_test("API Currencies", success, details)
            return success
            
        except Exception as e:
            self.log_test("API Currencies", False, f"Erreur: {e}")
            return False
    
    def test_api_departments(self):
        """Tester l'API des départements"""
        print("\n🏛️ Test de l'API Departments...")
        try:
            # Tester avec la France
            response = self.session.get(f"{self.base_url}/api/countries/FR/departments")
            success = response.status_code == 200
            details = f"Status: {response.status_code}"
            
            if success:
                data = response.json()
                if data.get('success') and 'departments' in data:
                    departments = data['departments']
                    details += f" - {len(departments)} départements français"
                    
                    if len(departments) > 90:  # La France a 94+ départements
                        details += " - Nombre correct"
                    else:
                        success = False
                        details += " - Nombre insuffisant"
                else:
                    success = False
                    details += " - Format de réponse incorrect"
            
            self.log_test("API Departments (FR)", success, details)
            
            # Tester avec l'Algérie
            response_dz = self.session.get(f"{self.base_url}/api/countries/DZ/departments")
            success_dz = response_dz.status_code == 200
            details_dz = f"Status: {response_dz.status_code}"
            
            if success_dz:
                data_dz = response_dz.json()
                if data_dz.get('success') and 'departments' in data_dz:
                    wilayas = data_dz['departments']
                    details_dz += f" - {len(wilayas)} wilayas algériennes"
                else:
                    success_dz = False
                    details_dz += " - Format de réponse incorrect"
            
            self.log_test("API Departments (DZ)", success_dz, details_dz)
            return success and success_dz
            
        except Exception as e:
            self.log_test("API Departments", False, f"Erreur: {e}")
            return False
    
    def test_auth_pages(self):
        """Tester les pages d'authentification"""
        print("\n🔐 Test des pages d'authentification...")
        
        auth_pages = [
            ('/auth/login', 'Page de connexion'),
            ('/auth/register', 'Page d\'inscription')
        ]
        
        all_success = True
        for path, name in auth_pages:
            try:
                response = self.session.get(f"{self.base_url}{path}")
                success = response.status_code == 200
                details = f"Status: {response.status_code}"
                
                if success:
                    content = response.text.lower()
                    if 'email' in content and 'password' in content:
                        details += " - Formulaire présent"
                    else:
                        success = False
                        details += " - Formulaire manquant"
                
                self.log_test(name, success, details)
                all_success = all_success and success
                
            except Exception as e:
                self.log_test(name, False, f"Erreur: {e}")
                all_success = False
        
        return all_success
    
    def test_static_resources(self):
        """Tester les ressources statiques"""
        print("\n📁 Test des ressources statiques...")
        
        static_resources = [
            ('/static/css/style.css', 'CSS principal'),
            ('/static/js/location.js', 'JavaScript localisation'),
            ('/static/js/currency.js', 'JavaScript devises')
        ]
        
        all_success = True
        for path, name in static_resources:
            try:
                response = self.session.get(f"{self.base_url}{path}")
                success = response.status_code == 200
                details = f"Status: {response.status_code}"
                
                if success:
                    content_length = len(response.content)
                    details += f" - Taille: {content_length} bytes"
                    
                    if content_length > 100:  # Fichier non vide
                        details += " - Contenu présent"
                    else:
                        success = False
                        details += " - Fichier vide"
                
                self.log_test(name, success, details)
                all_success = all_success and success
                
            except Exception as e:
                self.log_test(name, False, f"Erreur: {e}")
                all_success = False
        
        return all_success
    
    def test_performance(self):
        """Tester les performances"""
        print("\n⚡ Test de performance...")
        
        try:
            start_time = time.time()
            response = self.session.get(f"{self.base_url}/")
            end_time = time.time()
            
            response_time = (end_time - start_time) * 1000  # en ms
            success = response.status_code == 200 and response_time < 5000  # < 5 secondes
            
            details = f"Temps de réponse: {response_time:.0f}ms"
            if response_time < 1000:
                details += " - Excellent"
            elif response_time < 3000:
                details += " - Bon"
            elif response_time < 5000:
                details += " - Acceptable"
            else:
                details += " - Lent"
            
            self.log_test("Performance page d'accueil", success, details)
            return success
            
        except Exception as e:
            self.log_test("Performance", False, f"Erreur: {e}")
            return False
    
    def run_all_tests(self):
        """Exécuter tous les tests"""
        print("🧪 DÉMARRAGE DES TESTS DE DÉPLOIEMENT FIREBASE")
        print("=" * 60)
        print(f"🌐 URL testée: {self.base_url}")
        print(f"⏰ Heure: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("=" * 60)
        
        tests = [
            self.test_homepage,
            self.test_api_countries,
            self.test_api_currencies,
            self.test_api_departments,
            self.test_auth_pages,
            self.test_static_resources,
            self.test_performance
        ]
        
        results = []
        for test in tests:
            try:
                result = test()
                results.append(result)
            except Exception as e:
                print(f"❌ Erreur inattendue dans {test.__name__}: {e}")
                results.append(False)
        
        # Résumé final
        print("\n" + "=" * 60)
        print("📊 RÉSUMÉ DES TESTS")
        print("=" * 60)
        
        passed = sum(results)
        total = len(results)
        success_rate = (passed / total) * 100
        
        print(f"✅ Tests réussis: {passed}/{total} ({success_rate:.1f}%)")
        
        if success_rate >= 90:
            print("🎉 EXCELLENT: Déploiement Firebase réussi!")
            print("✅ Toutes les fonctionnalités principales sont opérationnelles")
        elif success_rate >= 70:
            print("⚠️  BON: Déploiement majoritairement réussi")
            print("🔧 Quelques ajustements peuvent être nécessaires")
        else:
            print("❌ PROBLÈME: Plusieurs tests ont échoué")
            print("🛠️  Vérifiez la configuration et les logs Firebase")
        
        # Détails des échecs
        failed_tests = [self.test_results[i] for i, result in enumerate(results) if not result]
        if failed_tests:
            print(f"\n❌ Tests échoués ({len(failed_tests)}):")
            for test in failed_tests:
                print(f"   - {test['test']}: {test['details']}")
        
        print("\n📋 Recommandations:")
        if success_rate >= 90:
            print("   - Configurez le monitoring en production")
            print("   - Programmez des sauvegardes régulières")
            print("   - Configurez un domaine personnalisé")
        else:
            print("   - Vérifiez les logs Firebase Console")
            print("   - Contrôlez la configuration des Cloud Functions")
            print("   - Vérifiez les règles Firestore")
        
        print("=" * 60)
        return success_rate >= 70

def main():
    """Fonction principale"""
    import sys
    
    if len(sys.argv) != 2:
        print("Usage: python test_firebase_deployment.py <URL_APPLICATION>")
        print("Exemple: python test_firebase_deployment.py https://sabtrans-app.web.app")
        sys.exit(1)
    
    base_url = sys.argv[1]
    tester = FirebaseDeploymentTester(base_url)
    
    success = tester.run_all_tests()
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
