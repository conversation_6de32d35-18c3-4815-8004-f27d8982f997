# 🚀 COMMANDES RAPIDES DE DÉPLOIEMENT FIREBASE

## ⚡ DÉPLOIEMENT AUTOMATIQUE (RECOMMANDÉ)

```bash
# Exécuter le script d'installation automatique
python deploy_to_firebase.py
```

---

## 🔧 DÉPLOIEMENT MANUEL ÉTAPE PAR ÉTAPE

### 1. Prérequis
```bash
# Installer Firebase CLI
npm install -g firebase-tools

# Se connecter à Firebase
firebase login

# Vérifier la connexion
firebase projects:list
```

### 2. Configuration Initiale
```bash
# Initialiser Firebase dans le projet
firebase init

# Configurer le projet
firebase use --add
# Sélectionner votre projet Firebase
```

### 3. Migration des Données
```bash
# Configurer les credentials (remplacer par votre chemin)
export GOOGLE_APPLICATION_CREDENTIALS="path/to/serviceAccountKey.json"

# Migrer les données MySQL vers Firestore
python migrate_to_firestore.py
```

### 4. Déploiement
```bash
# Déployer tout
firebase deploy

# Ou déployer par service:
firebase deploy --only firestore        # Règles et index
firebase deploy --only functions        # Cloud Functions
firebase deploy --only hosting          # Frontend
firebase deploy --only storage          # Règles de stockage
```

---

## 🔍 COMMANDES DE VÉRIFICATION

### Statut du Déploiement
```bash
# Vérifier le statut des services
firebase projects:list
firebase functions:list
firebase hosting:channel:list

# Voir les logs
firebase functions:log
firebase functions:log --only api
```

### Tests de Base
```bash
# Tester l'application (remplacer PROJECT_ID)
curl https://PROJECT_ID.web.app/
curl https://PROJECT_ID.web.app/api/countries
curl https://PROJECT_ID.web.app/api/currencies
```

---

## 🛠️ COMMANDES DE MAINTENANCE

### Mise à Jour
```bash
# Mettre à jour les functions seulement
firebase deploy --only functions

# Mettre à jour le frontend seulement
firebase deploy --only hosting

# Redéploiement complet
firebase deploy
```

### Monitoring
```bash
# Logs en temps réel
firebase functions:log --follow

# Logs spécifiques à une function
firebase functions:log --only api

# Informations sur les quotas
firebase projects:list
```

### Sauvegardes
```bash
# Exporter Firestore (remplacer BUCKET_NAME)
gcloud firestore export gs://BUCKET_NAME/backup-$(date +%Y%m%d)

# Lister les sauvegardes
gsutil ls gs://BUCKET_NAME/
```

---

## 🚨 DÉPANNAGE RAPIDE

### Problèmes Courants
```bash
# Réinitialiser la configuration Firebase
firebase logout
firebase login
firebase use PROJECT_ID

# Forcer le redéploiement
firebase deploy --force

# Vérifier les permissions
firebase projects:list
gcloud auth list
```

### Erreurs de Functions
```bash
# Voir les erreurs détaillées
firebase functions:log --only api

# Tester localement
cd functions
functions-framework --target=api --debug
```

### Erreurs de Hosting
```bash
# Vérifier la configuration
cat firebase.json
cat .firebaserc

# Redéployer le hosting
firebase deploy --only hosting --debug
```

---

## 📋 CHECKLIST DE DÉPLOIEMENT

### Avant le Déploiement
- [ ] Projet Firebase créé
- [ ] Facturation activée
- [ ] Firebase CLI installé
- [ ] Credentials configurés
- [ ] Données MySQL prêtes

### Pendant le Déploiement
- [ ] Migration des données réussie
- [ ] Functions déployées sans erreur
- [ ] Hosting déployé
- [ ] Règles Firestore configurées
- [ ] Tests de base passés

### Après le Déploiement
- [ ] Application accessible
- [ ] APIs fonctionnelles
- [ ] Authentification opérationnelle
- [ ] Données visibles dans Firestore
- [ ] Monitoring configuré

---

## 🎯 URLS IMPORTANTES

### Console Firebase
- **Projet**: https://console.firebase.google.com/project/PROJECT_ID
- **Firestore**: https://console.firebase.google.com/project/PROJECT_ID/firestore
- **Functions**: https://console.firebase.google.com/project/PROJECT_ID/functions
- **Hosting**: https://console.firebase.google.com/project/PROJECT_ID/hosting

### Application Déployée
- **URL principale**: https://PROJECT_ID.web.app
- **URL alternative**: https://PROJECT_ID.firebaseapp.com

### APIs
- **Countries**: https://PROJECT_ID.web.app/api/countries
- **Currencies**: https://PROJECT_ID.web.app/api/currencies
- **Departments**: https://PROJECT_ID.web.app/api/countries/FR/departments

---

## 💡 CONSEILS

### Performance
- Utilisez les index Firestore pour optimiser les requêtes
- Configurez le cache pour les ressources statiques
- Surveillez les quotas de Cloud Functions

### Sécurité
- Révisez régulièrement les règles Firestore
- Utilisez HTTPS uniquement
- Configurez les CORS appropriés

### Monitoring
- Activez les alertes dans Firebase Console
- Surveillez les logs d'erreur
- Configurez des sauvegardes automatiques

---

## 🆘 SUPPORT

### Documentation
- **Firebase**: https://firebase.google.com/docs
- **Cloud Functions**: https://firebase.google.com/docs/functions
- **Firestore**: https://firebase.google.com/docs/firestore

### Communauté
- **Stack Overflow**: Tag `firebase`
- **GitHub**: Issues Firebase
- **Discord**: Firebase Community

---

**Remplacez `PROJECT_ID` par l'ID réel de votre projet Firebase dans toutes les commandes ! 🔧**
