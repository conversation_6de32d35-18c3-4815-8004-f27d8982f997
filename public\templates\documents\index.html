{% extends "base.html" %}

{% block title %}Mes Documents - SABTRANS{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 text-primary">
                        <i class="fas fa-file-alt me-2"></i>Gestion Documentaire
                    </h1>
                    <p class="text-muted">Gérez vos documents de transport et logistique</p>
                </div>
                <div>
                    <a href="{{ url_for('documents.upload') }}" class="btn btn-success">
                        <i class="fas fa-upload me-2"></i>Nouveau Document
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Filtres -->
    <div class="card mb-4">
        <div class="card-header">
            <h5 class="mb-0">
                <i class="fas fa-filter me-2"></i>Filtres
            </h5>
        </div>
        <div class="card-body">
            <form method="GET" action="{{ url_for('documents.index') }}">
                <div class="row">
                    <div class="col-md-4 mb-3">
                        <label for="search" class="form-label">Recherche</label>
                        <input type="text" class="form-control" id="search" name="search" 
                               value="{{ search_query }}" placeholder="Nom, description, tags...">
                    </div>
                    <div class="col-md-3 mb-3">
                        <label for="type" class="form-label">Type de document</label>
                        <select class="form-select" id="type" name="type">
                            <option value="">Tous les types</option>
                            {% for type_value, type_label in document_types %}
                                <option value="{{ type_value }}" {% if current_type == type_value %}selected{% endif %}>
                                    {{ type_label }}
                                </option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="col-md-3 mb-3 d-flex align-items-end">
                        <button type="submit" class="btn btn-primary w-100">
                            <i class="fas fa-search me-2"></i>Rechercher
                        </button>
                    </div>
                    <div class="col-md-2 mb-3 d-flex align-items-end">
                        <a href="{{ url_for('documents.index') }}" class="btn btn-outline-secondary w-100">
                            <i class="fas fa-times me-2"></i>Reset
                        </a>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Statistiques rapides -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">Total Documents</h6>
                            <h3>{{ documents.total }}</h3>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-file fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">Signés</h6>
                            <h3>0</h3>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-signature fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">En attente</h6>
                            <h3>0</h3>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-clock fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">Ce mois</h6>
                            <h3>{{ documents.items|length }}</h3>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-calendar fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Liste des documents -->
    <div class="row">
        <div class="col-12">
            {% if documents.items %}
                <!-- Options d'affichage -->
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <p class="text-muted mb-0">
                        {{ documents.total }} document(s) - Page {{ documents.page }} sur {{ documents.pages }}
                    </p>
                    <div class="btn-group btn-group-sm" role="group">
                        <input type="radio" class="btn-check" name="view" id="view-grid" checked>
                        <label class="btn btn-outline-primary" for="view-grid">
                            <i class="fas fa-th"></i>
                        </label>
                        <input type="radio" class="btn-check" name="view" id="view-list">
                        <label class="btn btn-outline-primary" for="view-list">
                            <i class="fas fa-list"></i>
                        </label>
                    </div>
                </div>

                <!-- Grille des documents -->
                <div class="row" id="documents-grid">
                    {% for document in documents.items %}
                    <div class="col-lg-3 col-md-4 col-sm-6 mb-4">
                        <div class="card h-100 document-card">
                            <div class="card-body">
                                <!-- Icône du type de document -->
                                <div class="text-center mb-3">
                                    <div class="document-icon mx-auto 
                                        {% if document.is_pdf() %}pdf
                                        {% elif document.is_image() %}image
                                        {% elif document.get_file_extension() in ['.doc', '.docx'] %}doc
                                        {% elif document.get_file_extension() in ['.xls', '.xlsx'] %}excel
                                        {% else %}pdf{% endif %}">
                                        {% if document.is_pdf() %}
                                            <i class="fas fa-file-pdf"></i>
                                        {% elif document.is_image() %}
                                            <i class="fas fa-file-image"></i>
                                        {% elif document.get_file_extension() in ['.doc', '.docx'] %}
                                            <i class="fas fa-file-word"></i>
                                        {% elif document.get_file_extension() in ['.xls', '.xlsx'] %}
                                            <i class="fas fa-file-excel"></i>
                                        {% else %}
                                            <i class="fas fa-file"></i>
                                        {% endif %}
                                    </div>
                                </div>

                                <!-- Informations du document -->
                                <h6 class="card-title text-truncate" title="{{ document.name }}">
                                    {{ document.name }}
                                </h6>
                                
                                <div class="mb-2">
                                    <span class="badge bg-secondary">{{ document.document_type.upper() }}</span>
                                    {% if document.is_signed %}
                                        <span class="badge bg-success">Signé</span>
                                    {% endif %}
                                    {% if document.is_public %}
                                        <span class="badge bg-info">Public</span>
                                    {% endif %}
                                </div>

                                <p class="text-muted small mb-2">
                                    <i class="fas fa-calendar me-1"></i>
                                    {{ document.created_at.strftime('%d/%m/%Y') }}
                                </p>

                                <p class="text-muted small mb-2">
                                    <i class="fas fa-hdd me-1"></i>
                                    {{ document.get_size_formatted() }}
                                </p>

                                {% if document.description %}
                                <p class="text-muted small mb-3">
                                    {{ document.description[:50] }}{% if document.description|length > 50 %}...{% endif %}
                                </p>
                                {% endif %}

                                <!-- Tags -->
                                {% if document.get_tags() %}
                                <div class="mb-3">
                                    {% for tag in document.get_tags()[:2] %}
                                        <span class="badge bg-light text-dark">{{ tag }}</span>
                                    {% endfor %}
                                    {% if document.get_tags()|length > 2 %}
                                        <span class="badge bg-light text-dark">+{{ document.get_tags()|length - 2 }}</span>
                                    {% endif %}
                                </div>
                                {% endif %}
                            </div>

                            <!-- Actions -->
                            <div class="card-footer bg-transparent">
                                <div class="d-grid gap-1">
                                    <a href="{{ url_for('documents.detail', document_id=document.id) }}" 
                                       class="btn btn-primary btn-sm">
                                        <i class="fas fa-eye me-1"></i>Voir
                                    </a>
                                    <div class="btn-group" role="group">
                                        <a href="{{ url_for('documents.download', document_id=document.id) }}" 
                                           class="btn btn-outline-success btn-sm">
                                            <i class="fas fa-download"></i>
                                        </a>
                                        {% if not document.is_signed %}
                                        <button class="btn btn-outline-warning btn-sm" 
                                                onclick="signDocument({{ document.id }})">
                                            <i class="fas fa-signature"></i>
                                        </button>
                                        {% endif %}
                                        <button class="btn btn-outline-danger btn-sm" 
                                                onclick="deleteDocument({{ document.id }})">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>

                <!-- Pagination -->
                {% if documents.pages > 1 %}
                <nav aria-label="Navigation des documents">
                    <ul class="pagination justify-content-center">
                        {% if documents.has_prev %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('documents.index', page=documents.prev_num, search=search_query, type=current_type) }}">
                                    <i class="fas fa-chevron-left"></i>
                                </a>
                            </li>
                        {% endif %}
                        
                        {% for page_num in documents.iter_pages() %}
                            {% if page_num %}
                                {% if page_num != documents.page %}
                                    <li class="page-item">
                                        <a class="page-link" href="{{ url_for('documents.index', page=page_num, search=search_query, type=current_type) }}">
                                            {{ page_num }}
                                        </a>
                                    </li>
                                {% else %}
                                    <li class="page-item active">
                                        <span class="page-link">{{ page_num }}</span>
                                    </li>
                                {% endif %}
                            {% else %}
                                <li class="page-item disabled">
                                    <span class="page-link">...</span>
                                </li>
                            {% endif %}
                        {% endfor %}
                        
                        {% if documents.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('documents.index', page=documents.next_num, search=search_query, type=current_type) }}">
                                    <i class="fas fa-chevron-right"></i>
                                </a>
                            </li>
                        {% endif %}
                    </ul>
                </nav>
                {% endif %}

            {% else %}
                <!-- Aucun document -->
                <div class="text-center py-5">
                    <i class="fas fa-file-alt fa-3x text-muted mb-3"></i>
                    <h4 class="text-muted">Aucun document trouvé</h4>
                    <p class="text-muted">
                        {% if search_query or current_type %}
                            Aucun document ne correspond à vos critères de recherche.
                        {% else %}
                            Vous n'avez pas encore uploadé de documents.
                        {% endif %}
                    </p>
                    <a href="{{ url_for('documents.upload') }}" class="btn btn-success">
                        <i class="fas fa-upload me-2"></i>Uploader un document
                    </a>
                </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function signDocument(documentId) {
    if (confirm('Voulez-vous signer ce document électroniquement ?')) {
        // Ici on pourrait implémenter une signature électronique
        alert('Fonctionnalité de signature en cours de développement');
    }
}

function deleteDocument(documentId) {
    if (confirm('Êtes-vous sûr de vouloir supprimer ce document ? Cette action est irréversible.')) {
        // Créer un formulaire pour la suppression
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = `/documents/${documentId}/delete`;
        document.body.appendChild(form);
        form.submit();
    }
}

// Changement de vue
document.addEventListener('DOMContentLoaded', function() {
    const gridView = document.getElementById('view-grid');
    const listView = document.getElementById('view-list');
    const documentsContainer = document.getElementById('documents-grid');
    
    listView.addEventListener('change', function() {
        if (this.checked) {
            documentsContainer.className = 'row';
            // Ici on pourrait implémenter une vue liste
        }
    });
    
    gridView.addEventListener('change', function() {
        if (this.checked) {
            documentsContainer.className = 'row';
        }
    });
});
</script>
{% endblock %}
