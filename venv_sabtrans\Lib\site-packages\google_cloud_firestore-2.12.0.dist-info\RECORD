../../Scripts/__pycache__/fixup_firestore_admin_v1_keywords.cpython-310.pyc,,
../../Scripts/__pycache__/fixup_firestore_v1_keywords.cpython-310.pyc,,
../../Scripts/fixup_firestore_admin_v1_keywords.py,sha256=Yy_kILTcElZUs3ovEVE4iLtHhIl2awYOnEA2MsFFdgM,6798
../../Scripts/fixup_firestore_v1_keywords.py,sha256=L_DkNElpFtkCXj4-CKfCH1SByq342Y4X9e5iji86zgI,7459
google/cloud/firestore/__init__.py,sha256=Xh7wydIH6QtvJqe-nAWgL5T_S5qhCiXdiOt1MxwDR9k,3338
google/cloud/firestore/__pycache__/__init__.cpython-310.pyc,,
google/cloud/firestore/__pycache__/gapic_version.cpython-310.pyc,,
google/cloud/firestore/gapic_version.py,sha256=rVhuW1EKDdRIH8NAUU1H8QG5MC9ywcw6EDAcuTEQQjs,653
google/cloud/firestore_admin_v1/__init__.py,sha256=uL69T_lG86f7NYMy0amW20r8_t0iI9zR-KQfRbAUp3Y,2282
google/cloud/firestore_admin_v1/__pycache__/__init__.cpython-310.pyc,,
google/cloud/firestore_admin_v1/__pycache__/gapic_version.cpython-310.pyc,,
google/cloud/firestore_admin_v1/gapic_metadata.json,sha256=rsV_RSyN_UhUU5fSBceq-1EVQaZXag6JcmQyQ_ce_Ws,5322
google/cloud/firestore_admin_v1/gapic_version.py,sha256=rVhuW1EKDdRIH8NAUU1H8QG5MC9ywcw6EDAcuTEQQjs,653
google/cloud/firestore_admin_v1/py.typed,sha256=986zLtpMXwL-7nXabn6tbnx8AMKbxkX0e3KeCdrySvA,89
google/cloud/firestore_admin_v1/services/__init__.py,sha256=zWbvQwgy48VXnYBlD4x4jlBof70BFYtIC9fehwEW1WQ,600
google/cloud/firestore_admin_v1/services/__pycache__/__init__.cpython-310.pyc,,
google/cloud/firestore_admin_v1/services/firestore_admin/__init__.py,sha256=d2InWZmHCpBSQnSRYaisE0WjeRAOjh18X1gSUK055uU,769
google/cloud/firestore_admin_v1/services/firestore_admin/__pycache__/__init__.cpython-310.pyc,,
google/cloud/firestore_admin_v1/services/firestore_admin/__pycache__/async_client.cpython-310.pyc,,
google/cloud/firestore_admin_v1/services/firestore_admin/__pycache__/client.cpython-310.pyc,,
google/cloud/firestore_admin_v1/services/firestore_admin/__pycache__/pagers.cpython-310.pyc,,
google/cloud/firestore_admin_v1/services/firestore_admin/async_client.py,sha256=n0Ytsz3mqZyMYjxl_lvkFhxeWZyUAgS1kYTHVTesxDc,85344
google/cloud/firestore_admin_v1/services/firestore_admin/client.py,sha256=P0GqHhNEGFoWVLH0aeZ8mAL0nIh25xMW9kZC_lcr_S4,95879
google/cloud/firestore_admin_v1/services/firestore_admin/pagers.py,sha256=7Tj4UhWMLdoJH01ngrzcsvo6A-Tu9V6GVKFan9qydgg,10866
google/cloud/firestore_admin_v1/services/firestore_admin/transports/__init__.py,sha256=YilajM9zZrwwf_FeU6lVxqsLariQiSuc7HmsFJvHxxA,1418
google/cloud/firestore_admin_v1/services/firestore_admin/transports/__pycache__/__init__.cpython-310.pyc,,
google/cloud/firestore_admin_v1/services/firestore_admin/transports/__pycache__/base.cpython-310.pyc,,
google/cloud/firestore_admin_v1/services/firestore_admin/transports/__pycache__/grpc.cpython-310.pyc,,
google/cloud/firestore_admin_v1/services/firestore_admin/transports/__pycache__/grpc_asyncio.cpython-310.pyc,,
google/cloud/firestore_admin_v1/services/firestore_admin/transports/__pycache__/rest.cpython-310.pyc,,
google/cloud/firestore_admin_v1/services/firestore_admin/transports/base.py,sha256=mC6NoWpbJysMP8Ab5OOKZjyxMBKfK_D8b-sajJ6jQsg,15069
google/cloud/firestore_admin_v1/services/firestore_admin/transports/grpc.py,sha256=5ceI5YmDhxsMDahIs3NPVKRaEizRkcWVfc5erzmJt7I,33129
google/cloud/firestore_admin_v1/services/firestore_admin/transports/grpc_asyncio.py,sha256=QFCMjH8rnAPajtlBF6JD9hWRZtDid4YD736FOjOki10,33784
google/cloud/firestore_admin_v1/services/firestore_admin/transports/rest.py,sha256=EuAzQFLSUvZGH_ouvASgrdr2IL8DhYVodTyfcMgrbGc,91059
google/cloud/firestore_admin_v1/types/__init__.py,sha256=aG4uUc4QAr153SeodaoNjiGkncwa3LzxNQErjvg3N_Q,2207
google/cloud/firestore_admin_v1/types/__pycache__/__init__.cpython-310.pyc,,
google/cloud/firestore_admin_v1/types/__pycache__/database.cpython-310.pyc,,
google/cloud/firestore_admin_v1/types/__pycache__/field.cpython-310.pyc,,
google/cloud/firestore_admin_v1/types/__pycache__/firestore_admin.cpython-310.pyc,,
google/cloud/firestore_admin_v1/types/__pycache__/index.cpython-310.pyc,,
google/cloud/firestore_admin_v1/types/__pycache__/location.cpython-310.pyc,,
google/cloud/firestore_admin_v1/types/__pycache__/operation.cpython-310.pyc,,
google/cloud/firestore_admin_v1/types/database.py,sha256=ZjOUUhlI02EGdhqJd_15yoMlJCwFwu5Smo77aCA0WWA,6063
google/cloud/firestore_admin_v1/types/field.py,sha256=3UwB7GvLxs--nB4jSv6LCtGJbiIEFuloIuV5nihdZWw,7553
google/cloud/firestore_admin_v1/types/firestore_admin.py,sha256=wxZukg9A7P5d8ZPWq9U2zSAilKevVoyXkjE5AXZ-7fM,13924
google/cloud/firestore_admin_v1/types/index.py,sha256=fERRjE5alHbHX6J8UD9IVRMDhDZDHFvApqFOdkCz0d8,9692
google/cloud/firestore_admin_v1/types/location.py,sha256=EADOxxwqpbVWmUq8JbqbrQwmhjytz8Sxco756O1oE6g,1061
google/cloud/firestore_admin_v1/types/operation.py,sha256=GWoBWlzUn2yITR5pCR8Ir7ucGrHFmYQgr8fRlMD045w,14189
google/cloud/firestore_bundle/__init__.py,sha256=7mza_CpDC-bac2R68ALozV1WwnbnHX1eoX5oHKdlYcg,1114
google/cloud/firestore_bundle/__pycache__/__init__.cpython-310.pyc,,
google/cloud/firestore_bundle/__pycache__/_helpers.cpython-310.pyc,,
google/cloud/firestore_bundle/__pycache__/bundle.cpython-310.pyc,,
google/cloud/firestore_bundle/__pycache__/gapic_version.cpython-310.pyc,,
google/cloud/firestore_bundle/_helpers.py,sha256=rs3YY_LgD6RRKR3ay2p9gM_1uwBk6hxEY032f_hSNXg,361
google/cloud/firestore_bundle/bundle.py,sha256=jkEFHhwzyuTghMBdda8X_uNZaK_1kiS8ddR0U5feLSk,13930
google/cloud/firestore_bundle/gapic_metadata.json,sha256=WP2fXupExiCP8Mz5jm4QoaOURqGtnA6tv5CGx_cq6A8,231
google/cloud/firestore_bundle/gapic_version.py,sha256=rVhuW1EKDdRIH8NAUU1H8QG5MC9ywcw6EDAcuTEQQjs,653
google/cloud/firestore_bundle/py.typed,sha256=22rn8vq8SChWrPBibDFfX0tiViQW0mXXsTsih71_Ye8,80
google/cloud/firestore_bundle/services/__init__.py,sha256=zWbvQwgy48VXnYBlD4x4jlBof70BFYtIC9fehwEW1WQ,600
google/cloud/firestore_bundle/services/__pycache__/__init__.cpython-310.pyc,,
google/cloud/firestore_bundle/types/__init__.py,sha256=otM_uBZPmrcCq8KIb5EuMrO0dtoUUUJr0VcsVOjU3-g,853
google/cloud/firestore_bundle/types/__pycache__/__init__.cpython-310.pyc,,
google/cloud/firestore_bundle/types/__pycache__/bundle.cpython-310.pyc,,
google/cloud/firestore_bundle/types/bundle.py,sha256=WWKqn9gaKcS4X1DV23Ux3o6HwZn3LvhPAbXr97NW-6I,7500
google/cloud/firestore_v1/__init__.py,sha256=hFNDp0-iGGYezZnDTn3o2Xo9z-qPXCZzPMp5ylBM8SI,5909
google/cloud/firestore_v1/__pycache__/__init__.cpython-310.pyc,,
google/cloud/firestore_v1/__pycache__/_helpers.cpython-310.pyc,,
google/cloud/firestore_v1/__pycache__/aggregation.cpython-310.pyc,,
google/cloud/firestore_v1/__pycache__/async_aggregation.cpython-310.pyc,,
google/cloud/firestore_v1/__pycache__/async_batch.cpython-310.pyc,,
google/cloud/firestore_v1/__pycache__/async_client.cpython-310.pyc,,
google/cloud/firestore_v1/__pycache__/async_collection.cpython-310.pyc,,
google/cloud/firestore_v1/__pycache__/async_document.cpython-310.pyc,,
google/cloud/firestore_v1/__pycache__/async_query.cpython-310.pyc,,
google/cloud/firestore_v1/__pycache__/async_transaction.cpython-310.pyc,,
google/cloud/firestore_v1/__pycache__/base_aggregation.cpython-310.pyc,,
google/cloud/firestore_v1/__pycache__/base_batch.cpython-310.pyc,,
google/cloud/firestore_v1/__pycache__/base_client.cpython-310.pyc,,
google/cloud/firestore_v1/__pycache__/base_collection.cpython-310.pyc,,
google/cloud/firestore_v1/__pycache__/base_document.cpython-310.pyc,,
google/cloud/firestore_v1/__pycache__/base_query.cpython-310.pyc,,
google/cloud/firestore_v1/__pycache__/base_transaction.cpython-310.pyc,,
google/cloud/firestore_v1/__pycache__/batch.cpython-310.pyc,,
google/cloud/firestore_v1/__pycache__/bulk_batch.cpython-310.pyc,,
google/cloud/firestore_v1/__pycache__/bulk_writer.cpython-310.pyc,,
google/cloud/firestore_v1/__pycache__/client.cpython-310.pyc,,
google/cloud/firestore_v1/__pycache__/collection.cpython-310.pyc,,
google/cloud/firestore_v1/__pycache__/document.cpython-310.pyc,,
google/cloud/firestore_v1/__pycache__/field_path.cpython-310.pyc,,
google/cloud/firestore_v1/__pycache__/gapic_version.cpython-310.pyc,,
google/cloud/firestore_v1/__pycache__/order.cpython-310.pyc,,
google/cloud/firestore_v1/__pycache__/query.cpython-310.pyc,,
google/cloud/firestore_v1/__pycache__/rate_limiter.cpython-310.pyc,,
google/cloud/firestore_v1/__pycache__/transaction.cpython-310.pyc,,
google/cloud/firestore_v1/__pycache__/transforms.cpython-310.pyc,,
google/cloud/firestore_v1/__pycache__/watch.cpython-310.pyc,,
google/cloud/firestore_v1/_helpers.py,sha256=VTtMltiRCC2kU3yeUBJHKkZYhwyawVvlPNbdkfsg6gk,45865
google/cloud/firestore_v1/aggregation.py,sha256=rCxzQiplucZwdABueQBqUboGD2jFzoyY8MD4yKJRyYQ,5820
google/cloud/firestore_v1/async_aggregation.py,sha256=MYsb0cgSr0CIYUfIrTS5MNwBRoXl1zr0pT5qD4F4QIQ,4702
google/cloud/firestore_v1/async_batch.py,sha256=vp0MKdk2yPA4MRJ63W_Mg9BnUCgtH2ar8PC7pEv57_U,2811
google/cloud/firestore_v1/async_client.py,sha256=rFvtXTxDV7glJlgkpUImEXM9o3W4IPuvUduOn5rxJYs,15485
google/cloud/firestore_v1/async_collection.py,sha256=izUMzoCDdGOnL0C0NWuK1W5wThZi_RWHCNd1ZKnHL2M,9966
google/cloud/firestore_v1/async_document.py,sha256=84UBBuBgLClEbQdLvWqgkh2gNZLxbOFsNWpT2SlPmp0,16155
google/cloud/firestore_v1/async_query.py,sha256=HAFDXkPXk0lnMECewNWUMWu1Pv0bO8waS781l4Z-LBA,14433
google/cloud/firestore_v1/async_transaction.py,sha256=JHlhqJnRkSm0_oN9GBU1XDgi8LjfobzExMWxKKr_-1w,15305
google/cloud/firestore_v1/base_aggregation.py,sha256=6Jfz6G7wQ3L6mizBbTriYmA3XMrIOHrQ1HBmlFOziOw,7700
google/cloud/firestore_v1/base_batch.py,sha256=KlBVKngVEU5-Dy0KbP6F8P4CabVRNGqDEPrnhx_Fdwg,7216
google/cloud/firestore_v1/base_client.py,sha256=gxCvrDqsKz5BQ-94eucwxIyrnvVDfXGwS97b3uQcNMU,22395
google/cloud/firestore_v1/base_collection.py,sha256=il1TybQZqTItv2P9ucuhziSE-kNfV58a6EwvbLM3e8M,18868
google/cloud/firestore_v1/base_document.py,sha256=7b4zzO8cUSAHF2i11nSsHvaT-V54fq8boGV3c3mAiTw,18496
google/cloud/firestore_v1/base_query.py,sha256=XbVtPna72iRWeAbNuu9gCmWUeUOpzLye-cDAaE2Ae6I,55489
google/cloud/firestore_v1/base_transaction.py,sha256=gjuK9QaCjj15ckgu1b1RWelwdUVSW0muRRBRw0xkihU,6557
google/cloud/firestore_v1/batch.py,sha256=u0I3oRWf3Cy09TFzTcGNYFKa1th4dt5deCD1v1WN1TE,2842
google/cloud/firestore_v1/bulk_batch.py,sha256=6ZSpctvtSLA3n-YnmnEWS6uvxg95XSfLJVAZO6dhElc,3870
google/cloud/firestore_v1/bulk_writer.py,sha256=OYCI2TxHgAK43LH7lnGYyk2s1_nrUUVTfrmwJEgCz94,34754
google/cloud/firestore_v1/client.py,sha256=p0iezqdxcsIJRqxtZ4-4J4LihGPKhrAXw-PPF0nB8Vo,14655
google/cloud/firestore_v1/collection.py,sha256=1KNhnHzufKtoKMTdc-pSrgM3kdf74nGQdvt88PZX4vw,10279
google/cloud/firestore_v1/document.py,sha256=K6tgFf-_YvTmIUPIb-Qs-B7Nz86LfLg0xxG-OGkBTis,19011
google/cloud/firestore_v1/field_path.py,sha256=EPlGSFdpYhnEYuTNjgsfNVJkg4l8TblRjJv3E5ctRak,12339
google/cloud/firestore_v1/gapic_metadata.json,sha256=9gKxXCVZOEXa_n32e0FyRurohH5hmCj8bEEea1O754o,6391
google/cloud/firestore_v1/gapic_version.py,sha256=rVhuW1EKDdRIH8NAUU1H8QG5MC9ywcw6EDAcuTEQQjs,653
google/cloud/firestore_v1/order.py,sha256=xZxijwPzlwxFnp7gKv1ujd5tPQ1Jd28QtHv1EhfxfaI,7026
google/cloud/firestore_v1/py.typed,sha256=9t7_u2uES6wHzMi1u2-nkdhxS1pAhtHKqubDWkRnMsQ,83
google/cloud/firestore_v1/query.py,sha256=EormuZDnqTU35igSw38dZms4efI94FRj08WEgNE7Dwo,16443
google/cloud/firestore_v1/rate_limiter.py,sha256=cQm9IAQGPHGmeGWMH5nCQ94uZuEEoR9KqiV6PTLmghQ,6983
google/cloud/firestore_v1/services/__init__.py,sha256=zWbvQwgy48VXnYBlD4x4jlBof70BFYtIC9fehwEW1WQ,600
google/cloud/firestore_v1/services/__pycache__/__init__.cpython-310.pyc,,
google/cloud/firestore_v1/services/firestore/__init__.py,sha256=ftj_Q3J0YGoW8BWEKqFVkMOex3oqNktgbejts9wM4-s,749
google/cloud/firestore_v1/services/firestore/__pycache__/__init__.cpython-310.pyc,,
google/cloud/firestore_v1/services/firestore/__pycache__/async_client.cpython-310.pyc,,
google/cloud/firestore_v1/services/firestore/__pycache__/client.cpython-310.pyc,,
google/cloud/firestore_v1/services/firestore/__pycache__/pagers.cpython-310.pyc,,
google/cloud/firestore_v1/services/firestore/async_client.py,sha256=2ObfLd53dc5p16OcLWi0d_QEpspNtZ89RuJFgH04itQ,86813
google/cloud/firestore_v1/services/firestore/client.py,sha256=9Mwba19a0mxClo795SmDOFAL17Vk9nrRZCYCGEUmFfo,90260
google/cloud/firestore_v1/services/firestore/pagers.py,sha256=gsXzP02WfP_j7oFYylQ93_ZouMvcfS4wBoEMilQAqaI,15879
google/cloud/firestore_v1/services/firestore/transports/__init__.py,sha256=OAyUUXUT0Zx9qg8awSB0_zEl0AdlFpyGyZaJge1lVcs,1348
google/cloud/firestore_v1/services/firestore/transports/__pycache__/__init__.cpython-310.pyc,,
google/cloud/firestore_v1/services/firestore/transports/__pycache__/base.cpython-310.pyc,,
google/cloud/firestore_v1/services/firestore/transports/__pycache__/grpc.cpython-310.pyc,,
google/cloud/firestore_v1/services/firestore/transports/__pycache__/grpc_asyncio.cpython-310.pyc,,
google/cloud/firestore_v1/services/firestore/transports/__pycache__/rest.cpython-310.pyc,,
google/cloud/firestore_v1/services/firestore/transports/base.py,sha256=RmL_WG05zKCJAOPniR_B4yNUlwgxnCS5dOJxJV1MVTg,21188
google/cloud/firestore_v1/services/firestore/transports/grpc.py,sha256=wF-hEJwxw3IUwINlsaSQXf8L3_x4F_04LzQTeIbbnaM,33315
google/cloud/firestore_v1/services/firestore/transports/grpc_asyncio.py,sha256=GeYtPZ_EuOrKBHe4Y92h6dhD38eeSkqXk1KWqPJaYX0,33983
google/cloud/firestore_v1/services/firestore/transports/rest.py,sha256=v4aDeLo_541v0sFIQFSbNI-RDcS9CU56DF9Pf_5h8PE,95517
google/cloud/firestore_v1/transaction.py,sha256=DmXdFFidWFjBikRFaq0KLOkNq_2RQ8OL9boiEso82-8,14783
google/cloud/firestore_v1/transforms.py,sha256=rirIGlNeKHV-iWLyNC6t6ufJt2W4RbB1OMdA1qeIB-8,4685
google/cloud/firestore_v1/types/__init__.py,sha256=Krw_olMUGCOYJPlbnfYwNnkM_zk98_cbotBdnT0D5fg,3070
google/cloud/firestore_v1/types/__pycache__/__init__.cpython-310.pyc,,
google/cloud/firestore_v1/types/__pycache__/aggregation_result.cpython-310.pyc,,
google/cloud/firestore_v1/types/__pycache__/bloom_filter.cpython-310.pyc,,
google/cloud/firestore_v1/types/__pycache__/common.cpython-310.pyc,,
google/cloud/firestore_v1/types/__pycache__/document.cpython-310.pyc,,
google/cloud/firestore_v1/types/__pycache__/firestore.cpython-310.pyc,,
google/cloud/firestore_v1/types/__pycache__/query.cpython-310.pyc,,
google/cloud/firestore_v1/types/__pycache__/write.cpython-310.pyc,,
google/cloud/firestore_v1/types/aggregation_result.py,sha256=myWHNrnskOLr2Hj6UwvN9MFO9gRQnqD4CH4Tusbmjv0,1901
google/cloud/firestore_v1/types/bloom_filter.py,sha256=N-_iG74nW-QmlQXSXITX-DkZeLrLpZJ8OVBU-3AfRMI,3480
google/cloud/firestore_v1/types/common.py,sha256=j7I3jmgX0aKScgc-kAGil5tq0zJA0bH69r0GnIITb_M,5529
google/cloud/firestore_v1/types/document.py,sha256=XfZ7IETV2sCnEw3oflzzSmsFgHHAOJT2eaBLwfid1Lo,9248
google/cloud/firestore_v1/types/firestore.py,sha256=nA0fj1K-lUBMJqTOqHfjNfRcTYVVtqJ6gnwJLmG_y-U,58581
google/cloud/firestore_v1/types/query.py,sha256=VBvfsqLKUtfb-a40VlDJX7nfB6C72bapAQGR4AESaWI,27686
google/cloud/firestore_v1/types/write.py,sha256=ublxV6TKGIpY2f0Y8uTj9fsuyRziG8ybBFC32vmys6c,19729
google/cloud/firestore_v1/watch.py,sha256=IBvUecyESKiRShLL8DnceRJLemr31GzFX8qdrxev39I,25445
google_cloud_firestore-2.12.0-py3.9-nspkg.pth,sha256=b0D5dZk3RUzK54tZ9iZDvLm7u8ltc5EzYrGCmhsuoNw,1698
google_cloud_firestore-2.12.0.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
google_cloud_firestore-2.12.0.dist-info/LICENSE,sha256=z8d0m5b2O9McPEK1xHG_dWgUBT6EfBDz6wA0F7xSPTA,11358
google_cloud_firestore-2.12.0.dist-info/METADATA,sha256=tl1UkoAG8dN0oy2lYhvHs8Oq9KV01bXzVF1mo9vmuLg,5556
google_cloud_firestore-2.12.0.dist-info/RECORD,,
google_cloud_firestore-2.12.0.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
google_cloud_firestore-2.12.0.dist-info/WHEEL,sha256=bb2Ot9scclHKMOLDEHY6B2sicWOgugjFKaJsT7vwMQo,110
google_cloud_firestore-2.12.0.dist-info/namespace_packages.txt,sha256=v8IaYqRE2a0onAGJIpZeFkkH83wXSWZRR9eOyfMwoTc,20
google_cloud_firestore-2.12.0.dist-info/top_level.txt,sha256=_1QvSJIhFAGfxb79D6DhB7SUw2X6T4rwnz_LLrbcD3c,7
