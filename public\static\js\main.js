// SABTRANS Main JavaScript

document.addEventListener('DOMContentLoaded', function() {
    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    // Initialize popovers
    var popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
    var popoverList = popoverTriggerList.map(function (popoverTriggerEl) {
        return new bootstrap.Popover(popoverTriggerEl);
    });

    // Auto-hide alerts after 5 seconds
    setTimeout(function() {
        var alerts = document.querySelectorAll('.alert');
        alerts.forEach(function(alert) {
            var bsAlert = new bootstrap.Alert(alert);
            bsAlert.close();
        });
    }, 5000);

    // Form validation
    var forms = document.querySelectorAll('.needs-validation');
    Array.prototype.slice.call(forms).forEach(function(form) {
        form.addEventListener('submit', function(event) {
            if (!form.checkValidity()) {
                event.preventDefault();
                event.stopPropagation();
            }
            form.classList.add('was-validated');
        }, false);
    });

    // Password strength indicator
    var passwordInput = document.getElementById('password');
    if (passwordInput) {
        passwordInput.addEventListener('input', function() {
            checkPasswordStrength(this.value);
        });
    }

    // Confirm password validation
    var confirmPasswordInput = document.getElementById('confirm_password');
    if (confirmPasswordInput) {
        confirmPasswordInput.addEventListener('input', function() {
            validatePasswordMatch();
        });
    }

    // File upload preview
    var fileInputs = document.querySelectorAll('input[type="file"]');
    fileInputs.forEach(function(input) {
        input.addEventListener('change', function() {
            previewFile(this);
        });
    });

    // Search functionality
    var searchInput = document.getElementById('search');
    if (searchInput) {
        searchInput.addEventListener('input', debounce(function() {
            performSearch(this.value);
        }, 300));
    }

    // Geolocation for freight offers
    var locationBtn = document.getElementById('get-location');
    if (locationBtn) {
        locationBtn.addEventListener('click', getCurrentLocation);
    }

    // Real-time updates for dashboard
    if (document.querySelector('.dashboard')) {
        setInterval(updateDashboardStats, 30000); // Update every 30 seconds
    }
});

// Password strength checker
function checkPasswordStrength(password) {
    var strengthIndicator = document.getElementById('password-strength');
    if (!strengthIndicator) return;

    var strength = 0;
    var feedback = [];

    // Length check
    if (password.length >= 8) {
        strength += 1;
    } else {
        feedback.push('Au moins 8 caractères');
    }

    // Uppercase check
    if (/[A-Z]/.test(password)) {
        strength += 1;
    } else {
        feedback.push('Une majuscule');
    }

    // Lowercase check
    if (/[a-z]/.test(password)) {
        strength += 1;
    } else {
        feedback.push('Une minuscule');
    }

    // Number check
    if (/\d/.test(password)) {
        strength += 1;
    } else {
        feedback.push('Un chiffre');
    }

    // Special character check
    if (/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
        strength += 1;
    }

    // Update indicator
    var strengthText = '';
    var strengthClass = '';

    switch (strength) {
        case 0:
        case 1:
            strengthText = 'Très faible';
            strengthClass = 'text-danger';
            break;
        case 2:
            strengthText = 'Faible';
            strengthClass = 'text-warning';
            break;
        case 3:
            strengthText = 'Moyen';
            strengthClass = 'text-info';
            break;
        case 4:
        case 5:
            strengthText = 'Fort';
            strengthClass = 'text-success';
            break;
    }

    strengthIndicator.textContent = strengthText;
    strengthIndicator.className = 'form-text ' + strengthClass;

    if (feedback.length > 0) {
        strengthIndicator.textContent += ' (Manque: ' + feedback.join(', ') + ')';
    }
}

// Password match validation
function validatePasswordMatch() {
    var password = document.getElementById('password').value;
    var confirmPassword = document.getElementById('confirm_password').value;
    var feedback = document.getElementById('password-match-feedback');

    if (!feedback) return;

    if (password !== confirmPassword) {
        feedback.textContent = 'Les mots de passe ne correspondent pas';
        feedback.className = 'form-text text-danger';
    } else {
        feedback.textContent = 'Les mots de passe correspondent';
        feedback.className = 'form-text text-success';
    }
}

// File preview
function previewFile(input) {
    var file = input.files[0];
    var preview = document.getElementById('file-preview');
    
    if (!preview) return;

    if (file) {
        var reader = new FileReader();
        
        reader.onload = function(e) {
            if (file.type.startsWith('image/')) {
                preview.innerHTML = '<img src="' + e.target.result + '" class="img-thumbnail" style="max-width: 200px;">';
            } else {
                preview.innerHTML = '<div class="alert alert-info"><i class="fas fa-file me-2"></i>' + file.name + '</div>';
            }
        };
        
        reader.readAsDataURL(file);
    } else {
        preview.innerHTML = '';
    }
}

// Debounce function for search
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// Search functionality
function performSearch(query) {
    // This would typically make an AJAX request to search
    console.log('Searching for:', query);
}

// Geolocation
function getCurrentLocation() {
    if (navigator.geolocation) {
        navigator.geolocation.getCurrentPosition(
            function(position) {
                var lat = position.coords.latitude;
                var lng = position.coords.longitude;
                
                // Update form fields if they exist
                var latInput = document.getElementById('latitude');
                var lngInput = document.getElementById('longitude');
                
                if (latInput) latInput.value = lat;
                if (lngInput) lngInput.value = lng;
                
                // Reverse geocoding to get address
                reverseGeocode(lat, lng);
            },
            function(error) {
                console.error('Geolocation error:', error);
                alert('Impossible d\'obtenir votre position');
            }
        );
    } else {
        alert('La géolocalisation n\'est pas supportée par ce navigateur');
    }
}

// Reverse geocoding (simplified - in production, use a real API)
function reverseGeocode(lat, lng) {
    // This would typically call a geocoding API
    console.log('Reverse geocoding:', lat, lng);
}

// Dashboard stats update
function updateDashboardStats() {
    fetch('/dashboard/stats/api')
        .then(response => response.json())
        .then(data => {
            // Update dashboard elements with new data
            console.log('Dashboard stats updated:', data);
        })
        .catch(error => {
            console.error('Error updating dashboard stats:', error);
        });
}

// Utility functions
function formatCurrency(amount, currency = 'EUR') {
    return new Intl.NumberFormat('fr-FR', {
        style: 'currency',
        currency: currency
    }).format(amount);
}

function formatDate(dateString) {
    return new Intl.DateTimeFormat('fr-FR', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
    }).format(new Date(dateString));
}

function formatDateTime(dateString) {
    return new Intl.DateTimeFormat('fr-FR', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
    }).format(new Date(dateString));
}

// AJAX helper
function makeRequest(url, method = 'GET', data = null) {
    return fetch(url, {
        method: method,
        headers: {
            'Content-Type': 'application/json',
            'X-Requested-With': 'XMLHttpRequest'
        },
        body: data ? JSON.stringify(data) : null
    });
}

// Show loading spinner
function showLoading(element) {
    element.innerHTML = '<div class="text-center"><div class="spinner-border" role="status"><span class="visually-hidden">Chargement...</span></div></div>';
}

// Hide loading spinner
function hideLoading(element, originalContent) {
    element.innerHTML = originalContent;
}

// Notification system
function showNotification(message, type = 'info') {
    var notification = document.createElement('div');
    notification.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    notification.style.top = '20px';
    notification.style.right = '20px';
    notification.style.zIndex = '9999';
    notification.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    document.body.appendChild(notification);
    
    // Auto remove after 5 seconds
    setTimeout(() => {
        if (notification.parentNode) {
            notification.parentNode.removeChild(notification);
        }
    }, 5000);
}
