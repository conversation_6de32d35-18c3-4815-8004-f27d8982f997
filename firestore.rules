rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Règles pour les utilisateurs
    match /users/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
      allow read: if request.auth != null && 
        (resource.data.user_type == 'admin' || 
         get(/databases/$(database)/documents/users/$(request.auth.uid)).data.user_type == 'admin');
    }
    
    // Règles pour les offres de fret
    match /freight_offers/{offerId} {
      allow read: if request.auth != null;
      allow create: if request.auth != null && 
        request.auth.uid == resource.data.user_id;
      allow update, delete: if request.auth != null && 
        (request.auth.uid == resource.data.user_id || 
         get(/databases/$(database)/documents/users/$(request.auth.uid)).data.user_type == 'admin');
    }
    
    // Règles pour les propositions de fret
    match /freight_proposals/{proposalId} {
      allow read: if request.auth != null && 
        (request.auth.uid == resource.data.transporter_id || 
         request.auth.uid == get(/databases/$(database)/documents/freight_offers/$(resource.data.offer_id)).data.user_id ||
         get(/databases/$(database)/documents/users/$(request.auth.uid)).data.user_type == 'admin');
      allow create: if request.auth != null && 
        request.auth.uid == resource.data.transporter_id;
      allow update: if request.auth != null && 
        (request.auth.uid == resource.data.transporter_id || 
         request.auth.uid == get(/databases/$(database)/documents/freight_offers/$(resource.data.offer_id)).data.user_id);
    }
    
    // Règles pour les pays (lecture seule)
    match /countries/{countryId} {
      allow read: if true;
      allow write: if request.auth != null && 
        get(/databases/$(database)/documents/users/$(request.auth.uid)).data.user_type == 'admin';
    }
    
    // Règles pour les départements (lecture seule)
    match /departments/{departmentId} {
      allow read: if true;
      allow write: if request.auth != null && 
        get(/databases/$(database)/documents/users/$(request.auth.uid)).data.user_type == 'admin';
    }
    
    // Règles pour les devises (lecture seule)
    match /currencies/{currencyId} {
      allow read: if true;
      allow write: if request.auth != null && 
        get(/databases/$(database)/documents/users/$(request.auth.uid)).data.user_type == 'admin';
    }
    
    // Règles pour les missions
    match /missions/{missionId} {
      allow read, write: if request.auth != null && 
        (request.auth.uid == resource.data.transporter_id || 
         request.auth.uid == resource.data.shipper_id ||
         get(/databases/$(database)/documents/users/$(request.auth.uid)).data.user_type == 'admin');
    }
    
    // Règles pour les documents
    match /documents/{documentId} {
      allow read, write: if request.auth != null && 
        request.auth.uid == resource.data.user_id;
      allow read: if request.auth != null && 
        get(/databases/$(database)/documents/users/$(request.auth.uid)).data.user_type == 'admin';
    }
  }
}
