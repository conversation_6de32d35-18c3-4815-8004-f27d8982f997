<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SABTRANS - Plateforme de Transport et Logistique</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <!-- Firebase SDK -->
    <script src="https://www.gstatic.com/firebasejs/10.5.0/firebase-app-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/10.5.0/firebase-auth-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/10.5.0/firebase-firestore-compat.js"></script>
    
    <style>
        .hero-section {
            background: linear-gradient(135deg, #003366 0%, #0066cc 100%);
            color: white;
            padding: 100px 0;
        }
        
        .feature-card {
            transition: transform 0.3s ease;
            border: none;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        
        .feature-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 15px rgba(0, 0, 0, 0.2);
        }
        
        .btn-primary {
            background-color: #003366;
            border-color: #003366;
        }
        
        .btn-primary:hover {
            background-color: #0066cc;
            border-color: #0066cc;
        }
        
        .navbar-brand {
            font-weight: bold;
            font-size: 1.5rem;
        }
        
        .loading {
            display: none;
        }
        
        .loading.show {
            display: block;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark" style="background-color: #003366;">
        <div class="container">
            <a class="navbar-brand" href="/">
                <i class="fas fa-truck me-2"></i>SABTRANS
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="#features">Fonctionnalités</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#about">À propos</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#contact">Contact</a>
                    </li>
                    <li class="nav-item" id="auth-buttons">
                        <a class="btn btn-outline-light me-2" href="/auth/login">Connexion</a>
                        <a class="btn btn-light" href="/auth/register">Inscription</a>
                    </li>
                    <li class="nav-item d-none" id="user-menu">
                        <div class="dropdown">
                            <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                                <i class="fas fa-user me-1"></i><span id="user-name"></span>
                            </a>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="/dashboard/"><i class="fas fa-tachometer-alt me-2"></i>Dashboard</a></li>
                                <li><a class="dropdown-item" href="/auth/profile"><i class="fas fa-user me-2"></i>Profil</a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="#" onclick="logout()"><i class="fas fa-sign-out-alt me-2"></i>Déconnexion</a></li>
                            </ul>
                        </div>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Section Hero -->
    <section class="hero-section text-center">
        <div class="container">
            <div class="row">
                <div class="col-lg-8 mx-auto">
                    <h1 class="display-4 fw-bold mb-4">
                        Plateforme de Transport et Logistique
                    </h1>
                    <p class="lead mb-5">
                        Connectez expéditeurs et transporteurs pour optimiser vos opérations logistiques. 
                        Solution complète avec gestion multi-devises et localisation internationale.
                    </p>
                    <div class="d-grid gap-2 d-md-flex justify-content-md-center">
                        <a href="/auth/register" class="btn btn-light btn-lg me-md-2">
                            <i class="fas fa-user-plus me-2"></i>Commencer Gratuitement
                        </a>
                        <a href="#features" class="btn btn-outline-light btn-lg">
                            <i class="fas fa-info-circle me-2"></i>En savoir plus
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Section Fonctionnalités -->
    <section id="features" class="py-5">
        <div class="container">
            <div class="row">
                <div class="col-lg-12 text-center mb-5">
                    <h2 class="display-5 fw-bold">Fonctionnalités Principales</h2>
                    <p class="lead text-muted">Une solution complète pour tous vos besoins logistiques</p>
                </div>
            </div>
            <div class="row g-4">
                <div class="col-lg-4 col-md-6">
                    <div class="card feature-card h-100 text-center p-4">
                        <div class="card-body">
                            <i class="fas fa-shipping-fast fa-3x text-primary mb-3"></i>
                            <h5 class="card-title">Gestion des Offres de Fret</h5>
                            <p class="card-text">Créez et gérez vos offres de transport avec un système complet de localisation et de devises.</p>
                        </div>
                    </div>
                </div>
                <div class="col-lg-4 col-md-6">
                    <div class="card feature-card h-100 text-center p-4">
                        <div class="card-body">
                            <i class="fas fa-handshake fa-3x text-success mb-3"></i>
                            <h5 class="card-title">Système de Propositions</h5>
                            <p class="card-text">Les transporteurs peuvent proposer leurs prix et services pour vos expéditions.</p>
                        </div>
                    </div>
                </div>
                <div class="col-lg-4 col-md-6">
                    <div class="card feature-card h-100 text-center p-4">
                        <div class="card-body">
                            <i class="fas fa-globe fa-3x text-info mb-3"></i>
                            <h5 class="card-title">Multi-Pays & Devises</h5>
                            <p class="card-text">Support de 10 pays avec leurs subdivisions et 8 devises internationales.</p>
                        </div>
                    </div>
                </div>
                <div class="col-lg-4 col-md-6">
                    <div class="card feature-card h-100 text-center p-4">
                        <div class="card-body">
                            <i class="fas fa-chart-line fa-3x text-warning mb-3"></i>
                            <h5 class="card-title">Tableaux de Bord</h5>
                            <p class="card-text">Suivez vos performances avec des tableaux de bord détaillés et des statistiques.</p>
                        </div>
                    </div>
                </div>
                <div class="col-lg-4 col-md-6">
                    <div class="card feature-card h-100 text-center p-4">
                        <div class="card-body">
                            <i class="fas fa-shield-alt fa-3x text-danger mb-3"></i>
                            <h5 class="card-title">Sécurité Avancée</h5>
                            <p class="card-text">Protection des données avec authentification sécurisée et validation multi-niveaux.</p>
                        </div>
                    </div>
                </div>
                <div class="col-lg-4 col-md-6">
                    <div class="card feature-card h-100 text-center p-4">
                        <div class="card-body">
                            <i class="fas fa-mobile-alt fa-3x text-purple mb-3"></i>
                            <h5 class="card-title">Interface Responsive</h5>
                            <p class="card-text">Accédez à la plateforme depuis n'importe quel appareil avec une interface adaptative.</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Section À propos -->
    <section id="about" class="py-5 bg-light">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-6">
                    <h2 class="display-5 fw-bold mb-4">À propos de SABTRANS</h2>
                    <p class="lead mb-4">
                        SABTRANS est une plateforme innovante qui révolutionne le secteur du transport et de la logistique 
                        en connectant efficacement les expéditeurs et les transporteurs.
                    </p>
                    <ul class="list-unstyled">
                        <li class="mb-2"><i class="fas fa-check text-success me-2"></i>Interface intuitive et moderne</li>
                        <li class="mb-2"><i class="fas fa-check text-success me-2"></i>Support multi-langues et multi-devises</li>
                        <li class="mb-2"><i class="fas fa-check text-success me-2"></i>Sécurité et fiabilité garanties</li>
                        <li class="mb-2"><i class="fas fa-check text-success me-2"></i>Support client 24/7</li>
                    </ul>
                </div>
                <div class="col-lg-6">
                    <img src="https://via.placeholder.com/500x400/003366/FFFFFF?text=SABTRANS" 
                         alt="SABTRANS Platform" class="img-fluid rounded shadow">
                </div>
            </div>
        </div>
    </section>

    <!-- Section Contact -->
    <section id="contact" class="py-5">
        <div class="container">
            <div class="row">
                <div class="col-lg-8 mx-auto text-center">
                    <h2 class="display-5 fw-bold mb-4">Contactez-nous</h2>
                    <p class="lead mb-5">
                        Prêt à optimiser vos opérations logistiques ? Contactez notre équipe pour une démonstration.
                    </p>
                    <div class="row g-4">
                        <div class="col-md-4">
                            <i class="fas fa-envelope fa-2x text-primary mb-3"></i>
                            <h5>Email</h5>
                            <p><EMAIL></p>
                        </div>
                        <div class="col-md-4">
                            <i class="fas fa-phone fa-2x text-primary mb-3"></i>
                            <h5>Téléphone</h5>
                            <p>+33 1 23 45 67 89</p>
                        </div>
                        <div class="col-md-4">
                            <i class="fas fa-map-marker-alt fa-2x text-primary mb-3"></i>
                            <h5>Adresse</h5>
                            <p>Paris, France</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-dark text-light py-4">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <h5><i class="fas fa-truck me-2"></i>SABTRANS</h5>
                    <p>Plateforme de transport et logistique nouvelle génération.</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <p>&copy; 2024 SABTRANS. Tous droits réservés.</p>
                </div>
            </div>
        </div>
    </footer>

    <!-- Loading Spinner -->
    <div class="loading position-fixed top-50 start-50 translate-middle">
        <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">Chargement...</span>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Firebase Configuration -->
    <script>
        // Configuration Firebase (à remplacer par vos vraies clés)
        const firebaseConfig = {
            apiKey: "your-api-key",
            authDomain: "sabtrans-app.firebaseapp.com",
            projectId: "sabtrans-app",
            storageBucket: "sabtrans-app.appspot.com",
            messagingSenderId: "123456789",
            appId: "your-app-id"
        };

        // Initialiser Firebase
        firebase.initializeApp(firebaseConfig);
        const auth = firebase.auth();
        const db = firebase.firestore();

        // Vérifier l'état d'authentification
        auth.onAuthStateChanged((user) => {
            const authButtons = document.getElementById('auth-buttons');
            const userMenu = document.getElementById('user-menu');
            const userName = document.getElementById('user-name');

            if (user) {
                // Utilisateur connecté
                authButtons.classList.add('d-none');
                userMenu.classList.remove('d-none');
                userName.textContent = user.displayName || user.email;
            } else {
                // Utilisateur non connecté
                authButtons.classList.remove('d-none');
                userMenu.classList.add('d-none');
            }
        });

        // Fonction de déconnexion
        function logout() {
            auth.signOut().then(() => {
                window.location.href = '/';
            }).catch((error) => {
                console.error('Erreur lors de la déconnexion:', error);
            });
        }

        // Smooth scrolling pour les liens d'ancrage
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });
    </script>
</body>
</html>
