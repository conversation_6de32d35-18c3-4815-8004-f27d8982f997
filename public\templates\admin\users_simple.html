{% extends "base.html" %}

{% block title %}Gestion des Utilisateurs - SABTRANS{% endblock %}

{# Initialisation sécurisée des variables #}
{% set filters = filters or {} %}
{% set search_query = search_query or '' %}
{% set total_users = total_users or 0 %}
{% set active_users = active_users or 0 %}
{% set pending_users = pending_users or 0 %}
{% set new_users_week = new_users_week or 0 %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 text-primary">
                        <i class="fas fa-users me-2"></i>Gestion des Utilisateurs
                    </h1>
                    <p class="text-muted">Administration des comptes utilisateurs</p>
                </div>
                <div>
                    <a href="{{ url_for('dashboard.index') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-2"></i>Retour Dashboard
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistiques rapides -->
    <div class="row mb-4">
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card dashboard-card h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title text-muted">Total Utilisateurs</h6>
                            <h2 class="stat-number">{{ total_users }}</h2>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-users fa-2x text-primary"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card dashboard-card success h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title text-muted">Utilisateurs Actifs</h6>
                            <h2 class="stat-number text-success">{{ active_users }}</h2>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-user-check fa-2x text-success"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card dashboard-card warning h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title text-muted">En Attente</h6>
                            <h2 class="stat-number text-warning">{{ pending_users }}</h2>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-user-clock fa-2x text-warning"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card dashboard-card info h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title text-muted">Nouveaux (7j)</h6>
                            <h2 class="stat-number text-info">{{ new_users_week }}</h2>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-user-plus fa-2x text-info"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Liste des utilisateurs -->
    <div class="card">
        <div class="card-header bg-primary text-white">
            <h5 class="mb-0">
                <i class="fas fa-list me-2"></i>Liste des Utilisateurs
            </h5>
        </div>
        <div class="card-body">
            {% if users and users.items %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Utilisateur</th>
                                <th>Type</th>
                                <th>Email</th>
                                <th>Statut</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for user in users.items %}
                            <tr>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="avatar-circle me-3">
                                            {{ user.first_name[0] if user.first_name else 'U' }}{{ user.last_name[0] if user.last_name else 'U' }}
                                        </div>
                                        <div>
                                            <strong>{{ user.get_full_name() }}</strong><br>
                                            <small class="text-muted">@{{ user.username }}</small>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <span class="badge bg-{{ 'primary' if user.user_type == 'admin' else 'secondary' }}">
                                        {{ user.user_type.title() }}
                                    </span>
                                </td>
                                <td>{{ user.email }}</td>
                                <td>
                                    {% if user.is_active %}
                                        <span class="badge bg-success">Actif</span>
                                    {% else %}
                                        <span class="badge bg-danger">Inactif</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm" role="group">
                                        <a href="{{ url_for('admin.user_detail', user_id=user.id) }}" class="btn btn-outline-primary" title="Voir">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="{{ url_for('admin.edit_user', user_id=user.id) }}" class="btn btn-outline-warning" title="Modifier">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>

                <!-- Pagination simple -->
                {% if users.pages > 1 %}
                <nav aria-label="Navigation des utilisateurs" class="mt-4">
                    <ul class="pagination justify-content-center">
                        {% if users.has_prev %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('admin.users_simple', page=users.prev_num) }}">
                                    <i class="fas fa-chevron-left"></i>
                                </a>
                            </li>
                        {% endif %}
                        
                        <li class="page-item active">
                            <span class="page-link">{{ users.page }}</span>
                        </li>
                        
                        {% if users.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('admin.users_simple', page=users.next_num) }}">
                                    <i class="fas fa-chevron-right"></i>
                                </a>
                            </li>
                        {% endif %}
                    </ul>
                </nav>
                {% endif %}

            {% else %}
                <!-- Aucun utilisateur -->
                <div class="text-center py-5">
                    <i class="fas fa-users fa-3x text-muted mb-3"></i>
                    <h4 class="text-muted">Aucun utilisateur trouvé</h4>
                    <p class="text-muted">Aucun utilisateur dans la base de données.</p>
                </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.avatar-circle {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: linear-gradient(45deg, #003366, #90EE90);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: bold;
    font-size: 14px;
}

.dashboard-card {
    border-left: 4px solid;
}

.dashboard-card.success {
    border-left-color: #28a745;
}

.dashboard-card.warning {
    border-left-color: #ffc107;
}

.dashboard-card.info {
    border-left-color: #17a2b8;
}

.stat-number {
    font-size: 2rem;
    font-weight: bold;
    margin: 0;
}
</style>
{% endblock %}
