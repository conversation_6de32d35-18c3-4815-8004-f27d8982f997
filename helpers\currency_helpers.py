"""
Helpers pour la gestion des devises et le formatage des prix
"""

from models.location import Currency

def format_price(amount, currency_id=None, currency_code=None):
    """
    Formate un prix avec la devise appropriée
    
    Args:
        amount (float): Montant à formater
        currency_id (int, optional): ID de la devise
        currency_code (str, optional): Code de la devise (EUR, USD, etc.)
    
    Returns:
        str: Prix formaté avec le symbole de devise
    """
    if amount is None:
        return "Prix non défini"
    
    currency = None
    
    # Récupérer la devise par ID
    if currency_id:
        currency = Currency.query.get(currency_id)
    
    # Récupérer la devise par code
    elif currency_code:
        currency = Currency.query.filter_by(code=currency_code).first()
    
    # Devise par défaut
    if not currency:
        currency = Currency.query.filter_by(code='EUR').first()
    
    if currency:
        return f"{amount:.2f} {currency.symbol}"
    else:
        return f"{amount:.2f} €"

def format_price_with_name(amount, currency_id=None, currency_code=None):
    """
    Formate un prix avec le nom complet de la devise
    
    Args:
        amount (float): Montant à formater
        currency_id (int, optional): ID de la devise
        currency_code (str, optional): Code de la devise
    
    Returns:
        str: Prix formaté avec le nom de la devise
    """
    if amount is None:
        return "Prix non défini"
    
    currency = None
    
    if currency_id:
        currency = Currency.query.get(currency_id)
    elif currency_code:
        currency = Currency.query.filter_by(code=currency_code).first()
    
    if not currency:
        currency = Currency.query.filter_by(code='EUR').first()
    
    if currency:
        return f"{amount:.2f} {currency.symbol} ({currency.name_fr})"
    else:
        return f"{amount:.2f} € (Euro)"

def get_currency_symbol(currency_id=None, currency_code=None):
    """
    Récupère le symbole d'une devise
    
    Args:
        currency_id (int, optional): ID de la devise
        currency_code (str, optional): Code de la devise
    
    Returns:
        str: Symbole de la devise
    """
    currency = None
    
    if currency_id:
        currency = Currency.query.get(currency_id)
    elif currency_code:
        currency = Currency.query.filter_by(code=currency_code).first()
    
    return currency.symbol if currency else '€'

def get_currency_name(currency_id=None, currency_code=None, lang='fr'):
    """
    Récupère le nom d'une devise
    
    Args:
        currency_id (int, optional): ID de la devise
        currency_code (str, optional): Code de la devise
        lang (str): Langue ('fr' ou 'en')
    
    Returns:
        str: Nom de la devise
    """
    currency = None
    
    if currency_id:
        currency = Currency.query.get(currency_id)
    elif currency_code:
        currency = Currency.query.filter_by(code=currency_code).first()
    
    if currency:
        return currency.name_fr if lang == 'fr' else currency.name_en
    else:
        return 'Euro' if lang == 'fr' else 'Euro'

def convert_price_display(amount, from_currency_id, to_currency_code='EUR'):
    """
    Affiche un prix avec conversion (simulation - en production, utiliser une API de change)
    
    Args:
        amount (float): Montant à convertir
        from_currency_id (int): ID de la devise source
        to_currency_code (str): Code de la devise cible
    
    Returns:
        str: Prix formaté avec indication de conversion
    """
    if amount is None:
        return "Prix non défini"
    
    from_currency = Currency.query.get(from_currency_id) if from_currency_id else None
    to_currency = Currency.query.filter_by(code=to_currency_code).first()
    
    if not from_currency or not to_currency:
        return format_price(amount, from_currency_id)
    
    # Si même devise, pas de conversion
    if from_currency.code == to_currency.code:
        return format_price(amount, from_currency_id)
    
    # Simulation de conversion (en production, utiliser des taux de change réels)
    conversion_rates = {
        ('EUR', 'DZD'): 145.0,
        ('DZD', 'EUR'): 1/145.0,
        ('EUR', 'MAD'): 10.5,
        ('MAD', 'EUR'): 1/10.5,
        ('EUR', 'TND'): 3.3,
        ('TND', 'EUR'): 1/3.3,
        ('EUR', 'USD'): 1.1,
        ('USD', 'EUR'): 1/1.1,
        ('EUR', 'GBP'): 0.85,
        ('GBP', 'EUR'): 1/0.85,
        ('EUR', 'CHF'): 0.95,
        ('CHF', 'EUR'): 1/0.95,
        ('EUR', 'CAD'): 1.45,
        ('CAD', 'EUR'): 1/1.45,
    }
    
    rate = conversion_rates.get((from_currency.code, to_currency.code), 1.0)
    converted_amount = amount * rate
    
    original = format_price(amount, from_currency_id)
    converted = format_price(converted_amount, currency_code=to_currency_code)
    
    return f"{original} (≈ {converted})"

def get_user_preferred_currency(user):
    """
    Récupère la devise préférée d'un utilisateur
    
    Args:
        user: Objet utilisateur
    
    Returns:
        Currency: Objet devise ou None
    """
    if hasattr(user, 'preferred_currency_id') and user.preferred_currency_id:
        return Currency.query.get(user.preferred_currency_id)
    elif hasattr(user, 'get_currency'):
        return user.get_currency()
    else:
        return Currency.query.filter_by(code='EUR').first()

def format_price_for_user(amount, user, show_conversion=False):
    """
    Formate un prix selon les préférences de l'utilisateur
    
    Args:
        amount (float): Montant à formater
        user: Objet utilisateur
        show_conversion (bool): Afficher la conversion si nécessaire
    
    Returns:
        str: Prix formaté
    """
    if amount is None:
        return "Prix non défini"
    
    user_currency = get_user_preferred_currency(user)
    
    if user_currency:
        if show_conversion and user_currency.code != 'EUR':
            return convert_price_display(amount, user_currency.id, 'EUR')
        else:
            return format_price(amount, user_currency.id)
    else:
        return format_price(amount, currency_code='EUR')

# Fonctions pour les templates Jinja2
def register_currency_filters(app):
    """
    Enregistre les filtres de devise pour les templates Jinja2
    """
    
    @app.template_filter('format_price')
    def format_price_filter(amount, currency_id=None, currency_code=None):
        return format_price(amount, currency_id, currency_code)
    
    @app.template_filter('format_price_with_name')
    def format_price_with_name_filter(amount, currency_id=None, currency_code=None):
        return format_price_with_name(amount, currency_id, currency_code)
    
    @app.template_filter('currency_symbol')
    def currency_symbol_filter(currency_id=None, currency_code=None):
        return get_currency_symbol(currency_id, currency_code)
    
    @app.template_filter('currency_name')
    def currency_name_filter(currency_id=None, currency_code=None, lang='fr'):
        return get_currency_name(currency_id, currency_code, lang)
    
    @app.template_filter('format_price_for_user')
    def format_price_for_user_filter(amount, user, show_conversion=False):
        return format_price_for_user(amount, user, show_conversion)
