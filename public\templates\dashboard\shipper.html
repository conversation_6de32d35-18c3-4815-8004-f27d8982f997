{% extends "base.html" %}

{% block title %}Dashboard Expéditeur - SABTRANS{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col">
            <h1 class="h3 text-primary">
                <i class="fas fa-industry me-2"></i>Dashboard Expéditeur
            </h1>
            <p class="text-muted">Bienvenue {{ current_user.get_full_name() }}</p>
        </div>
    </div>

    <!-- Statistiques principales -->
    <div class="row mb-4">
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card dashboard-card h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title text-muted">Mes Offres</h6>
                            <h2 class="stat-number">{{ total_offers }}</h2>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-clipboard-list fa-2x text-primary"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card dashboard-card success h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title text-muted">Offres Actives</h6>
                            <h2 class="stat-number text-success">{{ active_offers }}</h2>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-bullhorn fa-2x text-success"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card dashboard-card warning h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title text-muted">Missions Actives</h6>
                            <h2 class="stat-number text-warning">{{ active_missions }}</h2>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-shipping-fast fa-2x text-warning"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card dashboard-card info h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title text-muted">Propositions Reçues</h6>
                            <h2 class="stat-number text-info">{{ pending_proposals }}</h2>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-handshake fa-2x text-info"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Section Propositions de Transport -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-warning text-dark">
                    <h5 class="mb-0">
                        <i class="fas fa-handshake me-2"></i>Propositions de Transport Reçues
                        {% if pending_proposals > 0 %}
                            <span class="badge bg-danger ms-2">{{ pending_proposals }}</span>
                        {% endif %}
                    </h5>
                </div>
                <div class="card-body">
                    {% if my_proposals %}
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Demande</th>
                                        <th>Transporteur</th>
                                        <th>Prix Proposé</th>
                                        <th>Durée Estimée</th>
                                        <th>Véhicule</th>
                                        <th>Statut</th>
                                        <th>Date</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for proposal in my_proposals %}
                                    <tr class="{{ 'table-warning' if proposal.status == 'pending' else '' }}">
                                        <td>
                                            <strong>{{ proposal.freight_offer.title }}</strong><br>
                                            <small class="text-muted">
                                                <i class="fas fa-route me-1"></i>
                                                {{ proposal.freight_offer.pickup_city }} → {{ proposal.freight_offer.delivery_city }}
                                            </small>
                                        </td>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="avatar-sm bg-primary text-white rounded-circle d-flex align-items-center justify-content-center me-2">
                                                    {{ proposal.transporter.first_name[0] }}{{ proposal.transporter.last_name[0] }}
                                                </div>
                                                <div>
                                                    <strong>{{ proposal.transporter.get_full_name() }}</strong><br>
                                                    {% if proposal.transporter.company_name %}
                                                        <small class="text-muted">{{ proposal.transporter.company_name }}</small>
                                                    {% endif %}
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <h5 class="text-success mb-0">{{ "%.0f"|format(proposal.proposed_price) }}€</h5>
                                            <small class="text-muted">{{ proposal.price_type or 'Prix fixe' }}</small>
                                        </td>
                                        <td>
                                            {% if proposal.estimated_duration %}
                                                {{ proposal.estimated_duration }}h
                                            {% else %}
                                                <span class="text-muted">Non spécifiée</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            {% if proposal.vehicle_details %}
                                                <small>{{ proposal.vehicle_details[:30] }}{% if proposal.vehicle_details|length > 30 %}...{% endif %}</small>
                                            {% else %}
                                                <span class="text-muted">Non spécifié</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            <span class="badge bg-{{ 'warning' if proposal.status == 'pending' else 'success' if proposal.status == 'accepted' else 'danger' }}">
                                                {% if proposal.status %}
                                                    {{ proposal.status.title() }}
                                                {% else %}
                                                    Statut non défini
                                                {% endif %}
                                            </span>
                                        </td>
                                        <td>
                                            <small>
                                                {% if proposal.created_at %}
                                                    {{ proposal.created_at.strftime('%d/%m/%Y %H:%M') }}
                                                {% else %}
                                                    Date inconnue
                                                {% endif %}
                                            </small>
                                        </td>
                                        <td>
                                            {% if proposal.status == 'pending' %}
                                                <div class="btn-group" role="group">
                                                    <button class="btn btn-sm btn-success" onclick="acceptProposal({{ proposal.id }})" title="Accepter">
                                                        <i class="fas fa-check"></i>
                                                    </button>
                                                    <button class="btn btn-sm btn-danger" onclick="rejectProposal({{ proposal.id }})" title="Rejeter">
                                                        <i class="fas fa-times"></i>
                                                    </button>
                                                    <a href="{{ url_for('freight.detail', offer_id=proposal.freight_offer.id) }}" class="btn btn-sm btn-outline-primary" title="Voir détails">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                </div>
                                            {% else %}
                                                <a href="{{ url_for('freight.detail', offer_id=proposal.freight_offer.id) }}" class="btn btn-sm btn-outline-primary">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                            {% endif %}
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>

                        {% if my_proposals|length >= 10 %}
                        <div class="text-center mt-3">
                            <a href="{{ url_for('admin.freight_proposals') }}" class="btn btn-outline-warning">
                                <i class="fas fa-eye me-2"></i>Voir toutes les propositions
                            </a>
                        </div>
                        {% endif %}
                    {% else %}
                        <div class="text-center py-4">
                            <i class="fas fa-handshake fa-3x text-muted mb-3"></i>
                            <p class="text-muted">Aucune proposition de transport reçue</p>
                            <p class="text-muted">Créez des demandes de transport pour recevoir des propositions de prix de la part des transporteurs.</p>
                            <a href="{{ url_for('freight.create') }}" class="btn btn-warning">
                                <i class="fas fa-plus me-2"></i>Créer une demande de transport
                            </a>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Missions en cours -->
        <div class="col-lg-8 mb-4">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-tasks me-2"></i>Mes Expéditions en Cours
                    </h5>
                </div>
                <div class="card-body">
                    {% if current_missions %}
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Mission</th>
                                        <th>Transporteur</th>
                                        <th>Itinéraire</th>
                                        <th>Collecte</th>
                                        <th>Statut</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for mission in current_missions %}
                                    <tr>
                                        <td>
                                            <strong>{{ mission.mission_number }}</strong><br>
                                            <small class="text-muted">{{ mission.title[:50] }}...</small>
                                        </td>
                                        <td>{{ mission.transporter.company_name or mission.transporter.get_full_name() }}</td>
                                        <td>
                                            <i class="fas fa-map-marker-alt text-success me-1"></i>
                                            {{ mission.freight_offer.pickup_city }}<br>
                                            <i class="fas fa-flag-checkered text-danger me-1"></i>
                                            {{ mission.freight_offer.delivery_city }}
                                        </td>
                                        <td>
                                            {% if mission.pickup_scheduled_at %}
                                                {{ mission.pickup_scheduled_at.strftime('%d/%m/%Y %H:%M') }}
                                            {% else %}
                                                <span class="text-muted">Non programmée</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            <span class="badge bg-{{ 'success' if mission.status == 'in_transit' else 'warning' }}">
                                                {{ mission.get_status_label() }}
                                            </span>
                                        </td>
                                        <td>
                                            <a href="#" class="btn btn-sm btn-outline-primary">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% else %}
                        <div class="text-center py-4">
                            <i class="fas fa-shipping-fast fa-3x text-muted mb-3"></i>
                            <p class="text-muted">Aucune expédition en cours</p>
                            <a href="{{ url_for('freight.create') }}" class="btn btn-primary">
                                <i class="fas fa-plus me-2"></i>Créer une demande de transport
                            </a>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Mes offres actives -->
        <div class="col-lg-4 mb-4">
            <div class="card">
                <div class="card-header bg-success text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-bullhorn me-2"></i>Mes Offres Actives
                    </h5>
                </div>
                <div class="card-body">
                    {% if my_active_offers %}
                        {% for offer in my_active_offers %}
                        <div class="freight-card border-start border-3 p-3 mb-3 bg-light">
                            <h6 class="mb-1">{{ offer.title }}</h6>
                            <p class="text-muted small mb-2">
                                <i class="fas fa-route me-1"></i>
                                {{ offer.get_route_summary() }}
                            </p>
                            <div class="d-flex justify-content-between align-items-center">
                                <span class="badge bg-primary">{{ offer.goods_type }}</span>
                                {% if offer.price %}
                                    <strong class="text-success">{{ "%.0f"|format(offer.price) }}€</strong>
                                {% endif %}
                            </div>
                            <div class="mt-2">
                                <small class="text-muted">
                                    Créée le
                                    {% if offer.created_at %}
                                        {{ offer.created_at.strftime('%d/%m/%Y') }}
                                    {% else %}
                                        Date inconnue
                                    {% endif %}
                                </small>
                            </div>
                        </div>
                        {% endfor %}
                        
                        <div class="text-center">
                            <a href="{{ url_for('freight.my_offers') }}" class="btn btn-success">
                                <i class="fas fa-eye me-2"></i>Voir toutes mes offres
                            </a>
                        </div>
                    {% else %}
                        <div class="text-center py-3">
                            <i class="fas fa-bullhorn fa-2x text-muted mb-2"></i>
                            <p class="text-muted">Aucune offre active</p>
                            <a href="{{ url_for('freight.create') }}" class="btn btn-success btn-sm">
                                <i class="fas fa-plus me-2"></i>Créer une offre
                            </a>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Missions récentes et évaluations -->
    <div class="row">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header bg-info text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-history me-2"></i>Historique des Expéditions
                    </h5>
                </div>
                <div class="card-body">
                    {% if recent_missions %}
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>Mission</th>
                                        <th>Transporteur</th>
                                        <th>Itinéraire</th>
                                        <th>Date</th>
                                        <th>Statut</th>
                                        <th>Coût</th>
                                        <th>Note</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for mission in recent_missions %}
                                    <tr>
                                        <td>
                                            <strong>{{ mission.mission_number }}</strong><br>
                                            <small class="text-muted">{{ mission.title[:30] }}...</small>
                                        </td>
                                        <td>{{ mission.transporter.company_name or mission.transporter.get_full_name() }}</td>
                                        <td>
                                            <small>
                                                {{ mission.freight_offer.pickup_city }} → 
                                                {{ mission.freight_offer.delivery_city }}
                                            </small>
                                        </td>
                                        <td>
                                            {% if mission.created_at %}
                                                {{ mission.created_at.strftime('%d/%m/%Y') }}
                                            {% else %}
                                                <span class="text-muted">Date inconnue</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            <span class="badge bg-{{ 'success' if mission.status == 'completed' else 'secondary' }}">
                                                {{ mission.get_status_label() }}
                                            </span>
                                        </td>
                                        <td>
                                            {% if mission.agreed_price %}
                                                {{ "%.0f"|format(mission.agreed_price) }}€
                                            {% else %}
                                                <span class="text-muted">-</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            {% if mission.shipper_rating %}
                                                {% for i in range(1, 6) %}
                                                    {% if i <= mission.shipper_rating %}
                                                        <i class="fas fa-star text-warning"></i>
                                                    {% else %}
                                                        <i class="far fa-star text-muted"></i>
                                                    {% endif %}
                                                {% endfor %}
                                            {% else %}
                                                <button class="btn btn-sm btn-outline-warning" onclick="rateTransporter({{ mission.id }})">
                                                    <i class="fas fa-star"></i> Noter
                                                </button>
                                            {% endif %}
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% else %}
                        <div class="text-center py-4">
                            <i class="fas fa-history fa-3x text-muted mb-3"></i>
                            <p class="text-muted">Aucune expédition récente</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <div class="card">
                <div class="card-header bg-warning text-dark">
                    <h5 class="mb-0">
                        <i class="fas fa-chart-pie me-2"></i>Mes Statistiques
                    </h5>
                </div>
                <div class="card-body">
                    <div class="text-center mb-3">
                        <h3 class="text-primary">{{ avg_rating }}/5</h3>
                        <div class="mb-2">
                            {% for i in range(1, 6) %}
                                {% if i <= avg_rating %}
                                    <i class="fas fa-star text-warning"></i>
                                {% else %}
                                    <i class="far fa-star text-muted"></i>
                                {% endif %}
                            {% endfor %}
                        </div>
                        <small class="text-muted">Note moyenne reçue</small>
                    </div>
                    
                    <hr>
                    
                    <div class="row text-center">
                        <div class="col-6 mb-3">
                            <h4 class="text-success">{{ completed_missions }}</h4>
                            <small class="text-muted">Expéditions réussies</small>
                        </div>
                        <div class="col-6 mb-3">
                            <h4 class="text-info">{{ total_cost|round|int }}€</h4>
                            <small class="text-muted">Coût total</small>
                        </div>
                    </div>
                    
                    <div class="d-grid">
                        <a href="{{ url_for('freight.create') }}" class="btn btn-primary">
                            <i class="fas fa-plus me-2"></i>Nouvelle demande
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function rateTransporter(missionId) {
    // Ici on pourrait implémenter un système de notation
    alert('Système de notation en cours de développement');
}

// Gestion des propositions de prix
function acceptProposal(proposalId) {
    if (confirm('Êtes-vous sûr de vouloir accepter cette proposition ? Cela créera automatiquement une mission et rejettera les autres propositions.')) {
        fetch(`/freight/proposal/${proposalId}/accept`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showNotification(data.message, 'success');
                setTimeout(() => {
                    location.reload();
                }, 1500);
            } else {
                showNotification(data.message, 'error');
            }
        })
        .catch(error => {
            showNotification('Erreur lors de l\'acceptation', 'error');
        });
    }
}

function rejectProposal(proposalId) {
    if (confirm('Êtes-vous sûr de vouloir rejeter cette proposition ?')) {
        fetch(`/freight/proposal/${proposalId}/reject`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showNotification(data.message, 'info');
                setTimeout(() => {
                    location.reload();
                }, 1000);
            } else {
                showNotification(data.message, 'error');
            }
        })
        .catch(error => {
            showNotification('Erreur lors du rejet', 'error');
        });
    }
}

// Fonction pour afficher les notifications
function showNotification(message, type) {
    const alertClass = type === 'success' ? 'alert-success' :
                      type === 'error' ? 'alert-danger' :
                      type === 'info' ? 'alert-info' : 'alert-warning';

    const notification = `
        <div class="alert ${alertClass} alert-dismissible fade show position-fixed"
             style="top: 20px; right: 20px; z-index: 9999; min-width: 300px;" role="alert">
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;

    document.body.insertAdjacentHTML('beforeend', notification);

    // Auto-remove after 5 seconds
    setTimeout(() => {
        const alerts = document.querySelectorAll('.alert');
        if (alerts.length > 0) {
            alerts[alerts.length - 1].remove();
        }
    }, 5000);
}

// Actualisation automatique des données
setInterval(function() {
    console.log('Actualisation des données du dashboard expéditeur...');
}, 60000); // Toutes les minutes
</script>
{% endblock %}
