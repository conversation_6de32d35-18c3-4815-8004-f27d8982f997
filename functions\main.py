"""
SABTRANS Firebase Cloud Functions
Point d'entrée principal pour l'application Flask adaptée à Firebase
"""

import os
import firebase_admin
from firebase_admin import credentials, firestore, auth
from flask import Flask, request, jsonify, render_template, session, redirect, url_for, flash
from flask_login import Lo<PERSON><PERSON>ana<PERSON>, login_user, logout_user, login_required, current_user
from werkzeug.security import generate_password_hash, check_password_hash
from functions_framework import create_app
import json
from datetime import datetime

# Initialisation Firebase Admin
if not firebase_admin._apps:
    cred = credentials.ApplicationDefault()
    firebase_admin.initialize_app(cred)

db = firestore.client()

# Configuration Flask
app = Flask(__name__)
app.config['SECRET_KEY'] = os.environ.get('SECRET_KEY', 'dev-secret-key-change-in-production')

# Configuration Flask-Login
login_manager = LoginManager()
login_manager.init_app(app)
login_manager.login_view = 'auth.login'
login_manager.login_message = 'Veuillez vous connecter pour accéder à cette page.'

class User:
    """Classe utilisateur pour Flask-Login"""
    def __init__(self, user_data):
        self.id = user_data.get('id')
        self.email = user_data.get('email')
        self.username = user_data.get('username')
        self.first_name = user_data.get('first_name')
        self.last_name = user_data.get('last_name')
        self.user_type = user_data.get('user_type')
        self.is_active = user_data.get('is_active', True)
        self.country_id = user_data.get('country_id')
        self.department_id = user_data.get('department_id')
        self.preferred_currency_id = user_data.get('preferred_currency_id')

    def is_authenticated(self):
        return True

    def is_anonymous(self):
        return False

    def get_id(self):
        return str(self.id)

    def get_full_name(self):
        return f"{self.first_name} {self.last_name}"

@login_manager.user_loader
def load_user(user_id):
    """Charger un utilisateur depuis Firestore"""
    try:
        user_ref = db.collection('users').document(user_id)
        user_doc = user_ref.get()
        if user_doc.exists:
            user_data = user_doc.to_dict()
            user_data['id'] = user_doc.id
            return User(user_data)
    except Exception as e:
        print(f"Erreur lors du chargement de l'utilisateur: {e}")
    return None

# Routes d'authentification
@app.route('/auth/login', methods=['GET', 'POST'])
def login():
    """Page de connexion"""
    if request.method == 'POST':
        email = request.form.get('email')
        password = request.form.get('password')
        
        try:
            # Rechercher l'utilisateur par email
            users_ref = db.collection('users')
            query = users_ref.where('email', '==', email).limit(1)
            users = query.stream()
            
            user_doc = None
            for user in users:
                user_doc = user
                break
            
            if user_doc and user_doc.exists:
                user_data = user_doc.to_dict()
                
                # Vérifier le mot de passe
                if check_password_hash(user_data.get('password_hash'), password):
                    user_data['id'] = user_doc.id
                    user_obj = User(user_data)
                    login_user(user_obj)
                    
                    next_page = request.args.get('next')
                    if next_page:
                        return redirect(next_page)
                    
                    # Redirection selon le type d'utilisateur
                    if user_data.get('user_type') == 'admin':
                        return redirect('/admin/')
                    else:
                        return redirect('/dashboard/')
                else:
                    flash('Email ou mot de passe incorrect', 'error')
            else:
                flash('Email ou mot de passe incorrect', 'error')
                
        except Exception as e:
            print(f"Erreur lors de la connexion: {e}")
            flash('Erreur lors de la connexion', 'error')
    
    return render_template('auth/login.html')

@app.route('/auth/register', methods=['GET', 'POST'])
def register():
    """Page d'inscription"""
    if request.method == 'POST':
        try:
            # Récupération des données du formulaire
            email = request.form.get('email')
            username = request.form.get('username')
            password = request.form.get('password')
            first_name = request.form.get('first_name')
            last_name = request.form.get('last_name')
            user_type = request.form.get('user_type')
            phone = request.form.get('phone', '')
            company_name = request.form.get('company_name', '')
            country_id = request.form.get('country_id')
            department_id = request.form.get('department_id')
            preferred_currency_id = request.form.get('preferred_currency_id')
            
            # Validation des champs obligatoires
            if not all([email, username, password, first_name, last_name, user_type]):
                flash('Tous les champs obligatoires doivent être remplis', 'error')
                return render_template('auth/register.html')
            
            # Vérifier si l'email existe déjà
            users_ref = db.collection('users')
            existing_email = users_ref.where('email', '==', email).limit(1).stream()
            if any(existing_email):
                flash('Cette adresse email est déjà utilisée', 'error')
                return render_template('auth/register.html')
            
            # Vérifier si le nom d'utilisateur existe déjà
            existing_username = users_ref.where('username', '==', username).limit(1).stream()
            if any(existing_username):
                flash('Ce nom d\'utilisateur est déjà utilisé', 'error')
                return render_template('auth/register.html')
            
            # Créer le nouvel utilisateur
            user_data = {
                'email': email,
                'username': username,
                'password_hash': generate_password_hash(password),
                'first_name': first_name,
                'last_name': last_name,
                'user_type': user_type,
                'phone': phone,
                'company_name': company_name if company_name else None,
                'country_id': int(country_id) if country_id else None,
                'department_id': int(department_id) if department_id else None,
                'preferred_currency_id': int(preferred_currency_id) if preferred_currency_id else None,
                'is_active': True,
                'created_at': datetime.utcnow(),
                'updated_at': datetime.utcnow()
            }
            
            # Ajouter à Firestore
            doc_ref = users_ref.add(user_data)
            
            flash('Inscription réussie ! Vous pouvez maintenant vous connecter.', 'success')
            return redirect('/auth/login')
            
        except Exception as e:
            print(f"Erreur lors de l'inscription: {e}")
            flash('Erreur lors de l\'inscription', 'error')
    
    return render_template('auth/register.html')

@app.route('/auth/logout')
@login_required
def logout():
    """Déconnexion"""
    logout_user()
    flash('Vous avez été déconnecté avec succès', 'info')
    return redirect('/auth/login')

# Routes API pour les données de référence
@app.route('/api/countries')
def api_countries():
    """API pour récupérer la liste des pays"""
    try:
        countries = []
        countries_ref = db.collection('countries')
        docs = countries_ref.stream()
        
        for doc in docs:
            country_data = doc.to_dict()
            country_data['id'] = doc.id
            countries.append(country_data)
        
        return jsonify({
            'success': True,
            'countries': countries
        })
    except Exception as e:
        print(f"Erreur API countries: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/countries/<country_code>/departments')
def api_departments(country_code):
    """API pour récupérer les départements d'un pays"""
    try:
        # Récupérer le pays par son code
        countries_ref = db.collection('countries')
        country_query = countries_ref.where('code', '==', country_code).limit(1)
        country_docs = list(country_query.stream())
        
        if not country_docs:
            return jsonify({
                'success': False,
                'error': 'Pays non trouvé'
            }), 404
        
        country_id = country_docs[0].id
        
        # Récupérer les départements
        departments = []
        departments_ref = db.collection('departments')
        dept_query = departments_ref.where('country_id', '==', int(country_id))
        docs = dept_query.stream()
        
        for doc in docs:
            dept_data = doc.to_dict()
            dept_data['id'] = doc.id
            departments.append(dept_data)
        
        return jsonify({
            'success': True,
            'departments': departments
        })
    except Exception as e:
        print(f"Erreur API departments: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/currencies')
def api_currencies():
    """API pour récupérer la liste des devises"""
    try:
        currencies = []
        currencies_ref = db.collection('currencies')
        docs = currencies_ref.stream()
        
        for doc in docs:
            currency_data = doc.to_dict()
            currency_data['id'] = doc.id
            currencies.append(currency_data)
        
        return jsonify({
            'success': True,
            'currencies': currencies
        })
    except Exception as e:
        print(f"Erreur API currencies: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

# Route principale
@app.route('/')
def index():
    """Page d'accueil"""
    if current_user.is_authenticated:
        if current_user.user_type == 'admin':
            return redirect('/admin/')
        else:
            return redirect('/dashboard/')
    return redirect('/auth/login')

# Point d'entrée pour Firebase Functions
def api(request):
    """Point d'entrée pour Firebase Cloud Functions"""
    with app.request_context(request.environ):
        return app.full_dispatch_request()
