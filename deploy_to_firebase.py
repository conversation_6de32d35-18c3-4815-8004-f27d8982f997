#!/usr/bin/env python3
"""
Script d'installation automatique pour déployer SABTRANS sur Firebase
Automatise le processus de déploiement complet
"""

import os
import sys
import subprocess
import json
import shutil
from pathlib import Path

def print_step(step, description):
    """Afficher une étape du processus"""
    print(f"\n🔄 ÉTAPE {step}: {description}")
    print("=" * 60)

def run_command(command, description, check=True):
    """Exécuter une commande avec gestion d'erreur et encodage"""
    print(f"📋 {description}")
    print(f"💻 Commande: {command}")

    try:
        # Utiliser l'encodage UTF-8 pour éviter les problèmes d'encodage
        result = subprocess.run(command, shell=True, check=check,
                              capture_output=True, text=True,
                              encoding='utf-8', errors='replace')
        if result.stdout:
            print(f"✅ Sortie: {result.stdout.strip()}")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Erreur: {e}")
        if e.stderr:
            print(f"❌ Détails: {e.stderr.strip()}")
        return False
    except UnicodeDecodeError as e:
        print(f"❌ Erreur d'encodage: {e}")
        print("💡 Essayez de changer l'encodage de votre terminal")
        return False

def check_prerequisites():
    """Vérifier les prérequis"""
    print_step(1, "VÉRIFICATION DES PRÉREQUIS")
    
    prerequisites = [
        ("node --version", "Node.js"),
        ("npm --version", "NPM"),
        ("python --version", "Python"),
        ("firebase --version", "Firebase CLI")
    ]
    
    all_good = True
    for command, name in prerequisites:
        if not run_command(command, f"Vérification de {name}", check=False):
            print(f"❌ {name} n'est pas installé ou accessible")
            all_good = False
        else:
            print(f"✅ {name} disponible")
    
    if not all_good:
        print("\n❌ Prérequis manquants. Veuillez installer:")
        print("- Node.js: https://nodejs.org/")
        print("- Firebase CLI: npm install -g firebase-tools")
        print("- Python 3.9+: https://python.org/")
        return False
    
    return True

def setup_firebase_project():
    """Configurer le projet Firebase"""
    print_step(2, "CONFIGURATION DU PROJET FIREBASE")

    # Vérifier la connexion Firebase
    if not run_command("firebase login --no-localhost", "Connexion à Firebase", check=False):
        print("❌ Échec de la connexion Firebase")
        print("💡 Exécutez manuellement: firebase login")
        return False

    # Lister les projets avec gestion d'erreur
    print("📋 Projets Firebase disponibles:")
    projects_success = run_command("firebase projects:list", "Liste des projets", check=False)

    if not projects_success:
        print("⚠️  Impossible de lister les projets automatiquement")
        print("💡 Vérifiez vos projets sur: https://console.firebase.google.com")

    print("\n" + "="*50)
    print("🔧 CONFIGURATION DU PROJET")
    print("="*50)
    print("Si vous n'avez pas encore de projet Firebase:")
    print("1. Allez sur https://console.firebase.google.com")
    print("2. Cliquez sur 'Ajouter un projet'")
    print("3. Nommez votre projet (ex: SABTRANS)")
    print("4. Activez la facturation (plan Blaze)")
    print("5. Activez Authentication, Firestore, Functions, Hosting")
    print("="*50)

    # Demander le projet à utiliser
    project_id = input("\n🔧 Entrez l'ID de votre projet Firebase: ").strip()
    if not project_id:
        print("❌ ID de projet requis")
        return False

    # Configurer le projet
    print(f"🔄 Configuration du projet: {project_id}")
    if not run_command(f"firebase use {project_id}", f"Configuration du projet {project_id}", check=False):
        print("❌ Échec de la configuration du projet")
        print("💡 Vérifiez que:")
        print("   - Le projet existe sur Firebase Console")
        print("   - Vous avez les permissions sur ce projet")
        print("   - L'ID du projet est correct")
        return False

    # Mettre à jour .firebaserc
    firebaserc_content = {
        "projects": {
            "default": project_id
        }
    }

    try:
        with open('.firebaserc', 'w') as f:
            json.dump(firebaserc_content, f, indent=2)
        print(f"✅ Fichier .firebaserc créé")
    except Exception as e:
        print(f"⚠️  Erreur lors de la création de .firebaserc: {e}")

    print(f"✅ Projet Firebase configuré: {project_id}")
    return True

def prepare_functions():
    """Préparer les Cloud Functions"""
    print_step(3, "PRÉPARATION DES CLOUD FUNCTIONS")

    # Créer le dossier functions s'il n'existe pas
    functions_dir = Path("functions")
    if not functions_dir.exists():
        print("❌ Dossier functions manquant")
        return False

    # Vérifier si on est dans un environnement virtuel
    in_venv = hasattr(sys, 'real_prefix') or (hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix)

    if not in_venv:
        print("⚠️  Pas d'environnement virtuel détecté")
        print("💡 Recommandation: Créer un environnement virtuel")

        create_venv = input("🔧 Créer un environnement virtuel? (o/N): ").strip().lower()
        if create_venv in ['o', 'oui', 'y', 'yes']:
            print("📦 Création de l'environnement virtuel...")
            if not run_command("python -m venv venv_firebase", "Création environnement virtuel"):
                print("❌ Échec de la création de l'environnement virtuel")
                print("💡 Continuons avec l'installation globale...")
            else:
                print("✅ Environnement virtuel créé")
                print("💡 Activez-le manuellement:")
                print("   Windows: venv_firebase\\Scripts\\activate")
                print("   Puis relancez ce script")
                return False

    # Installer les dépendances Python
    original_dir = os.getcwd()
    os.chdir("functions")

    # Essayer plusieurs méthodes d'installation
    install_methods = [
        ("pip install -r requirements.txt --no-cache-dir", "Installation sans cache"),
        ("pip install -r requirements.txt --user", "Installation utilisateur"),
        ("python -m pip install -r requirements.txt", "Installation avec python -m pip"),
        ("pip install -r requirements.txt --force-reinstall", "Installation forcée")
    ]

    success = False
    for command, description in install_methods:
        print(f"🔄 Tentative: {description}")
        if run_command(command, description, check=False):
            success = True
            break
        print("⚠️  Méthode échouée, tentative suivante...")

    os.chdir(original_dir)

    if not success:
        print("❌ Échec de l'installation des dépendances")
        print("💡 Solutions possibles:")
        print("   1. Créer un environnement virtuel propre")
        print("   2. Réparer l'installation Python")
        print("   3. Exécuter en tant qu'administrateur")
        print("   4. Installer manuellement: cd functions && pip install -r requirements.txt")
        return False

    print("✅ Cloud Functions préparées")
    return True

def prepare_hosting():
    """Préparer Firebase Hosting"""
    print_step(4, "PRÉPARATION DU HOSTING")
    
    # Créer le dossier public s'il n'existe pas
    public_dir = Path("public")
    if not public_dir.exists():
        public_dir.mkdir()
    
    # Copier les fichiers statiques
    static_dir = Path("static")
    if static_dir.exists():
        print("📁 Copie des fichiers statiques...")
        shutil.copytree(static_dir, public_dir / "static", dirs_exist_ok=True)
    
    # Copier les templates (pour référence)
    templates_dir = Path("templates")
    if templates_dir.exists():
        print("📁 Copie des templates...")
        shutil.copytree(templates_dir, public_dir / "templates", dirs_exist_ok=True)
    
    print("✅ Hosting préparé")
    return True

def migrate_data():
    """Migrer les données vers Firestore"""
    print_step(5, "MIGRATION DES DONNÉES")
    
    # Vérifier si le script de migration existe
    if not Path("migrate_to_firestore.py").exists():
        print("❌ Script de migration manquant")
        return False
    
    # Demander les credentials Firebase
    print("🔑 Configuration des credentials Firebase...")
    creds_path = input("Chemin vers le fichier de credentials JSON (ou Entrée pour utiliser les credentials par défaut): ").strip()
    
    if creds_path and Path(creds_path).exists():
        os.environ["GOOGLE_APPLICATION_CREDENTIALS"] = creds_path
        print(f"✅ Credentials configurés: {creds_path}")
    else:
        print("⚠️  Utilisation des credentials par défaut")
    
    # Exécuter la migration
    if not run_command("python migrate_to_firestore.py", "Migration des données vers Firestore"):
        print("❌ Échec de la migration")
        print("💡 Vérifiez vos credentials Firebase et la connexion à la base MySQL")
        return False
    
    print("✅ Données migrées vers Firestore")
    return True

def deploy_application():
    """Déployer l'application sur Firebase"""
    print_step(6, "DÉPLOIEMENT SUR FIREBASE")
    
    # Déployer les règles Firestore
    if not run_command("firebase deploy --only firestore", "Déploiement des règles Firestore"):
        return False
    
    # Déployer les Cloud Functions
    if not run_command("firebase deploy --only functions", "Déploiement des Cloud Functions"):
        return False
    
    # Déployer le Hosting
    if not run_command("firebase deploy --only hosting", "Déploiement du Hosting"):
        return False
    
    print("✅ Application déployée sur Firebase")
    return True

def test_deployment():
    """Tester le déploiement"""
    print_step(7, "TESTS DE DÉPLOIEMENT")
    
    # Obtenir l'URL de l'application
    try:
        result = subprocess.run("firebase hosting:channel:list", shell=True, 
                              capture_output=True, text=True, check=True)
        print("📋 Informations de hosting:")
        print(result.stdout)
    except:
        print("⚠️  Impossible de récupérer les informations de hosting")
    
    # Tests basiques
    project_id = None
    try:
        with open('.firebaserc', 'r') as f:
            config = json.load(f)
            project_id = config['projects']['default']
    except:
        print("⚠️  Impossible de lire la configuration du projet")
    
    if project_id:
        app_url = f"https://{project_id}.web.app"
        print(f"🌐 URL de l'application: {app_url}")
        
        # Test simple de l'API
        test_commands = [
            (f"curl -s -o /dev/null -w '%{{http_code}}' {app_url}", "Test de la page d'accueil"),
            (f"curl -s -o /dev/null -w '%{{http_code}}' {app_url}/api/countries", "Test de l'API countries"),
            (f"curl -s -o /dev/null -w '%{{http_code}}' {app_url}/api/currencies", "Test de l'API currencies")
        ]
        
        for command, description in test_commands:
            run_command(command, description, check=False)
    
    print("✅ Tests de base terminés")
    return True

def main():
    """Fonction principale"""
    print("🚀 DÉPLOIEMENT AUTOMATIQUE DE SABTRANS SUR FIREBASE")
    print("=" * 60)
    print("Ce script va déployer votre application SABTRANS sur Firebase.")
    print("Assurez-vous d'avoir:")
    print("- Un projet Firebase créé")
    print("- La facturation activée sur Firebase")
    print("- Les credentials Firebase configurés")
    print("=" * 60)
    
    confirm = input("\n▶️  Continuer avec le déploiement? (o/N): ").strip().lower()
    if confirm not in ['o', 'oui', 'y', 'yes']:
        print("❌ Déploiement annulé")
        return False
    
    # Étapes du déploiement
    steps = [
        check_prerequisites,
        setup_firebase_project,
        prepare_functions,
        prepare_hosting,
        migrate_data,
        deploy_application,
        test_deployment
    ]
    
    for i, step in enumerate(steps, 1):
        try:
            if not step():
                print(f"\n❌ ÉCHEC À L'ÉTAPE {i}")
                print("Consultez le guide de déploiement pour résoudre les problèmes")
                return False
        except KeyboardInterrupt:
            print("\n⏹️  Déploiement interrompu par l'utilisateur")
            return False
        except Exception as e:
            print(f"\n❌ ERREUR INATTENDUE À L'ÉTAPE {i}: {e}")
            return False
    
    # Succès
    print("\n" + "=" * 60)
    print("🎉 DÉPLOIEMENT RÉUSSI!")
    print("=" * 60)
    print("✅ Votre application SABTRANS est maintenant déployée sur Firebase")
    print("✅ Toutes les fonctionnalités sont opérationnelles")
    print("✅ Les données ont été migrées vers Firestore")
    print("\n📋 Prochaines étapes:")
    print("1. Testez l'application dans votre navigateur")
    print("2. Configurez un domaine personnalisé (optionnel)")
    print("3. Configurez les alertes de monitoring")
    print("4. Programmez des sauvegardes régulières")
    print("\n🌐 Accédez à votre application via Firebase Console > Hosting")
    print("=" * 60)
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
