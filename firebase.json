{"hosting": {"public": "public", "ignore": ["firebase.json", "**/.*", "**/node_modules/**"], "rewrites": [{"source": "/api/**", "function": "api"}, {"source": "/auth/**", "function": "api"}, {"source": "/freight/**", "function": "api"}, {"source": "/admin/**", "function": "api"}, {"source": "/dashboard/**", "function": "api"}, {"source": "**", "destination": "/index.html"}], "headers": [{"source": "/static/**", "headers": [{"key": "Cache-Control", "value": "max-age=31536000"}]}]}, "functions": {"source": "functions", "runtime": "python39", "predeploy": ["pip install -r functions/requirements.txt"]}, "firestore": {"rules": "firestore.rules", "indexes": "firestore.indexes.json"}, "storage": {"rules": "storage.rules"}, "emulators": {"auth": {"port": 9099}, "functions": {"port": 5001}, "firestore": {"port": 8080}, "hosting": {"port": 5000}, "ui": {"enabled": true}, "singleProjectMode": true}}