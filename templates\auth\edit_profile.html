{% extends "base.html" %}

{% block title %}Modifier mon Profil - SABTRANS{% endblock %}

{% block content %}
<div class="container">
    <div class="row">
        <div class="col-md-8 mx-auto">
            <!-- Header -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 text-primary">
                    <i class="fas fa-user-edit me-2"></i>Modifier mon Profil
                </h1>
                <a href="{{ url_for('auth.profile') }}" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left me-2"></i>Retour au profil
                </a>
            </div>

            <form method="POST" class="needs-validation" novalidate>
                <!-- Informations personnelles -->
                <div class="card mb-4">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-user me-2"></i>Informations Personnelles
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="first_name" class="form-label">Prénom *</label>
                                <input type="text" class="form-control" id="first_name" name="first_name" 
                                       value="{{ current_user.first_name }}" required>
                                <div class="invalid-feedback">Veuillez saisir votre prénom</div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="last_name" class="form-label">Nom *</label>
                                <input type="text" class="form-control" id="last_name" name="last_name" 
                                       value="{{ current_user.last_name }}" required>
                                <div class="invalid-feedback">Veuillez saisir votre nom</div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="email" class="form-label">Email *</label>
                                <input type="email" class="form-control" id="email" name="email" 
                                       value="{{ current_user.email }}" required>
                                <div class="invalid-feedback">Veuillez saisir un email valide</div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="phone" class="form-label">Téléphone</label>
                                <input type="tel" class="form-control" id="phone" name="phone" 
                                       value="{{ current_user.phone or '' }}" placeholder="+33 1 23 45 67 89">
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="username" class="form-label">Nom d'utilisateur *</label>
                            <input type="text" class="form-control" id="username" name="username" 
                                   value="{{ current_user.username }}" required>
                            <div class="form-text">Au moins 3 caractères, uniquement lettres, chiffres et tirets</div>
                            <div class="invalid-feedback">Veuillez saisir un nom d'utilisateur valide</div>
                        </div>
                    </div>
                </div>

                <!-- Informations entreprise -->
                <div class="card mb-4">
                    <div class="card-header bg-success text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-building me-2"></i>Informations Entreprise
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-8 mb-3">
                                <label for="company_name" class="form-label">Nom de l'entreprise</label>
                                <input type="text" class="form-control" id="company_name" name="company_name" 
                                       value="{{ current_user.company_name or '' }}" placeholder="SARL Transport Express">
                            </div>
                            <div class="col-md-4 mb-3">
                                <label for="siret" class="form-label">SIRET</label>
                                <input type="text" class="form-control" id="siret" name="siret" 
                                       value="{{ current_user.siret or '' }}" placeholder="12345678901234">
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="address" class="form-label">Adresse</label>
                            <textarea class="form-control" id="address" name="address" rows="2" 
                                      placeholder="123 Rue de la Logistique">{{ current_user.address or '' }}</textarea>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-4 mb-3">
                                <label for="postal_code" class="form-label">Code postal</label>
                                <input type="text" class="form-control" id="postal_code" name="postal_code"
                                       value="{{ current_user.postal_code or '' }}" placeholder="75001">
                            </div>
                            <div class="col-md-4 mb-3">
                                <label for="city" class="form-label">Ville</label>
                                <input type="text" class="form-control" id="city" name="city"
                                       value="{{ current_user.city or '' }}" placeholder="Paris">
                            </div>
                            <div class="col-md-4 mb-3">
                                <label for="country_id" class="form-label">Pays</label>
                                <select class="form-select" id="country_id" name="country_id"
                                        {% if current_user.country_id %}data-preselected="{{ current_user.country_id }}"{% endif %}>
                                    <option value="">Sélectionnez votre pays</option>
                                </select>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="department_id" class="form-label">
                                    <span id="department_label">Département</span>
                                </label>
                                <select class="form-select" id="department_id" name="department_id" disabled
                                        {% if current_user.department_id %}data-preselected="{{ current_user.department_id }}"{% endif %}>
                                    <option value="">Sélectionnez d'abord un pays</option>
                                </select>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="country_legacy" class="form-label">Pays (ancien format)</label>
                                <select class="form-select" id="country_legacy" name="country">
                                    <option value="France" {% if current_user.country == 'France' %}selected{% endif %}>France</option>
                                    <option value="Belgique" {% if current_user.country == 'Belgique' %}selected{% endif %}>Belgique</option>
                                    <option value="Suisse" {% if current_user.country == 'Suisse' %}selected{% endif %}>Suisse</option>
                                    <option value="Luxembourg" {% if current_user.country == 'Luxembourg' %}selected{% endif %}>Luxembourg</option>
                                    <option value="Espagne" {% if current_user.country == 'Espagne' %}selected{% endif %}>Espagne</option>
                                    <option value="Italie" {% if current_user.country == 'Italie' %}selected{% endif %}>Italie</option>
                                    <option value="Allemagne" {% if current_user.country == 'Allemagne' %}selected{% endif %}>Allemagne</option>
                                    <option value="Autre" {% if current_user.country not in ['France', 'Belgique', 'Suisse', 'Luxembourg', 'Espagne', 'Italie', 'Allemagne'] %}selected{% endif %}>Autre</option>
                                </select>
                                <div class="form-text">Maintenu pour compatibilité - utilisez le nouveau système ci-dessus</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Informations de compte -->
                <div class="card mb-4">
                    <div class="card-header bg-info text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-cog me-2"></i>Informations de Compte
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">Type de compte</label>
                                <div class="form-control-plaintext">
                                    <span class="badge bg-{{ 'primary' if current_user.user_type == 'admin' else 'secondary' }} fs-6">
                                        {{ current_user.user_type.title() }}
                                    </span>
                                    <div class="form-text">Le type de compte ne peut pas être modifié</div>
                                </div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">Statut du compte</label>
                                <div class="form-control-plaintext">
                                    {% if current_user.is_active %}
                                        <span class="badge bg-success fs-6">Actif</span>
                                    {% else %}
                                        <span class="badge bg-danger fs-6">Inactif</span>
                                    {% endif %}
                                    {% if current_user.is_verified %}
                                        <span class="badge bg-info fs-6 ms-1">Vérifié</span>
                                    {% endif %}
                                    {% if current_user.email_confirmed %}
                                        <span class="badge bg-success fs-6 ms-1">Email confirmé</span>
                                    {% else %}
                                        <span class="badge bg-warning fs-6 ms-1">Email non confirmé</span>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">Membre depuis</label>
                                <div class="form-control-plaintext">
                                    {{ current_user.created_at.strftime('%d/%m/%Y à %H:%M') }}
                                </div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">Dernière connexion</label>
                                <div class="form-control-plaintext">
                                    {% if current_user.last_login %}
                                        {{ current_user.last_login.strftime('%d/%m/%Y à %H:%M') }}
                                    {% else %}
                                        <span class="text-muted">Jamais</span>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Préférences -->
                <div class="card mb-4">
                    <div class="card-header bg-warning text-dark">
                        <h5 class="mb-0">
                            <i class="fas fa-sliders-h me-2"></i>Préférences
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6 mb-4">
                                <h6><i class="fas fa-coins me-2"></i>Devise Préférée</h6>
                                <label for="preferred_currency_id" class="form-label">Devise pour vos transactions</label>
                                <select class="form-select" id="preferred_currency_id" name="preferred_currency_id"
                                        {% if current_user.preferred_currency_id %}data-preselected="{{ current_user.preferred_currency_id }}"{% endif %}>
                                    <option value="">Sélectionnez votre devise</option>
                                </select>
                                <div class="form-text">
                                    <i class="fas fa-info-circle me-1"></i>
                                    Cette devise sera utilisée par défaut pour vos offres de fret
                                </div>
                            </div>
                            <div class="col-md-6">
                                <h6>Notifications Email</h6>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="email_notifications" name="email_notifications" checked>
                                    <label class="form-check-label" for="email_notifications">
                                        Recevoir les notifications par email
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="marketing_emails" name="marketing_emails">
                                    <label class="form-check-label" for="marketing_emails">
                                        Recevoir les emails marketing
                                    </label>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <h6>Confidentialité</h6>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="public_profile" name="public_profile">
                                    <label class="form-check-label" for="public_profile">
                                        Profil public visible
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="show_contact" name="show_contact" checked>
                                    <label class="form-check-label" for="show_contact">
                                        Afficher mes informations de contact
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Boutons d'action -->
                <div class="d-grid gap-2 d-md-flex justify-content-md-between mb-4">
                    <div>
                        <a href="{{ url_for('auth.change_password') }}" class="btn btn-outline-warning">
                            <i class="fas fa-key me-2"></i>Changer le mot de passe
                        </a>
                    </div>
                    <div>
                        <a href="{{ url_for('auth.profile') }}" class="btn btn-outline-secondary me-md-2">
                            <i class="fas fa-times me-2"></i>Annuler
                        </a>
                        <button type="submit" class="btn btn-success">
                            <i class="fas fa-save me-2"></i>Sauvegarder les modifications
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Validation du formulaire
(function() {
    'use strict';
    window.addEventListener('load', function() {
        var forms = document.getElementsByClassName('needs-validation');
        var validation = Array.prototype.filter.call(forms, function(form) {
            form.addEventListener('submit', function(event) {
                if (form.checkValidity() === false) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                form.classList.add('was-validated');
            }, false);
        });
    }, false);
})();

// Validation du nom d'utilisateur
document.getElementById('username').addEventListener('input', function() {
    const username = this.value;
    const pattern = /^[a-zA-Z0-9_-]{3,}$/;
    
    if (username.length >= 3 && pattern.test(username)) {
        this.classList.remove('is-invalid');
        this.classList.add('is-valid');
    } else {
        this.classList.remove('is-valid');
        this.classList.add('is-invalid');
    }
});

// Validation de l'email
document.getElementById('email').addEventListener('input', function() {
    const email = this.value;
    const pattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    
    if (pattern.test(email)) {
        this.classList.remove('is-invalid');
        this.classList.add('is-valid');
    } else {
        this.classList.remove('is-valid');
        this.classList.add('is-invalid');
    }
});

// Formatage automatique du SIRET
document.getElementById('siret').addEventListener('input', function() {
    let value = this.value.replace(/\D/g, ''); // Enlever tout ce qui n'est pas un chiffre
    if (value.length > 14) {
        value = value.substring(0, 14);
    }
    this.value = value;
});

// Formatage automatique du code postal
document.getElementById('postal_code').addEventListener('input', function() {
    let value = this.value.replace(/\D/g, ''); // Enlever tout ce qui n'est pas un chiffre
    if (value.length > 5) {
        value = value.substring(0, 5);
    }
    this.value = value;
});

// Confirmation avant sauvegarde
document.querySelector('form').addEventListener('submit', function(e) {
    if (!confirm('Êtes-vous sûr de vouloir sauvegarder ces modifications ?')) {
        e.preventDefault();
    }
});
</script>
<script src="{{ url_for('static', filename='js/location.js') }}"></script>
<script src="{{ url_for('static', filename='js/currency.js') }}"></script>
{% endblock %}
