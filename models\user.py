from flask_login import UserMixin
from werkzeug.security import generate_password_hash, check_password_hash
from datetime import datetime
from database import db

class User(UserMixin, db.Model):
    __tablename__ = 'users'
    
    id = db.Column(db.Integer, primary_key=True)
    email = db.Column(db.String(120), unique=True, nullable=False, index=True)
    username = db.Column(db.String(80), unique=True, nullable=False, index=True)
    password_hash = db.Column(db.String(255), nullable=False)
    
    # Informations personnelles
    first_name = db.Column(db.String(50), nullable=False)
    last_name = db.Column(db.String(50), nullable=False)
    phone = db.Column(db.String(20))
    
    # Type d'utilisateur
    user_type = db.Column(db.Enum('transporteur', 'expediteur', 'chauffeur', 'admin'), nullable=False)
    
    # Informations entreprise
    company_name = db.Column(db.String(100))
    siret = db.Column(db.String(20))
    address = db.Column(db.Text)
    city = db.Column(db.String(50))
    postal_code = db.Column(db.String(10))

    # Localisation avec nouvelles relations
    country_id = db.Column(db.Integer, db.ForeignKey('countries.id'))
    department_id = db.Column(db.Integer, db.ForeignKey('departments.id'))

    # Ancien champ country maintenu pour compatibilité
    country = db.Column(db.String(50), default='France')
    
    # Statut et validation
    is_active = db.Column(db.Boolean, default=True)
    is_verified = db.Column(db.Boolean, default=False)
    email_confirmed = db.Column(db.Boolean, default=False)
    
    # Notes administrateur
    admin_notes = db.Column(db.Text)

    # Dates
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    last_login = db.Column(db.DateTime)
    
    # Relations
    freight_offers = db.relationship('FreightOffer', backref='user', lazy='dynamic')
    documents = db.relationship('Document', backref='user', lazy='dynamic')
    missions_as_transporter = db.relationship('Mission', foreign_keys='Mission.transporter_id', backref='transporter', lazy='dynamic')
    missions_as_shipper = db.relationship('Mission', foreign_keys='Mission.shipper_id', backref='shipper', lazy='dynamic')
    
    def set_password(self, password):
        """Définit le mot de passe hashé"""
        self.password_hash = generate_password_hash(password)
    
    def check_password(self, password):
        """Vérifie le mot de passe"""
        return check_password_hash(self.password_hash, password)
    
    def get_full_name(self):
        """Retourne le nom complet"""
        return f"{self.first_name} {self.last_name}"
    
    def is_transporter(self):
        """Vérifie si l'utilisateur est un transporteur"""
        return self.user_type == 'transporteur'
    
    def is_shipper(self):
        """Vérifie si l'utilisateur est un expéditeur"""
        return self.user_type == 'expediteur'
    
    def is_driver(self):
        """Vérifie si l'utilisateur est un chauffeur"""
        return self.user_type == 'chauffeur'
    
    def is_admin(self):
        """Vérifie si l'utilisateur est un administrateur"""
        return self.user_type == 'admin'

    def get_country_name(self):
        """Retourne le nom du pays"""
        if self.user_country:
            return self.user_country.name_fr
        return self.country or 'Non défini'

    def get_department_name(self):
        """Retourne le nom du département/wilaya"""
        if self.user_department:
            return self.user_department.name_fr
        return 'Non défini'

    def get_full_location(self):
        """Retourne la localisation complète"""
        parts = []
        if self.city:
            parts.append(self.city)
        if self.user_department:
            parts.append(self.user_department.get_full_name())
        if self.user_country:
            parts.append(self.user_country.name_fr)
        return ', '.join(parts) if parts else 'Non défini'

    def get_currency(self):
        """Retourne la devise du pays"""
        if self.user_country and self.user_country.currency:
            return self.user_country.currency
        return None
    
    def to_dict(self):
        """Convertit l'objet en dictionnaire pour l'API"""
        return {
            'id': self.id,
            'email': self.email,
            'username': self.username,
            'first_name': self.first_name,
            'last_name': self.last_name,
            'phone': self.phone,
            'user_type': self.user_type,
            'company_name': self.company_name,
            'is_verified': self.is_verified,
            'created_at': self.created_at.isoformat() if self.created_at else None
        }
    
    def __repr__(self):
        return f'<User {self.username}>'
